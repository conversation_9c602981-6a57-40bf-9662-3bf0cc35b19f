-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: crt; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA crt;


ALTER SCHEMA crt OWNER TO smsuser;

--
-- Name: SCHEMA crt; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA crt IS 'Certification (certification)';

--
-- Name: certificates; Type: TABLE; Schema: crt; Owner: smsuser
--

CREATE TABLE crt.certificates (
    certificateid uuid NOT NULL,
    certificatetypeid uuid NOT NULL,
    code character varying,
    name character varying,
    orderno integer,
    description character varying,
    "interval" integer,
    isactive boolean DEFAULT false,
    creatoruserid uuid,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE crt.certificates OWNER TO smsuser;

--
-- Name: certificatetypes; Type: TABLE; Schema: crt; Owner: smsuser
--

CREATE TABLE crt.certificatetypes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    name character varying(256) NOT NULL,
    description character varying(512),
    validityperiod integer,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE crt.certificatetypes OWNER TO smsuser;

--
-- Name: trainingcategories; Type: TABLE; Schema: crt; Owner: smsuser
--

CREATE TABLE crt.trainingcategories (
    trainingcategoryid uuid NOT NULL,
    name character varying,
    description character varying,
    parentcategoryid uuid NOT NULL,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime uuid,
    isdeleted boolean DEFAULT false
);


ALTER TABLE crt.trainingcategories OWNER TO smsuser;