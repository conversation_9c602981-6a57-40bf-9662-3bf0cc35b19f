-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: acc; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA acc;


ALTER SCHEMA acc OWNER TO smsuser;

--
-- Name: SCHEMA acc; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA acc IS 'Accounting module tables';

--
-- Name: fin; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA fin;


ALTER SCHEMA fin OWNER TO smsuser;

--
-- Name: <PERSON><PERSON>EMA fin; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA fin IS 'Finance (finance)';

--
-- Name: accounts; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.accounts (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    accountcode character varying(50) NOT NULL,
    accountname character varying(256) NOT NULL,
    accounttype character varying(100) NOT NULL,
    parentaccountid uuid,
    level integer DEFAULT 1 NOT NULL,
    isdebitable boolean DEFAULT true NOT NULL,
    iscreditable boolean DEFAULT true NOT NULL,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE acc.accounts OWNER TO smsuser;

--
-- Name: budgetestimations; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.budgetestimations (
    budgetestimationid uuid NOT NULL,
    budgetperiodid uuid NOT NULL,
    costcenterid uuid NOT NULL,
    value numeric,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.budgetestimations OWNER TO smsuser;

--
-- Name: budgetperiods; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.budgetperiods (
    budgetperiodid uuid NOT NULL,
    budgetplanid uuid NOT NULL,
    fromdate timestamp without time zone,
    todate timestamp without time zone,
    name character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.budgetperiods OWNER TO smsuser;

--
-- Name: budgetplancostcenters; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.budgetplancostcenters (
    budgetplancostcenterid uuid NOT NULL,
    budgetplanid uuid NOT NULL,
    costcenterid uuid NOT NULL,
    parentcostcenterid uuid NOT NULL,
    code character varying,
    name character varying,
    hierarchyname character varying,
    typeenum integer,
    orderno integer,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    description character varying
);


ALTER TABLE acc.budgetplancostcenters OWNER TO smsuser;

--
-- Name: budgetplans; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.budgetplans (
    budgetplanid uuid NOT NULL,
    vesselid uuid NOT NULL,
    currencyid uuid NOT NULL,
    name character varying,
    fiscalyear integer,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    isincludetax boolean DEFAULT false
);


ALTER TABLE acc.budgetplans OWNER TO smsuser;

--
-- Name: costcategories; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.costcategories (
    costcategoryid uuid NOT NULL,
    costcategorydefinitionid uuid NOT NULL,
    name character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.costcategories OWNER TO smsuser;

--
-- Name: costcategoriesvscostcenters; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.costcategoriesvscostcenters (
    costcategoriesvscostcenterid uuid NOT NULL,
    creatoruserid uuid NOT NULL,
    costcenterid uuid NOT NULL,
    costcategoryid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.costcategoriesvscostcenters OWNER TO smsuser;

--
-- Name: costcategorydefinitions; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.costcategorydefinitions (
    costcategorydefinitionid uuid NOT NULL,
    name character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.costcategorydefinitions OWNER TO smsuser;

--
-- Name: costcenters; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.costcenters (
    costcenterid uuid NOT NULL,
    parentcostcenterid uuid NOT NULL,
    code character varying,
    name character varying,
    typeenum integer,
    orderno integer,
    isactive boolean DEFAULT false,
    hierarchy character varying,
    hierarchyname character varying,
    description character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    isexcludebudgetcalculation boolean DEFAULT false,
    level integer
);


ALTER TABLE acc.costcenters OWNER TO smsuser;

--
-- Name: documentageing; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.documentageing (
    documentageingid uuid NOT NULL,
    documentid uuid NOT NULL,
    ageingperiod character varying,
    ageingperioddate timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.documentageing OWNER TO smsuser;

--
-- Name: documentcategories; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.documentcategories (
    documentcategoryid uuid NOT NULL,
    code character varying,
    name character varying,
    orderno integer,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    isactive boolean DEFAULT false
);


ALTER TABLE acc.documentcategories OWNER TO smsuser;

--
-- Name: documentitems; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.documentitems (
    documentitemid uuid NOT NULL,
    documentid uuid NOT NULL,
    itemdescription character varying,
    purchaseorderitemid uuid NOT NULL,
    unitid uuid NOT NULL,
    itemtypeenum integer,
    materialid uuid NOT NULL,
    itemcode character varying,
    quantity numeric,
    discount numeric,
    unitpricedocument numeric,
    netpricedocument numeric,
    linetotaldocument numeric,
    isdeleted boolean DEFAULT false,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    taxid uuid NOT NULL,
    budgetperiodid uuid NOT NULL,
    budgetplancostcenterid uuid NOT NULL,
    vatnetprice numeric,
    vatratio numeric
);


ALTER TABLE acc.documentitems OWNER TO smsuser;

--
-- Name: documentitemvspurchaseorderitems; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.documentitemvspurchaseorderitems (
    documentitemvspurchaseorderitemid uuid NOT NULL,
    documentitemid uuid NOT NULL,
    purchaseorderitemid uuid NOT NULL,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.documentitemvspurchaseorderitems OWNER TO smsuser;

--
-- Name: documentperiods; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.documentperiods (
    documentperiodid uuid NOT NULL,
    documentid uuid NOT NULL,
    amount numeric,
    perioddate timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.documentperiods OWNER TO smsuser;

--
-- Name: documents; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.documents (
    documentid uuid NOT NULL,
    currencyid uuid NOT NULL,
    vesselid uuid NOT NULL,
    documentcategoryid uuid NOT NULL,
    documenttypeid uuid NOT NULL,
    companyid uuid NOT NULL,
    statusid uuid NOT NULL,
    paymenttermid uuid NOT NULL,
    documenttypeenum integer,
    paymentinone numeric,
    paymentintwo numeric,
    paymentinthree numeric,
    paymentinonedays numeric,
    paymentintwodays numeric,
    paymentinthreedays numeric,
    documentno character varying,
    documentdate timestamp without time zone,
    isfixedexchangerate boolean DEFAULT false,
    exchangerate numeric,
    grandtotaldocument numeric,
    grandtotaldefault numeric,
    description character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    documentduedate timestamp without time zone,
    paymentcleareddate timestamp without time zone,
    paymentclearedstatusenum integer,
    paidamount numeric,
    paymenttypeenum integer,
    amountinpaymentcurrency numeric,
    exchangeratetodocumentcurrency numeric,
    amountindocumentcurrency numeric,
    exchangeratetodefaultcurrency numeric,
    amountindefaultcurrency numeric,
    relatedforpaymentdocumentid uuid NOT NULL,
    isperiod boolean DEFAULT false,
    totalperiods integer,
    paidbycompanyid uuid NOT NULL,
    paidbyuserid uuid NOT NULL,
    reference character varying,
    grandtotalvatdefault numeric,
    grandtotalvat numeric,
    postingdate timestamp without time zone,
    additionaldate timestamp without time zone
);


ALTER TABLE acc.documents OWNER TO smsuser;

--
-- Name: documenttypes; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.documenttypes (
    documenttypeid uuid NOT NULL,
    code character varying,
    name character varying,
    isactive boolean DEFAULT false,
    orderno integer,
    creatoruserid uuid,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE acc.documenttypes OWNER TO smsuser;

--
-- Name: journalentries; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.journalentries (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    entrynumber character varying(100) NOT NULL,
    entrydate date NOT NULL,
    description text NOT NULL,
    referencenumber character varying(100),
    vesselid uuid,
    totaldebit numeric(18,2) DEFAULT 0 NOT NULL,
    totalcredit numeric(18,2) DEFAULT 0 NOT NULL,
    status character varying(50),
    approvedby uuid,
    approvaldate timestamp without time zone,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE acc.journalentries OWNER TO smsuser;

--
-- Name: taxes; Type: TABLE; Schema: acc; Owner: smsuser
--

CREATE TABLE acc.taxes (
    taxid uuid NOT NULL,
    code character varying,
    name character varying,
    amountpercentage numeric,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    isactive boolean DEFAULT false
);


ALTER TABLE acc.taxes OWNER TO smsuser;