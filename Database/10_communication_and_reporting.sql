-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: com; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA com;


ALTER SCHEMA com OWNER TO smsuser;

--
-- Name: SCHEMA com; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA com IS 'Communication (communication)';

--
-- Name: hangfire; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA hangfire;


ALTER SCHEMA hangfire OWNER TO smsuser;

--
-- Name: SCHEMA hangfire; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA hangfire IS 'Background job processing tables (HangFire)';


--
-- Name: hangfirecore; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA hangfirecore;


ALTER SCHEMA hangfirecore OWNER TO smsuser;

--
-- Name: integration; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA integration;


ALTER SCHEMA integration OWNER TO smsuser;

--
-- Name: SCHEMA integration; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA integration IS 'Integration lookup tables';

--
-- Name: report; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA report;


ALTER SCHEMA report OWNER TO smsuser;

--
-- Name: SCHEMA report; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA report IS 'Reporting framework tables';

--
-- Name: trm; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA trm;


ALTER SCHEMA trm OWNER TO smsuser;

--
-- Name: SCHEMA trm; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA trm IS 'Terminal and Maritime operations tables';

--
-- Name: emailfollowups; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.emailfollowups (
    emailfollowupid uuid NOT NULL,
    tablename character varying,
    rowid uuid NOT NULL,
    emailsubject character varying,
    referenceid character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    status character varying
);


ALTER TABLE com.emailfollowups OWNER TO smsuser;

--
-- Name: emailgroups; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.emailgroups (
    emailgroupid uuid NOT NULL,
    name character varying,
    description character varying,
    emails character varying,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.emailgroups OWNER TO smsuser;

--
-- Name: emailgroupsvsusers; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.emailgroupsvsusers (
    emailgroupvsuserid uuid NOT NULL,
    emailgroupid uuid NOT NULL,
    userid uuid NOT NULL,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.emailgroupsvsusers OWNER TO smsuser;

--
-- Name: emailrules; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.emailrules (
    emailruleid uuid NOT NULL,
    emailgroupid uuid NOT NULL,
    typeenum integer,
    name character varying,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.emailrules OWNER TO smsuser;

--
-- Name: emailrulesvsvessels; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.emailrulesvsvessels (
    emailrulesvsvesselid uuid NOT NULL,
    emailruleid uuid NOT NULL,
    vesselid uuid NOT NULL,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.emailrulesvsvessels OWNER TO smsuser;

--
-- Name: emailtemplates; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.emailtemplates (
    emailtemplateid uuid NOT NULL,
    name character varying,
    subject character varying,
    description character varying,
    fromemail character varying,
    fromname character varying,
    bcc character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    isactive boolean DEFAULT false,
    cc character varying
);


ALTER TABLE com.emailtemplates OWNER TO smsuser;

--
-- Name: notificationattachments; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationattachments (
    notificationattachmentid uuid NOT NULL,
    filename character varying,
    filepath character varying,
    filetype character varying,
    filesize integer,
    rowid uuid,
    creationtime timestamp without time zone,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    deletiontime timestamp without time zone,
    deleteruserid uuid,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationattachments OWNER TO smsuser;

--
-- Name: notificationchannels; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationchannels (
    notificationchannelid uuid NOT NULL,
    name character varying,
    channeltypeenum integer,
    isactive boolean DEFAULT false,
    issystem boolean DEFAULT false,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationchannels OWNER TO smsuser;

--
-- Name: notificationobjectactions; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationobjectactions (
    notificationobjectactionid uuid NOT NULL,
    actiontypeenum integer,
    notificationobjectid uuid NOT NULL,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationobjectactions OWNER TO smsuser;

--
-- Name: notificationobjects; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationobjects (
    notificationobjectid uuid NOT NULL,
    name character varying,
    description character varying,
    tablename character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationobjects OWNER TO smsuser;

--
-- Name: notificationobjectvariables; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationobjectvariables (
    notificationobjectvariableid uuid NOT NULL,
    notificationobjectid uuid NOT NULL,
    variable character varying,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationobjectvariables OWNER TO smsuser;

--
-- Name: notificationruleresponsibles; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationruleresponsibles (
    notficationruleresponsibleid uuid NOT NULL,
    notificationruleid uuid NOT NULL,
    userid uuid NOT NULL,
    toassigneduser boolean DEFAULT false,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime uuid,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationruleresponsibles OWNER TO smsuser;

--
-- Name: notificationrules; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationrules (
    notificationruleid uuid NOT NULL,
    notificationobjectid uuid NOT NULL,
    notificationobjectactionid uuid NOT NULL,
    notificationchannelid uuid NOT NULL,
    notificationtemplateid uuid NOT NULL,
    intervaldays integer,
    threshold integer,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    isdeleted boolean DEFAULT false,
    usergrouptype integer
);


ALTER TABLE com.notificationrules OWNER TO smsuser;

--
-- Name: notifications; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notifications (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    userid uuid NOT NULL,
    title character varying(256) NOT NULL,
    message text NOT NULL,
    notificationtype character varying(100),
    entitytype character varying(100),
    entityid uuid,
    isread boolean DEFAULT false NOT NULL,
    readdate timestamp without time zone,
    priority character varying(50),
    expirydate timestamp without time zone,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE com.notifications OWNER TO smsuser;

--
-- Name: notificationsubscriptions; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationsubscriptions (
    id uuid,
    entityid character varying,
    entitytypeassemblyqualifiedname character varying,
    entitytypename character varying,
    notificationname character varying,
    userguid uuid,
    creationtime timestamp without time zone,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    deletiontime timestamp without time zone,
    deleteruserid uuid,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationsubscriptions OWNER TO smsuser;

--
-- Name: notificationtemplates; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationtemplates (
    notificationtemplateid uuid NOT NULL,
    name character varying,
    subject character varying,
    body character varying,
    notificationobjectid uuid NOT NULL,
    notificationchannelid uuid NOT NULL,
    notificationobjectactionid uuid NOT NULL,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationtemplates OWNER TO smsuser;

--
-- Name: notificationunsubscribers; Type: TABLE; Schema: com; Owner: smsuser
--

CREATE TABLE com.notificationunsubscribers (
    notificationunsubscriberid uuid NOT NULL,
    userid uuid NOT NULL,
    notificationobjectid uuid NOT NULL,
    notificationchannelid uuid NOT NULL,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid NOT NULL,
    isdeleted boolean DEFAULT false
);


ALTER TABLE com.notificationunsubscribers OWNER TO smsuser;