-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: cmp; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA cmp;


ALTER SCHEMA cmp OWNER TO smsuser;

--
-- Name: SCHEMA cmp; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA cmp IS 'Company Management (company_management)';

--
-- Name: ids; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA ids;


ALTER SCHEMA ids OWNER TO smsuser;

--
-- Name: SCHEMA ids; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA ids IS 'Identity Security (identity_security)';

--
-- Name: sys; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA sys;


ALTER SCHEMA sys OWNER TO smsuser;

--
-- Name: SCHEMA sys; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA sys IS 'System Config (system_config)';

--
-- Name: companycategories; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companycategories (
    companycategoryid uuid NOT NULL,
    name character varying,
    isdeleted boolean DEFAULT false
);


ALTER TABLE cmp.companycategories OWNER TO smsuser;

--
-- Name: companycontacts; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companycontacts (
    companycontactid uuid NOT NULL,
    companyid uuid NOT NULL,
    name character varying,
    surname character varying,
    phone character varying,
    email character varying,
    title character varying,
    jobtitle character varying,
    isdeleted boolean DEFAULT false
);


ALTER TABLE cmp.companycontacts OWNER TO smsuser;

--
-- Name: companyevaluations; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companyevaluations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    companyid uuid NOT NULL,
    companyevaluationtypeid uuid,
    statusid uuid,
    vesselid uuid,
    evaluatedbyuserid uuid,
    evaluationtime timestamp without time zone,
    code character varying(50),
    remark text,
    duedate timestamp without time zone,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE cmp.companyevaluations OWNER TO smsuser;

--
-- Name: companyevaluationtypes; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companyevaluationtypes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    companycategoryid uuid,
    name character varying(200) NOT NULL,
    code character varying(50),
    description text,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE cmp.companyevaluationtypes OWNER TO smsuser;

--
-- Name: companyevaluationvspurchaseorders; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companyevaluationvspurchaseorders (
    companyevaluationvspurchaseorderid uuid NOT NULL,
    purchaseorderid uuid NOT NULL,
    companyevaluationid uuid NOT NULL,
    creatoruserid uuid,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE cmp.companyevaluationvspurchaseorders OWNER TO smsuser;

--
-- Name: companyvscertificates; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companyvscertificates (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    companyid uuid NOT NULL,
    certificatetypeid uuid,
    certificateid uuid,
    issuedate date,
    expiredate date,
    description text,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE cmp.companyvscertificates OWNER TO smsuser;

--
-- Name: companyvscompanycategories; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companyvscompanycategories (
    companyvscompanycategoryid uuid NOT NULL,
    companyid uuid NOT NULL,
    companycategoryid uuid NOT NULL,
    creatoruserid uuid,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE cmp.companyvscompanycategories OWNER TO smsuser;

--
-- Name: companyvssegments; Type: TABLE; Schema: cmp; Owner: smsuser
--

CREATE TABLE cmp.companyvssegments (
    companyvssegmentid uuid NOT NULL,
    companyid uuid NOT NULL,
    segmentid uuid NOT NULL,
    startdate timestamp without time zone,
    expiredate timestamp without time zone,
    creatoruserid uuid,
    creationtime timestamp without time zone,
    lastmodifieruserid uuid,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid,
    deletiontime timestamp without time zone,
    isdeleted boolean DEFAULT false
);


ALTER TABLE cmp.companyvssegments OWNER TO smsuser;