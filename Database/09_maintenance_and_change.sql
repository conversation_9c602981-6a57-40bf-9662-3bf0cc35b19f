-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: moc; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA moc;


ALTER SCHEMA moc OWNER TO smsuser;

--
-- Name: SCHEMA moc; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA moc IS 'Management of Change tables';


--
-- Name: pms; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA pms;


ALTER SCHEMA pms OWNER TO smsuser;

--
-- Name: SCHEMA pms; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA pms IS 'Planned Maintenance System tables';