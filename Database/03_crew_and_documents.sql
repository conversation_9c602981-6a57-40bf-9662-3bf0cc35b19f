-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: crw; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA crw;


ALTER SCHEMA crw OWNER TO smsuser;

--
-- Name: SCHEMA crw; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA crw IS 'Crew Management (crew_management)';


--
-- Name: doc; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA doc;


ALTER SCHEMA doc OWNER TO smsuser;

--
-- Name: SCHEMA doc; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA doc IS 'Document Management (document_management)';