-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: chr; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA chr;


ALTER SCHEMA chr OWNER TO smsuser;

--
-- Name: SCHEMA chr; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA chr IS 'Chartering operations tables';

--
-- Name: additionalexpenses; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.additionalexpenses (
    additionalexpenseid uuid NOT NULL,
    additionalexpensetypeid uuid NOT NULL,
    statusid uuid NOT NULL,
    vesselid uuid NOT NULL,
    voyagecalculationid uuid NOT NULL,
    expense numeric,
    currencyid uuid NOT NULL,
    exchangerate numeric,
    remark character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    totalexpense numeric,
    expensedate timestamp without time zone,
    portid uuid NOT NULL,
    charteringfeetypeenum integer
);


ALTER TABLE chr.additionalexpenses OWNER TO smsuser;

--
-- Name: additionalexpensetypes; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.additionalexpensetypes (
    additionalexpensetypeid uuid NOT NULL,
    name character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone
);


ALTER TABLE chr.additionalexpensetypes OWNER TO smsuser;

--
-- Name: additionalincomes; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.additionalincomes (
    additionalincomeid uuid NOT NULL,
    freightincomeid uuid NOT NULL,
    statusid uuid NOT NULL,
    additionalincometypeid uuid NOT NULL,
    income numeric,
    currencyid uuid NOT NULL,
    exchangerate numeric,
    totalincome numeric,
    remark character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    date timestamp without time zone
);


ALTER TABLE chr.additionalincomes OWNER TO smsuser;

--
-- Name: additionalincometypes; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.additionalincometypes (
    additionalincometypeid uuid NOT NULL,
    name character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone
);


ALTER TABLE chr.additionalincometypes OWNER TO smsuser;

--
-- Name: bunkerexpensedetails; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.bunkerexpensedetails (
    bunkerexpensesdetailid uuid NOT NULL,
    portid uuid NOT NULL,
    bunkerexpenseid uuid NOT NULL,
    receivedquantity numeric,
    receiveddate timestamp without time zone,
    unitprice numeric,
    barging numeric,
    callingcoast numeric,
    finalunitprice numeric,
    totalprice numeric,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone
);


ALTER TABLE chr.bunkerexpensedetails OWNER TO smsuser;

--
-- Name: bunkerexpenses; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.bunkerexpenses (
    bunkerexpenseid uuid NOT NULL,
    voyagecalculationid uuid NOT NULL,
    statusid uuid NOT NULL,
    vesselbunkertypeid uuid NOT NULL,
    vesselid uuid NOT NULL,
    beginrob numeric,
    totalreceivedquantity numeric,
    endrob numeric,
    consumption numeric,
    beginprice numeric,
    receivedprice numeric,
    endprice numeric,
    totalbunkerexpense numeric,
    remark character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone
);


ALTER TABLE chr.bunkerexpenses OWNER TO smsuser;

--
-- Name: freightincomes; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.freightincomes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    vesselid uuid NOT NULL,
    voyageid uuid,
    incometype character varying(100) NOT NULL,
    description character varying(512),
    amount numeric(18,2) NOT NULL,
    currency character varying(10),
    invoicedate date,
    duedate date,
    paiddate date,
    status character varying(50),
    client character varying(256),
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE chr.freightincomes OWNER TO smsuser;

--
-- Name: initialfreightpayments; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.initialfreightpayments (
    initialfreightpaymentid uuid NOT NULL,
    freightincomeid uuid NOT NULL,
    initialfreighttypeid uuid NOT NULL,
    income numeric,
    incomedate timestamp without time zone,
    currencyid uuid NOT NULL,
    exchangerate numeric,
    totalfreightpayment numeric,
    remark character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone
);


ALTER TABLE chr.initialfreightpayments OWNER TO smsuser;

--
-- Name: initialfreighttypes; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.initialfreighttypes (
    initialfreighttypeid uuid NOT NULL,
    name character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone
);


ALTER TABLE chr.initialfreighttypes OWNER TO smsuser;

--
-- Name: portexpenses; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.portexpenses (
    portexpenseid uuid NOT NULL,
    voyagecalculationid uuid NOT NULL,
    vesselid uuid NOT NULL,
    companyid uuid NOT NULL,
    statusid uuid NOT NULL,
    portid uuid NOT NULL,
    arrivaldate timestamp without time zone,
    departuredate timestamp without time zone,
    fda numeric,
    pda numeric,
    balance numeric,
    currencyid uuid NOT NULL,
    exchangerate numeric,
    totalportexpense numeric,
    remark character varying,
    isdeleted boolean DEFAULT false,
    lastmodifieruserid uuid NOT NULL,
    lastmodificationtime timestamp without time zone,
    deleteruserid uuid NOT NULL,
    deletiontime timestamp without time zone,
    creatoruserid uuid NOT NULL,
    creationtime timestamp without time zone,
    operationtypeenum integer
);


ALTER TABLE chr.portexpenses OWNER TO smsuser;

--
-- Name: voyagecalculations; Type: TABLE; Schema: chr; Owner: smsuser
--

CREATE TABLE chr.voyagecalculations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    tenantid uuid,
    vesselid uuid NOT NULL,
    calculationname character varying(256) NOT NULL,
    voyagetype character varying(100),
    fromport character varying(256),
    toport character varying(256),
    distance numeric(18,2),
    speed numeric(10,2),
    fuelconsumption numeric(18,3),
    fuelprice numeric(18,2),
    portcharges numeric(18,2),
    freightrate numeric(18,2),
    totalrevenue numeric(18,2),
    totalcost numeric(18,2),
    netprofit numeric(18,2),
    profitmargin numeric(10,4),
    currency character varying(10),
    calculationdate timestamp without time zone NOT NULL,
    isactive boolean DEFAULT true NOT NULL,
    creationtime timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    creatoruserid uuid,
    lastmodificationtime timestamp without time zone,
    lastmodifieruserid uuid,
    isdeleted boolean DEFAULT false NOT NULL,
    deleteruserid uuid,
    deletiontime timestamp without time zone
);


ALTER TABLE chr.voyagecalculations OWNER TO smsuser;