-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: ras; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA ras;


ALTER SCHEMA ras OWNER TO smsuser;

--
-- Name: SCHEMA ras; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA ras IS 'Risk Assessment System tables';

--
-- Name: saf; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA saf;


ALTER SCHEMA saf OWNER TO smsuser;

--
-- Name: SCHEMA saf; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA saf IS 'Safety Compliance (safety_compliance)';


--
-- Name: sire; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA sire;


ALTER SCHEMA sire OWNER TO smsuser;

--
-- Name: SCHEMA sire; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA sire IS 'SIRE inspection related tables';