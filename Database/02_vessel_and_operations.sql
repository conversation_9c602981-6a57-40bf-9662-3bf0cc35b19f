-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: loc; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA loc;


ALTER SCHEMA loc OWNER TO smsuser;

--
-- Name: SCHEMA loc; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA loc IS 'Location (location)';


--
-- Name: opr; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA opr;


ALTER SCHEMA opr OWNER TO smsuser;

--
-- Name: SCHEMA opr; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA opr IS 'Operations and Voyage estimation tables';


--
-- Name: ops; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA ops;


ALTER SCHEMA ops OWNER TO smsuser;

--
-- Name: SCHEMA ops; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA ops IS 'Operations (operations)';

--
-- Name: vsl; Type: SCHEMA; Schema: -; Owner: smsuser
--

CREATE SCHEMA vsl;


ALTER SCHEMA vsl OWNER TO smsuser;

--
-- Name: SCHEMA vsl; Type: COMMENT; Schema: -; Owner: smsuser
--

COMMENT ON SCHEMA vsl IS 'Vessel Management (vessel_management)';