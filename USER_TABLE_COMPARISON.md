# Users Table Comparison Report

This document compares the Users table structure from three sources:
1. **users.txt** - SQL Server table definition
2. **Current Model** - User.cs model in the application
3. **PostgreSQL DB** - Actual database implementation

## Field-by-Field Comparison

| Field Name | users.txt (SQL Server) | Current Model (User.cs) | PostgreSQL DB | Status |
|------------|------------------------|------------------------|---------------|---------|
| **Primary Key** |
| UserID/id | uniqueidentifier NOT NULL | Guid Id | id (uuid) | ✅ Matched (different naming) |
| **User Information** |
| UserName | nvarchar(256) NOT NULL | string Username | username | ✅ Matched |
| Name | nvarchar(150) NOT NULL | string FirstName | first_name | ⚠️ Split field |
| Surname | nvarchar(150) NOT NULL | string LastName | last_name | ⚠️ Split field |
| Email | nvarchar(150) NOT NULL | string Email | email | ✅ Matched |
| NormalizedUserName | nvarchar(256) NOT NULL | - | - | ❌ Missing |
| NormalizedEmail | nvarchar(256) NOT NULL | - | - | ❌ Missing |
| DisplayName | - | string DisplayName | display_name | ✅ In Model Only |
| **Authentication** |
| Password | nvarchar(150) NOT NULL | - | - | ❌ Deprecated |
| PasswordHash | nvarchar(256) NULL | string PasswordHash | password_hash | ✅ Matched |
| SecurityStamp | nvarchar(256) NOT NULL | - | - | ❌ Missing |
| PasswordResetCode | nvarchar(328) NULL | - | - | ❌ Missing |
| ShouldChangePasswordOnNextLogin | bit NOT NULL | - | - | ❌ Missing |
| LastPasswordChangeTime | datetimeoffset NULL | - | - | ❌ Missing |
| **Contact Information** |
| PhoneNumber | nvarchar(16) NULL | string PhoneNumber | phone_number | ✅ Matched |
| PhoneNumberConfirmed | bit NOT NULL | - | - | ❌ Missing |
| EmailConfirmed | bit NOT NULL | bool EmailConfirmed | email_confirmed | ✅ Matched |
| **Status & Permissions** |
| IsActive | bit NOT NULL | bool IsActive | is_active | ✅ Matched |
| IsDeleted | bit NOT NULL | - | - | ❌ Missing |
| IsSystemUser | bit NULL | - | - | ❌ Missing |
| IsExternal | bit NOT NULL | - | - | ❌ Missing |
| UserGroupID | uniqueidentifier NULL | - | - | ❌ Missing |
| TenantId | uniqueidentifier NULL | - | - | ❌ Missing |
| **Security Features** |
| AccessFailedCount | int NULL | - | - | ❌ Missing |
| LastLoginTime | datetime NULL | - | - | ❌ Missing |
| TwoFactorEnabled | bit NOT NULL | - | - | ❌ Missing |
| LockoutEnd | datetimeoffset NULL | - | - | ❌ Missing |
| LockoutEnabled | bit NOT NULL | - | - | ❌ Missing |
| CurrentSessionID | uniqueidentifier NULL | - | - | ❌ Missing |
| **Audit Fields** |
| CreationTime | datetime2 NOT NULL | DateTime CreatedDate | created_date | ✅ Matched |
| CreatorId | uniqueidentifier NULL | - | - | ❌ Missing |
| LastModificationTime | datetime2 NULL | DateTime? ModifiedDate | modified_date | ✅ Matched |
| LastModifierId | uniqueidentifier NULL | - | - | ❌ Missing |
| DeleterId | uniqueidentifier NULL | - | - | ❌ Missing |
| DeletionTime | datetime2 NULL | - | - | ❌ Missing |
| **Additional Fields** |
| LanguageID | uniqueidentifier NULL | string PreferredLanguage | preferred_language | ⚠️ Different type |
| ExtraProperties | nvarchar(-0.5) NOT NULL | - | - | ❌ Missing |
| ConcurrencyStamp | nvarchar(40) NULL | - | - | ❌ Missing |
| EntityVersion | int NOT NULL | - | - | ❌ Missing |
| UserRowPointer | uniqueidentifier NOT NULL | - | - | ❌ Missing |
| OldUserID | int NULL | - | - | ❌ Missing |
| SelectorID | int NULL | - | - | ❌ Missing |
| DirectoryEnum | int NULL | - | - | ❌ Missing |
| **Crew/Maritime Fields (Model Only)** |
| - | - | DateTime? DateOfBirth | date_of_birth | ➕ Extra |
| - | - | string Nationality | nationality | ➕ Extra |
| - | - | string PlaceOfBirth | place_of_birth | ➕ Extra |
| - | - | string Department | department | ➕ Extra |
| - | - | Guid? DepartmentId | department_id | ➕ Extra |
| - | - | string EmployeeCode | employee_code | ➕ Extra |
| - | - | DateTime? HireDate | hire_date | ➕ Extra |
| - | - | string PassportNumber | passport_number | ➕ Extra |
| - | - | DateTime? PassportExpiryDate | passport_expiry_date | ➕ Extra |
| - | - | string SeamanBookNumber | seaman_book_number | ➕ Extra |
| - | - | DateTime? SeamanBookExpiryDate | seaman_book_expiry_date | ➕ Extra |
| - | - | string ProfilePicture | profile_picture | ➕ Extra |

## Summary Statistics

### Fields in users.txt (SQL Server): 42 fields
- **Implemented in Model**: 10 fields (24%)
- **Missing in Model**: 32 fields (76%)

### Fields in Current Model: 22 fields
- **Matching users.txt**: 10 fields (45%)
- **Extra/Maritime-specific**: 12 fields (55%)

## Key Differences

### 1. **Security Features Missing**
The current implementation lacks important security features present in users.txt:
- Password reset functionality (PasswordResetCode, ShouldChangePasswordOnNextLogin)
- Account lockout (LockoutEnd, LockoutEnabled, AccessFailedCount)
- Two-factor authentication (TwoFactorEnabled)
- Security stamp for invalidating tokens
- Session management (CurrentSessionID)

### 2. **Audit Trail Incomplete**
Missing audit fields:
- CreatorId, LastModifierId (who created/modified)
- DeleterId, DeletionTime (soft delete tracking)
- EntityVersion (optimistic concurrency)
- ConcurrencyStamp

### 3. **Multi-Tenancy Not Implemented**
- TenantId field is missing
- UserGroupID for organizational structure

### 4. **Data Normalization Missing**
- NormalizedUserName, NormalizedEmail for faster searches

### 5. **Additional Maritime Features**
The current model includes maritime-specific fields not in users.txt:
- Crew personal information (DateOfBirth, Nationality, PlaceOfBirth)
- Employment details (Department, EmployeeCode, HireDate)
- Maritime documents (Passport, SeamanBook)

## Recommendations

### High Priority (Security & Core Features)
1. **Add Security Fields**:
   ```csharp
   [Column("security_stamp")]
   public string? SecurityStamp { get; set; }
   
   [Column("access_failed_count")]
   public int AccessFailedCount { get; set; } = 0;
   
   [Column("lockout_end")]
   public DateTimeOffset? LockoutEnd { get; set; }
   
   [Column("lockout_enabled")]
   public bool LockoutEnabled { get; set; } = true;
   
   [Column("two_factor_enabled")]
   public bool TwoFactorEnabled { get; set; } = false;
   ```

2. **Implement Soft Delete**:
   ```csharp
   [Column("is_deleted")]
   public bool IsDeleted { get; set; } = false;
   
   [Column("deleted_date")]
   public DateTime? DeletedDate { get; set; }
   
   [Column("deleted_by")]
   public Guid? DeletedBy { get; set; }
   ```

3. **Add Audit Fields**:
   ```csharp
   [Column("created_by")]
   public Guid? CreatedBy { get; set; }
   
   [Column("modified_by")]
   public Guid? ModifiedBy { get; set; }
   ```

### Medium Priority (Performance & Multi-tenancy)
1. **Add Normalized Fields**:
   ```csharp
   [Column("normalized_username")]
   public string NormalizedUsername { get; set; } = string.Empty;
   
   [Column("normalized_email")]
   public string NormalizedEmail { get; set; } = string.Empty;
   ```

2. **Add Multi-tenancy Support**:
   ```csharp
   [Column("tenant_id")]
   public Guid? TenantId { get; set; }
   ```

3. **Add Concurrency Control**:
   ```csharp
   [Column("concurrency_stamp")]
   public string? ConcurrencyStamp { get; set; }
   ```

### Low Priority (Additional Features)
1. **Password Management**:
   ```csharp
   [Column("last_password_change_time")]
   public DateTimeOffset? LastPasswordChangeTime { get; set; }
   
   [Column("should_change_password_on_next_login")]
   public bool ShouldChangePasswordOnNextLogin { get; set; } = false;
   ```

2. **Session Tracking**:
   ```csharp
   [Column("last_login_time")]
   public DateTime? LastLoginTime { get; set; }
   ```

## Migration Script Needed

To align with users.txt structure, the following columns need to be added to the PostgreSQL database:

```sql
ALTER TABLE users
ADD COLUMN normalized_username VARCHAR(256),
ADD COLUMN normalized_email VARCHAR(256),
ADD COLUMN security_stamp VARCHAR(256),
ADD COLUMN access_failed_count INTEGER DEFAULT 0,
ADD COLUMN lockout_end TIMESTAMP WITH TIME ZONE,
ADD COLUMN lockout_enabled BOOLEAN DEFAULT true,
ADD COLUMN two_factor_enabled BOOLEAN DEFAULT false,
ADD COLUMN is_deleted BOOLEAN DEFAULT false,
ADD COLUMN deleted_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN deleted_by UUID,
ADD COLUMN created_by UUID,
ADD COLUMN modified_by UUID,
ADD COLUMN tenant_id UUID,
ADD COLUMN last_login_time TIMESTAMP WITH TIME ZONE,
ADD COLUMN last_password_change_time TIMESTAMP WITH TIME ZONE,
ADD COLUMN should_change_password_on_next_login BOOLEAN DEFAULT false,
ADD COLUMN concurrency_stamp VARCHAR(40);

-- Create indexes for normalized fields
CREATE INDEX idx_users_normalized_username ON users(normalized_username);
CREATE INDEX idx_users_normalized_email ON users(normalized_email);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
```