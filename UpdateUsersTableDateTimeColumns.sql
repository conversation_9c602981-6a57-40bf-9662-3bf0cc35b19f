-- Update users table timestamp columns to use timestamp with time zone
ALTER TABLE users 
    ALTER COLUMN created_date TYPE timestamp with time zone USING created_date AT TIME ZONE 'UTC',
    ALTER COLUMN modified_date TYPE timestamp with time zone USING modified_date AT TIME ZONE 'UTC',
    ALTER COLUMN deleted_date TYPE timestamp with time zone USING deleted_date AT TIME ZONE 'UTC',
    ALTER COLUMN last_password_change TYPE timestamp with time zone USING last_password_change AT TIME ZONE 'UTC';

-- The date columns (date_of_birth, hire_date, passport_expiry_date, seaman_book_expiry_date) 
-- should remain as date type since they don't need time information