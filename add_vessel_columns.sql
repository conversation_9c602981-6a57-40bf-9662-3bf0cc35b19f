-- Add missing columns to vessels table
DO $$ 
BEGIN
    -- Add beam column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'beam') THEN
        ALTER TABLE vessels ADD COLUMN beam DECIMAL(10,2);
    END IF;

    -- Add breadth_moulded column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'breadth_moulded') THEN
        ALTER TABLE vessels ADD COLUMN breadth_moulded DECIMAL(10,2);
    END IF;

    -- Add ballast_water_treatment column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'ballast_water_treatment') THEN
        ALTER TABLE vessels ADD COLUMN ballast_water_treatment BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add cargo_capacity_bale column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'cargo_capacity_bale') THEN
        ALTER TABLE vessels ADD COLUMN cargo_capacity_bale DECIMAL(10,2);
    END IF;

    -- Add cargo_capacity_grain column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'cargo_capacity_grain') THEN
        ALTER TABLE vessels ADD COLUMN cargo_capacity_grain DECIMAL(10,2);
    END IF;

    -- Add cargo_hatches column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'cargo_hatches') THEN
        ALTER TABLE vessels ADD COLUMN cargo_hatches INTEGER;
    END IF;

    -- Add cargo_holds column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'cargo_holds') THEN
        ALTER TABLE vessels ADD COLUMN cargo_holds INTEGER;
    END IF;

    -- Add commercial_manager_company column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'commercial_manager_company') THEN
        ALTER TABLE vessels ADD COLUMN commercial_manager_company VARCHAR(200);
    END IF;

    -- Add crew_manager_company column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'crew_manager_company') THEN
        ALTER TABLE vessels ADD COLUMN crew_manager_company VARCHAR(200);
    END IF;

    -- Add current_location column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'current_location') THEN
        ALTER TABLE vessels ADD COLUMN current_location VARCHAR(200);
    END IF;

    -- Add deadweight_summer column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'deadweight_summer') THEN
        ALTER TABLE vessels ADD COLUMN deadweight_summer DECIMAL(10,2);
    END IF;

    -- Add delivery_date column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'delivery_date') THEN
        ALTER TABLE vessels ADD COLUMN delivery_date DATE;
    END IF;

    -- Add depth_moulded column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'depth_moulded') THEN
        ALTER TABLE vessels ADD COLUMN depth_moulded DECIMAL(10,2);
    END IF;

    -- Add draft_summer column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'draft_summer') THEN
        ALTER TABLE vessels ADD COLUMN draft_summer DECIMAL(10,2);
    END IF;

    -- Add eco_design column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'eco_design') THEN
        ALTER TABLE vessels ADD COLUMN eco_design BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add eta column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'eta') THEN
        ALTER TABLE vessels ADD COLUMN eta TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add former_names column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'former_names') THEN
        ALTER TABLE vessels ADD COLUMN former_names TEXT;
    END IF;

    -- Add hull_number column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'hull_number') THEN
        ALTER TABLE vessels ADD COLUMN hull_number VARCHAR(50);
    END IF;

    -- Add last_drydock_date column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'last_drydock_date') THEN
        ALTER TABLE vessels ADD COLUMN last_drydock_date DATE;
    END IF;

    -- Add next_drydock_date column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'next_drydock_date') THEN
        ALTER TABLE vessels ADD COLUMN next_drydock_date DATE;
    END IF;

    -- Add next_port column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'next_port') THEN
        ALTER TABLE vessels ADD COLUMN next_port VARCHAR(200);
    END IF;

    -- Add passengers_capacity column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'passengers_capacity') THEN
        ALTER TABLE vessels ADD COLUMN passengers_capacity INTEGER;
    END IF;

    -- Add satellite_email column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'satellite_email') THEN
        ALTER TABLE vessels ADD COLUMN satellite_email VARCHAR(200);
    END IF;

    -- Add satellite_phone column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'satellite_phone') THEN
        ALTER TABLE vessels ADD COLUMN satellite_phone VARCHAR(50);
    END IF;

    -- Add scrubber_fitted column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'scrubber_fitted') THEN
        ALTER TABLE vessels ADD COLUMN scrubber_fitted BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add speed_maximum column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'speed_maximum') THEN
        ALTER TABLE vessels ADD COLUMN speed_maximum DECIMAL(10,2);
    END IF;

    -- Add speed_service column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'speed_service') THEN
        ALTER TABLE vessels ADD COLUMN speed_service DECIMAL(10,2);
    END IF;

    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'status') THEN
        ALTER TABLE vessels ADD COLUMN status VARCHAR(50);
    END IF;

    -- Add technical_manager_company column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'technical_manager_company') THEN
        ALTER TABLE vessels ADD COLUMN technical_manager_company VARCHAR(200);
    END IF;

    -- Add teu_capacity column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'teu_capacity') THEN
        ALTER TABLE vessels ADD COLUMN teu_capacity INTEGER;
    END IF;

    -- Add trade_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'trade_type') THEN
        ALTER TABLE vessels ADD COLUMN trade_type VARCHAR(100);
    END IF;

    -- Add trading_area column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'trading_area') THEN
        ALTER TABLE vessels ADD COLUMN trading_area VARCHAR(200);
    END IF;

    -- Add vessel_class_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'vessel_class_id') THEN
        ALTER TABLE vessels ADD COLUMN vessel_class_id UUID;
    END IF;

    -- Add vessel_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'vessel_type') THEN
        ALTER TABLE vessels ADD COLUMN vessel_type VARCHAR(50);
    END IF;
END $$;