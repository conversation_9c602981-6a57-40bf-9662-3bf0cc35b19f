@model IEnumerable<SMS_Maritime_Web.Models.VesselCrewAssignment>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CrewAssignments");
    var vessels = ViewBag.Vessels as List<SMS_Maritime_Web.Models.Vessel> ?? new List<SMS_Maritime_Web.Models.Vessel>();
    var selectedVesselId = ViewBag.SelectedVesselId as Guid?;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-check"></i> @LanguageService.GetText("CrewAssignments")
                    </h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> @LanguageService.GetText("FilterByVessel")
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="@Url.Action("Assignments")">@LanguageService.GetText("AllVessels")</a></li>
                                <li><hr class="dropdown-divider"></li>
                                @foreach (var vessel in vessels)
                                {
                                    <li>
                                        <a class="dropdown-item @(selectedVesselId == vessel.Id ? "active" : "")" 
                                           href="@Url.Action("Assignments", new { vesselId = vessel.Id })">
                                            @vessel.VesselName
                                        </a>
                                    </li>
                                }
                            </ul>
                        </div>
                        <a asp-action="AssignToVessel" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("NewAssignment")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="assignmentsTable">
                            <thead>
                                <tr>
                                    <th>@LanguageService.GetText("Vessel")</th>
                                    <th>@LanguageService.GetText("CrewMember")</th>
                                    <th>@LanguageService.GetText("Rank")</th>
                                    <th>@LanguageService.GetText("Department")</th>
                                    <th>@LanguageService.GetText("SignOnDate")</th>
                                    <th>@LanguageService.GetText("ExpectedSignOff")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("ReliefDue")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var assignment in Model)
                                {
                                    <tr>
                                        <td>
                                            <span class="badge bg-info">@assignment.Vessel?.VesselName</span>
                                        </td>
                                        <td>
                                            @if (assignment.User != null)
                                            {
                                                <a asp-controller="Crew" asp-action="Details" asp-route-id="@assignment.User.Id">
                                                    @assignment.User.FirstName @assignment.User.LastName
                                                </a>
                                            }
                                        </td>
                                        <td>@assignment.Rank?.Name</td>
                                        <td>@(assignment.Department ?? "-")</td>
                                        <td>@assignment.SignOnDate.ToString("yyyy-MM-dd")</td>
                                        <td>@(assignment.ExpectedSignOffDate?.ToString("yyyy-MM-dd") ?? "-")</td>
                                        <td>
                                            @if (assignment.Status == "Onboard")
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("OnBoard")</span>
                                            }
                                            else if (assignment.Status == "Completed")
                                            {
                                                <span class="badge bg-secondary">@LanguageService.GetText("Completed")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">@assignment.Status</span>
                                            }
                                        </td>
                                        <td>
                                            @if (assignment.ReliefDueDate.HasValue && assignment.Status == "Onboard")
                                            {
                                                var daysToRelief = (assignment.ReliefDueDate.Value - DateTime.Today).Days;
                                                <span class="@(daysToRelief < 30 ? "text-danger fw-bold" : daysToRelief < 60 ? "text-warning" : "")">
                                                    @assignment.ReliefDueDate.Value.ToString("yyyy-MM-dd")
                                                    <small>(@daysToRelief @LanguageService.GetText("Days"))</small>
                                                </span>
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (assignment.Status == "Onboard")
                                            {
                                                <button type="button" class="btn btn-danger btn-sm" onclick="showSignOffModal('@assignment.Id', '@assignment.User?.FirstName @assignment.User?.LastName')" title="@LanguageService.GetText("SignOff")">
                                                    <i class="fas fa-sign-out-alt"></i>
                                                </button>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sign Off Modal -->
<div class="modal fade" id="signOffModal" tabindex="-1" aria-labelledby="signOffModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="signOffForm" asp-action="SignOff" method="post">
                @Html.AntiForgeryToken()
                <input type="hidden" id="assignmentId" name="id" />
                <div class="modal-header">
                    <h5 class="modal-title" id="signOffModalLabel">@LanguageService.GetText("SignOffCrewMember")</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>@LanguageService.GetText("SignOffConfirmation") <strong id="crewNameToSignOff"></strong></p>
                    <div class="form-group">
                        <label for="signOffDate">@LanguageService.GetText("SignOffDate") *</label>
                        <input type="date" class="form-control" id="signOffDate" name="signOffDate" required value="@DateTime.Today.ToString("yyyy-MM-dd")" />
                    </div>
                    <div class="form-group">
                        <label for="signOffPort">@LanguageService.GetText("SignOffPort")</label>
                        <input type="text" class="form-control" id="signOffPort" name="signOffPort" maxlength="100" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@LanguageService.GetText("Cancel")</button>
                    <button type="submit" class="btn btn-danger">@LanguageService.GetText("SignOff")</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#assignmentsTable').DataTable({
                "language": {
                    "url": "/lib/datatables/i18n/@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName).json"
                },
                "order": [[0, "asc"], [4, "desc"]],
                "pageLength": 25,
                "responsive": true
            });
        });

        function showSignOffModal(assignmentId, crewName) {
            $('#assignmentId').val(assignmentId);
            $('#crewNameToSignOff').text(crewName);
            $('#signOffModal').modal('show');
        }
    </script>
}