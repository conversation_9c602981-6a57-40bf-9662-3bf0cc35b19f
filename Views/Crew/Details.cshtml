@model SMS_Maritime_Web.ViewModels.CrewViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CrewMemberDetails");
    var assignments = ViewBag.Assignments as List<SMS_Maritime_Web.Models.VesselCrewAssignment> ?? new List<SMS_Maritime_Web.Models.VesselCrewAssignment>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user"></i> @LanguageService.GetText("CrewMemberDetails") - @Model.FullName
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> @LanguageService.GetText("Edit")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Personal Information -->
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("PersonalInformation")</h4>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("EmployeeCode")</label>
                                <p class="form-control-static">@Model.EmployeeCode</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Name")</label>
                                <p class="form-control-static">@Model.FullName</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DateOfBirth")</label>
                                <p class="form-control-static">@(Model.DateOfBirth?.ToString("yyyy-MM-dd") ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Nationality")</label>
                                <p class="form-control-static">@(Model.Nationality ?? "-")</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("PlaceOfBirth")</label>
                                <p class="form-control-static">@(Model.PlaceOfBirth ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Department")</label>
                                <p class="form-control-static">@(Model.DepartmentName ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("HireDate")</label>
                                <p class="form-control-static">@(Model.HireDate?.ToString("yyyy-MM-dd") ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Status")</label>
                                <p class="form-control-static">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">@LanguageService.GetText("Active")</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">@LanguageService.GetText("Inactive")</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("ContactInformation")</h4>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Email")</label>
                                <p class="form-control-static">@Model.Email</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@LanguageService.GetText("PhoneNumber")</label>
                                <p class="form-control-static">@(Model.PhoneNumber ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DisplayName")</label>
                                <p class="form-control-static">@(Model.DisplayName ?? "-")</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Maritime Documents -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("MaritimeDocuments")</h4>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("PassportNumber")</label>
                                <p class="form-control-static">@(Model.PassportNumber ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("PassportExpiryDate")</label>
                                <p class="form-control-static">
                                    @if (Model.PassportExpiryDate.HasValue)
                                    {
                                        var daysToExpiry = (Model.PassportExpiryDate.Value - DateTime.Today).Days;
                                        <span class="@(daysToExpiry < 180 ? "text-danger" : "")">
                                            @Model.PassportExpiryDate.Value.ToString("yyyy-MM-dd")
                                            @if (daysToExpiry < 180)
                                            {
                                                <small class="text-danger">(@daysToExpiry @LanguageService.GetText("DaysToExpiry"))</small>
                                            }
                                        </span>
                                    }
                                    else
                                    {
                                        <span>-</span>
                                    }
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("SeamanBookNumber")</label>
                                <p class="form-control-static">@(Model.SeamanBookNumber ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("SeamanBookExpiryDate")</label>
                                <p class="form-control-static">
                                    @if (Model.SeamanBookExpiryDate.HasValue)
                                    {
                                        var daysToExpiry = (Model.SeamanBookExpiryDate.Value - DateTime.Today).Days;
                                        <span class="@(daysToExpiry < 180 ? "text-danger" : "")">
                                            @Model.SeamanBookExpiryDate.Value.ToString("yyyy-MM-dd")
                                            @if (daysToExpiry < 180)
                                            {
                                                <small class="text-danger">(@daysToExpiry @LanguageService.GetText("DaysToExpiry"))</small>
                                            }
                                        </span>
                                    }
                                    else
                                    {
                                        <span>-</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Assignment History -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("AssignmentHistory")</h4>
                            @if (assignments.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>@LanguageService.GetText("Vessel")</th>
                                                <th>@LanguageService.GetText("Rank")</th>
                                                <th>@LanguageService.GetText("SignOnDate")</th>
                                                <th>@LanguageService.GetText("SignOffDate")</th>
                                                <th>@LanguageService.GetText("Status")</th>
                                                <th>@LanguageService.GetText("Duration")</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var assignment in assignments)
                                            {
                                                <tr>
                                                    <td>@assignment.Vessel?.VesselName</td>
                                                    <td>@assignment.Rank?.Name</td>
                                                    <td>@assignment.SignOnDate.ToString("yyyy-MM-dd")</td>
                                                    <td>@(assignment.ActualSignOffDate?.ToString("yyyy-MM-dd") ?? "-")</td>
                                                    <td>
                                                        @if (assignment.Status == "Onboard")
                                                        {
                                                            <span class="badge bg-success">@LanguageService.GetText("OnBoard")</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-secondary">@assignment.Status</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (assignment.ActualSignOffDate.HasValue)
                                                        {
                                                            var days = (assignment.ActualSignOffDate.Value - assignment.SignOnDate).Days;
                                                            <span>@days @LanguageService.GetText("Days")</span>
                                                        }
                                                        else
                                                        {
                                                            var days = (DateTime.Today - assignment.SignOnDate).Days;
                                                            <span>@days @LanguageService.GetText("Days") (@LanguageService.GetText("Ongoing"))</span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">@LanguageService.GetText("NoAssignmentHistory")</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>