@model IEnumerable<SMS_Maritime_Web.ViewModels.CrewViewModel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CrewMembers");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i> @LanguageService.GetText("CrewMembers")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="crewTable">
                            <thead>
                                <tr>
                                    <th>@LanguageService.GetText("EmployeeCode")</th>
                                    <th>@LanguageService.GetText("Name")</th>
                                    <th>@LanguageService.GetText("Rank")</th>
                                    <th>@LanguageService.GetText("CurrentVessel")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("PassportExpiry")</th>
                                    <th>@LanguageService.GetText("SeamanBookExpiry")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var crew in Model)
                                {
                                    <tr>
                                        <td>@crew.EmployeeCode</td>
                                        <td>@crew.FullName</td>
                                        <td>@(crew.CurrentRankName ?? "-")</td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(crew.CurrentVesselName))
                                            {
                                                <span class="badge bg-info">@crew.CurrentVesselName</span>
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (crew.IsOnBoard)
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("OnBoard")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@LanguageService.GetText("OnLeave")</span>
                                            }
                                        </td>
                                        <td>
                                            @if (crew.PassportExpiryDate.HasValue)
                                            {
                                                var daysToExpiry = (crew.PassportExpiryDate.Value - DateTime.Today).Days;
                                                <span class="@(daysToExpiry < 180 ? "text-danger" : "")">
                                                    @crew.PassportExpiryDate.Value.ToString("yyyy-MM-dd")
                                                </span>
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (crew.SeamanBookExpiryDate.HasValue)
                                            {
                                                var daysToExpiry = (crew.SeamanBookExpiryDate.Value - DateTime.Today).Days;
                                                <span class="@(daysToExpiry < 180 ? "text-danger" : "")">
                                                    @crew.SeamanBookExpiryDate.Value.ToString("yyyy-MM-dd")
                                                </span>
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@crew.Id" class="btn btn-info btn-sm" title="@LanguageService.GetText("Details")">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@crew.Id" class="btn btn-warning btn-sm" title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if (!crew.IsOnBoard)
                                                {
                                                    <a asp-action="AssignToVessel" asp-route-userId="@crew.Id" class="btn btn-success btn-sm" title="@LanguageService.GetText("AssignToVessel")">
                                                        <i class="fas fa-ship"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#crewTable').DataTable({
                "language": {
                    "url": "/lib/datatables/i18n/@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName).json"
                },
                "order": [[1, "asc"]],
                "pageLength": 25,
                "responsive": true
            });
        });
    </script>
}