@model SMS_Maritime_Web.ViewModels.CrewViewModel
@using Microsoft.AspNetCore.Mvc.Rendering
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CreateCrewMember");
    var departments = ViewData["Departments"] as List<SMS_Maritime_Web.Models.CrewDepartment> ?? new List<SMS_Maritime_Web.Models.CrewDepartment>();
    var nationalities = ViewData["Nationalities"] as List<SelectListItem> ?? new List<SelectListItem>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-plus"></i> @LanguageService.GetText("CreateCrewMember")
                    </h3>
                </div>
                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Personal Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("PersonalInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="EmployeeCode" class="control-label">@LanguageService.GetText("EmployeeCode") *</label>
                                    <input asp-for="EmployeeCode" class="form-control" />
                                    <span asp-validation-for="EmployeeCode" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="FirstName" class="control-label">@LanguageService.GetText("FirstName") *</label>
                                    <input asp-for="FirstName" class="form-control" />
                                    <span asp-validation-for="FirstName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="LastName" class="control-label">@LanguageService.GetText("LastName") *</label>
                                    <input asp-for="LastName" class="form-control" />
                                    <span asp-validation-for="LastName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DateOfBirth" class="control-label">@LanguageService.GetText("DateOfBirth")</label>
                                    <input asp-for="DateOfBirth" type="date" class="form-control" />
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Nationality" class="control-label">@LanguageService.GetText("Nationality")</label>
                                    <select asp-for="Nationality" asp-items="@nationalities" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                    </select>
                                    <span asp-validation-for="Nationality" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="PlaceOfBirth" class="control-label">@LanguageService.GetText("PlaceOfBirth")</label>
                                    <input asp-for="PlaceOfBirth" class="form-control" />
                                    <span asp-validation-for="PlaceOfBirth" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DepartmentId" class="control-label">@LanguageService.GetText("Department")</label>
                                    <select asp-for="DepartmentId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var dept in departments)
                                        {
                                            <option value="@dept.Id">@dept.Name</option>
                                        }
                                    </select>
                                    <span asp-validation-for="DepartmentId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="HireDate" class="control-label">@LanguageService.GetText("HireDate")</label>
                                    <input asp-for="HireDate" type="date" class="form-control" />
                                    <span asp-validation-for="HireDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Contact Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("ContactInformation")</h4>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="Email" class="control-label">@LanguageService.GetText("Email") *</label>
                                    <input asp-for="Email" type="email" class="form-control" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="PhoneNumber" class="control-label">@LanguageService.GetText("PhoneNumber")</label>
                                    <input asp-for="PhoneNumber" type="tel" class="form-control" />
                                    <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="DisplayName" class="control-label">@LanguageService.GetText("DisplayName")</label>
                                    <input asp-for="DisplayName" class="form-control" placeholder="@LanguageService.GetText("LeaveEmptyForAutoGenerate")" />
                                    <span asp-validation-for="DisplayName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Maritime Documents -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("MaritimeDocuments")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="PassportNumber" class="control-label">@LanguageService.GetText("PassportNumber")</label>
                                    <input asp-for="PassportNumber" class="form-control" />
                                    <span asp-validation-for="PassportNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="PassportExpiryDate" class="control-label">@LanguageService.GetText("PassportExpiryDate")</label>
                                    <input asp-for="PassportExpiryDate" type="date" class="form-control" />
                                    <span asp-validation-for="PassportExpiryDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="SeamanBookNumber" class="control-label">@LanguageService.GetText("SeamanBookNumber")</label>
                                    <input asp-for="SeamanBookNumber" class="form-control" />
                                    <span asp-validation-for="SeamanBookNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="SeamanBookExpiryDate" class="control-label">@LanguageService.GetText("SeamanBookExpiryDate")</label>
                                    <input asp-for="SeamanBookExpiryDate" type="date" class="form-control" />
                                    <span asp-validation-for="SeamanBookExpiryDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Status -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("Status")</h4>
                            </div>
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="IsActive" class="form-check-label">
                                        @LanguageService.GetText("IsActive")
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}