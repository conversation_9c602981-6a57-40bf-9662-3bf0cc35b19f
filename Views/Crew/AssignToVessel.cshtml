@model SMS_Maritime_Web.ViewModels.CrewAssignmentViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("AssignCrewToVessel");
    var vessels = ViewData["Vessels"] as List<SMS_Maritime_Web.Models.Vessel> ?? new List<SMS_Maritime_Web.Models.Vessel>();
    var crew = ViewData["Crew"] as List<SMS_Maritime_Web.Models.User> ?? new List<SMS_Maritime_Web.Models.User>();
    var ranks = ViewData["Ranks"] as List<SMS_Maritime_Web.Models.CrewRank> ?? new List<SMS_Maritime_Web.Models.CrewRank>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-plus"></i> @LanguageService.GetText("AssignCrewToVessel")
                    </h3>
                </div>
                <form asp-action="AssignToVessel" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="VesselId" class="control-label">@LanguageService.GetText("Vessel") *</label>
                                    <select asp-for="VesselId" class="form-control select2-vessel" required>
                                        <option value="">-- @LanguageService.GetText("SelectVessel") --</option>
                                        @foreach (var vessel in vessels)
                                        {
                                            <option value="@vessel.Id">@vessel.VesselName (@vessel.VesselCode)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="VesselId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="UserId" class="control-label">@LanguageService.GetText("CrewMember") *</label>
                                    <select asp-for="UserId" class="form-control select2-crew" required>
                                        <option value="">-- @LanguageService.GetText("SelectCrewMember") --</option>
                                        @foreach (var member in crew)
                                        {
                                            <option value="@member.Id">
                                                @member.FirstName @member.LastName 
                                                (@member.EmployeeCode)
                                                @if (!string.IsNullOrEmpty(member.SeamanBookNumber))
                                                {
                                                    <text> - SB: @member.SeamanBookNumber</text>
                                                }
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="UserId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="RankId" class="control-label">@LanguageService.GetText("Rank") *</label>
                                    <select asp-for="RankId" class="form-control" required>
                                        <option value="">-- @LanguageService.GetText("SelectRank") --</option>
                                        @foreach (var rankGroup in ranks.GroupBy(r => r.Department))
                                        {
                                            <optgroup label="@rankGroup.Key">
                                                @foreach (var rank in rankGroup)
                                                {
                                                    <option value="@rank.Id">@rank.Name</option>
                                                }
                                            </optgroup>
                                        }
                                    </select>
                                    <span asp-validation-for="RankId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="Department" class="control-label">@LanguageService.GetText("Department")</label>
                                    <input asp-for="Department" class="form-control" readonly />
                                    <span asp-validation-for="Department" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="ContractDurationMonths" class="control-label">@LanguageService.GetText("ContractDuration") (@LanguageService.GetText("Months"))</label>
                                    <input asp-for="ContractDurationMonths" type="number" class="form-control" min="1" max="24" value="6" />
                                    <span asp-validation-for="ContractDurationMonths" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SignOnDate" class="control-label">@LanguageService.GetText("SignOnDate")</label>
                                    <input asp-for="SignOnDate" type="date" class="form-control" value="@DateTime.Today.ToString("yyyy-MM-dd")" />
                                    <span asp-validation-for="SignOnDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SignOnPort" class="control-label">@LanguageService.GetText("SignOnPort")</label>
                                    <input asp-for="SignOnPort" class="form-control" maxlength="100" />
                                    <span asp-validation-for="SignOnPort" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>@LanguageService.GetText("ExpectedSignOffDate")</label>
                                    <input type="text" class="form-control" id="expectedSignOffDate" readonly />
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label asp-for="Notes" class="control-label">@LanguageService.GetText("Notes")</label>
                                    <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Notes" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("AssignCrew")
                        </button>
                        <a asp-action="Assignments" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2-vessel').select2({
                placeholder: '@LanguageService.GetText("SelectVessel")',
                allowClear: true
            });
            
            $('.select2-crew').select2({
                placeholder: '@LanguageService.GetText("SelectCrewMember")',
                allowClear: true
            });
            
            // Auto-populate department based on rank selection
            $('#RankId').on('change', function() {
                var selectedOption = $(this).find('option:selected');
                var department = selectedOption.closest('optgroup').attr('label');
                $('#Department').val(department || '');
                calculateExpectedSignOffDate();
            });
            
            // Calculate expected sign-off date
            function calculateExpectedSignOffDate() {
                var signOnDate = $('#SignOnDate').val();
                var contractMonths = $('#ContractDurationMonths').val();
                
                if (signOnDate && contractMonths) {
                    var date = new Date(signOnDate);
                    date.setMonth(date.getMonth() + parseInt(contractMonths));
                    $('#expectedSignOffDate').val(date.toISOString().split('T')[0]);
                }
            }
            
            $('#SignOnDate, #ContractDurationMonths').on('change', calculateExpectedSignOffDate);
            
            // Initial calculation
            calculateExpectedSignOffDate();
        });
    </script>
}