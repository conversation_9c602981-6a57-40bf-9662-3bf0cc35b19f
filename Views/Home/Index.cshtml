@model SMS_Maritime_Web.ViewModels.DashboardViewModel
@using SMS_Maritime_Web.Services
@inject ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Dashboard");
}

<div class="container-fluid">
    <h1 class="h3 mb-4">@LanguageService.GetText("Dashboard")</h1>
    
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                @LanguageService.GetText("TotalVessels")</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalVessels</div>
                            <small class="text-muted">@Model.ActiveVessels @LanguageService.GetText("Active")</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-ship fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                @LanguageService.GetText("CrewMembers")</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalCrew</div>
                            <small class="text-muted">@Model.CrewOnboard @LanguageService.GetText("Onboard")</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                @LanguageService.GetText("ActiveVoyages")</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ActiveVoyages</div>
                            <small class="text-muted">@Model.CompletedVoyagesThisMonth @LanguageService.GetText("CompletedThisMonth")</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-route fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                @LanguageService.GetText("DocumentAlerts")</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ExpiredDocuments</div>
                            <small class="text-muted">@Model.ExpiringDocuments @LanguageService.GetText("ExpiringSoon")</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Voyages -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">@LanguageService.GetText("RecentVoyages")</h6>
                </div>
                <div class="card-body">
                    @if (Model.RecentVoyages.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>@LanguageService.GetText("VoyageNumber")</th>
                                        <th>@LanguageService.GetText("Vessel")</th>
                                        <th>@LanguageService.GetText("Route")</th>
                                        <th>@LanguageService.GetText("Status")</th>
                                        <th>@LanguageService.GetText("Departure")</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var voyage in Model.RecentVoyages)
                                    {
                                        <tr>
                                            <td>
                                                <a asp-controller="Voyages" asp-action="Details" asp-route-id="@voyage.Id">
                                                    @voyage.VoyageNumber
                                                </a>
                                            </td>
                                            <td>@voyage.VesselName</td>
                                            <td>@voyage.DeparturePort → @voyage.ArrivalPort</td>
                                            <td>
                                                <span class="badge badge-@GetStatusBadgeClass(voyage.Status)">
                                                    @LanguageService.GetText(voyage.Status)
                                                </span>
                                            </td>
                                            <td>@voyage.DepartureDate.ToString("dd.MM.yyyy")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted text-center">@LanguageService.GetText("NoRecentVoyages")</p>
                    }
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="col-lg-4">
            <!-- Vessel Status Chart -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">@LanguageService.GetText("VesselsByStatus")</h6>
                </div>
                <div class="card-body">
                    <canvas id="vesselStatusChart"></canvas>
                </div>
            </div>

            <!-- Crew by Department -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">@LanguageService.GetText("CrewByDepartment")</h6>
                </div>
                <div class="card-body">
                    @if (Model.CrewByDepartment.Any())
                    {
                        @foreach (var dept in Model.CrewByDepartment.OrderByDescending(d => d.Count))
                        {
                            <h4 class="small font-weight-bold">@dept.DepartmentName 
                                <span class="float-right">@dept.Count</span>
                            </h4>
                            <div class="progress mb-4">
                                @{
                                    var percentage = Model.TotalCrew > 0 ? (dept.Count * 100 / Model.TotalCrew) : 0;
                                }
                                <div class="progress-bar bg-info" role="progressbar" 
                                     style="width: @percentage%" 
                                     aria-valuenow="@percentage" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted text-center">@LanguageService.GetText("NoData")</p>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Vessel Status Chart
        @if (Model.VesselsByStatus.Any())
        {
            <text>
            var ctx = document.getElementById('vesselStatusChart').getContext('2d');
            var vesselStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: [@Html.Raw(string.Join(",", Model.VesselsByStatus.Select(v => $"'{LanguageService.GetText(v.Status)}'").ToArray()))],
                    datasets: [{
                        data: [@string.Join(",", Model.VesselsByStatus.Select(v => v.Count))],
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(255, 206, 86, 0.8)',
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'bottom'
                    }
                }
            });
            </text>
        }
    </script>
}

@functions {
    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "PLANNED" => "info",
            "IN_PROGRESS" => "primary",
            "AT_SEA" => "primary",
            "IN_PORT" => "warning",
            "COMPLETED" => "success",
            "CANCELLED" => "danger",
            _ => "secondary"
        };
    }
}