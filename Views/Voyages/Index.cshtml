@model IEnumerable<SMS_Maritime_Web.ViewModels.VoyageViewModel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Voyages");
    var statuses = ViewBag.Statuses as List<SMS_Maritime_Web.Models.VoyageStatus> ?? new List<SMS_Maritime_Web.Models.VoyageStatus>();
    var selectedStatus = ViewBag.SelectedStatus as string;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-route"></i> @LanguageService.GetText("Voyages")
                    </h3>
                    <div class="card-tools">
                        <div class="btn-group btn-group-sm me-2">
                            <a asp-action="Index" class="btn @(string.IsNullOrEmpty(selectedStatus) ? "btn-primary" : "btn-outline-primary")">
                                @LanguageService.GetText("All")
                            </a>
                            @foreach (var status in statuses)
                            {
                                <a asp-action="Index" asp-route-status="@status.Code" 
                                   class="btn @(selectedStatus == status.Code ? "btn-primary" : "btn-outline-primary")"
                                   style="@(!string.IsNullOrEmpty(status.Color) && selectedStatus == status.Code ? $"background-color: {status.Color}; border-color: {status.Color};" : "")">
                                    @LanguageService.GetText(status.Code)
                                </a>
                            }
                        </div>
                        <a asp-action="Create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="voyagesTable">
                            <thead>
                                <tr>
                                    <th>@LanguageService.GetText("VoyageNumber")</th>
                                    <th>@LanguageService.GetText("Vessel")</th>
                                    <th>@LanguageService.GetText("DeparturePort")</th>
                                    <th>@LanguageService.GetText("ArrivalPort")</th>
                                    <th>@LanguageService.GetText("DepartureDate")</th>
                                    <th>@LanguageService.GetText("ETA")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("CurrentLocation")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var voyage in Model)
                                {
                                    <tr>
                                        <td>
                                            <a asp-action="Details" asp-route-id="@voyage.Id">
                                                <strong>@voyage.VoyageNumber</strong>
                                            </a>
                                        </td>
                                        <td>@voyage.VesselName</td>
                                        <td>@voyage.DeparturePort</td>
                                        <td>@voyage.ArrivalPort</td>
                                        <td>@voyage.DepartureDate.ToString("yyyy-MM-dd HH:mm")</td>
                                        <td>
                                            @if (voyage.Ata.HasValue)
                                            {
                                                <span class="text-success">@voyage.Ata.Value.ToString("yyyy-MM-dd HH:mm")</span>
                                            }
                                            else if (voyage.Eta.HasValue)
                                            {
                                                @voyage.Eta.Value.ToString("yyyy-MM-dd HH:mm")
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            @{
                                                var statusInfo = statuses.FirstOrDefault(s => s.Code == voyage.Status);
                                                var statusColor = statusInfo?.Color ?? "#6c757d";
                                            }
                                            <span class="badge" style="background-color: @statusColor">
                                                @LanguageService.GetText(voyage.Status)
                                            </span>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(voyage.CurrentLocation))
                                            {
                                                if (voyage.CurrentLocation == "At Sea")
                                                {
                                                    <span class="text-info"><i class="fas fa-water"></i> @LanguageService.GetText("AtSea")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-warning"><i class="fas fa-anchor"></i> @voyage.CurrentLocation</span>
                                                }
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@voyage.Id" class="btn btn-info btn-sm" title="@LanguageService.GetText("Details")">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@voyage.Id" class="btn btn-warning btn-sm" title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="PortCalls" asp-route-id="@voyage.Id" class="btn btn-primary btn-sm" title="@LanguageService.GetText("PortCalls")">
                                                    <i class="fas fa-anchor"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#voyagesTable').DataTable({
                "language": {
                    "url": "/lib/datatables/i18n/@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName).json"
                },
                "order": [[4, "desc"]], // Sort by departure date descending
                "pageLength": 25,
                "responsive": true
            });
        });
    </script>
}