@model SMS_Maritime_Web.ViewModels.VoyageViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("EditVoyage");
    var vessels = ViewData["Vessels"] as List<SMS_Maritime_Web.Models.Vessel> ?? new List<SMS_Maritime_Web.Models.Vessel>();
    var statuses = ViewData["Statuses"] as List<SMS_Maritime_Web.Models.VoyageStatus> ?? new List<SMS_Maritime_Web.Models.VoyageStatus>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit"></i> @LanguageService.GetText("EditVoyage") - @Model.VoyageNumber
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> @LanguageService.GetText("Details")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <form asp-action="Edit" method="post">
                    <input type="hidden" asp-for="Id" />
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("BasicInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VesselId" class="control-label">@LanguageService.GetText("Vessel") *</label>
                                    <select asp-for="VesselId" class="form-control" required>
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var vessel in vessels)
                                        {
                                            <option value="@vessel.Id" selected="@(vessel.Id == Model.VesselId ? "selected" : null)">@vessel.VesselName (@vessel.VesselCode)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="VesselId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VoyageNumber" class="control-label">@LanguageService.GetText("VoyageNumber") *</label>
                                    <input asp-for="VoyageNumber" class="form-control" />
                                    <span asp-validation-for="VoyageNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VoyageType" class="control-label">@LanguageService.GetText("VoyageType")</label>
                                    <select asp-for="VoyageType" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        <option value="Laden">@LanguageService.GetText("Laden")</option>
                                        <option value="Ballast">@LanguageService.GetText("Ballast")</option>
                                        <option value="Positioning">@LanguageService.GetText("Positioning")</option>
                                        <option value="Charter">@LanguageService.GetText("Charter")</option>
                                        <option value="Other">@LanguageService.GetText("Other")</option>
                                    </select>
                                    <span asp-validation-for="VoyageType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Status" class="control-label">@LanguageService.GetText("Status")</label>
                                    <select asp-for="Status" class="form-control">
                                        @if (statuses.Any())
                                        {
                                            @foreach (var status in statuses)
                                            {
                                                <option value="@status.Code" selected="@(status.Code == Model.Status ? "selected" : null)">@status.Name</option>
                                            }
                                        }
                                        else
                                        {
                                            <option value="Planned" selected="@(Model.Status == "Planned" ? "selected" : null)">@LanguageService.GetText("Planned")</option>
                                            <option value="In Progress" selected="@(Model.Status == "In Progress" ? "selected" : null)">@LanguageService.GetText("InProgress")</option>
                                            <option value="At Sea" selected="@(Model.Status == "At Sea" ? "selected" : null)">@LanguageService.GetText("AtSea")</option>
                                            <option value="In Port" selected="@(Model.Status == "In Port" ? "selected" : null)">@LanguageService.GetText("InPort")</option>
                                            <option value="Completed" selected="@(Model.Status == "Completed" ? "selected" : null)">@LanguageService.GetText("Completed")</option>
                                            <option value="Cancelled" selected="@(Model.Status == "Cancelled" ? "selected" : null)">@LanguageService.GetText("Cancelled")</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Route Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("RouteInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DeparturePort" class="control-label">@LanguageService.GetText("DeparturePort") *</label>
                                    <input asp-for="DeparturePort" class="form-control" />
                                    <span asp-validation-for="DeparturePort" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DepartureDate" class="control-label">@LanguageService.GetText("DepartureDate") *</label>
                                    <input asp-for="DepartureDate" type="datetime-local" class="form-control" />
                                    <span asp-validation-for="DepartureDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="ArrivalPort" class="control-label">@LanguageService.GetText("ArrivalPort") *</label>
                                    <input asp-for="ArrivalPort" class="form-control" />
                                    <span asp-validation-for="ArrivalPort" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Eta" class="control-label">@LanguageService.GetText("ETA")</label>
                                    <input asp-for="Eta" type="datetime-local" class="form-control" />
                                    <span asp-validation-for="Eta" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Ata" class="control-label">@LanguageService.GetText("ATA")</label>
                                    <input asp-for="Ata" type="datetime-local" class="form-control" />
                                    <span asp-validation-for="Ata" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DistanceMiles" class="control-label">@LanguageService.GetText("DistanceMiles")</label>
                                    <input asp-for="DistanceMiles" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="DistanceMiles" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Cargo Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("CargoInformation")</h4>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CargoDescription" class="control-label">@LanguageService.GetText("CargoDescription")</label>
                                    <textarea asp-for="CargoDescription" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="CargoDescription" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="CargoQuantity" class="control-label">@LanguageService.GetText("CargoQuantity")</label>
                                    <input asp-for="CargoQuantity" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="CargoQuantity" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="CargoUnit" class="control-label">@LanguageService.GetText("CargoUnit")</label>
                                    <select asp-for="CargoUnit" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        <option value="MT">MT (Metric Tons)</option>
                                        <option value="CBM">CBM (Cubic Meters)</option>
                                        <option value="TEU">TEU (Twenty-foot Equivalent Unit)</option>
                                        <option value="FEU">FEU (Forty-foot Equivalent Unit)</option>
                                        <option value="Barrels">Barrels</option>
                                        <option value="Pieces">Pieces</option>
                                        <option value="Other">@LanguageService.GetText("Other")</option>
                                    </select>
                                    <span asp-validation-for="CargoUnit" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Charter Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("CharterInformation")</h4>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CharterPartyRef" class="control-label">@LanguageService.GetText("CharterPartyRef")</label>
                                    <input asp-for="CharterPartyRef" class="form-control" />
                                    <span asp-validation-for="CharterPartyRef" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Remarks" class="control-label">@LanguageService.GetText("Remarks")</label>
                                    <textarea asp-for="Remarks" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Remarks" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                            <i class="fas fa-eye"></i> @LanguageService.GetText("Details")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}