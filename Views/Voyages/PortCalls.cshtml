@model SMS_Maritime_Web.Models.VesselVoyage
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("PortCalls");
    var portCalls = Model.PortCalls?.OrderBy(pc => pc.PortSequence).ToList() ?? new List<SMS_Maritime_Web.Models.VoyagePortCall>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-anchor"></i> @LanguageService.GetText("PortCalls") - @Model.VoyageNumber
                    </h3>
                    <div class="card-tools">
                        <a asp-action="AddPortCall" asp-route-voyageId="@Model.Id" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddPortCall")
                        </a>
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> @LanguageService.GetText("Details")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Voyage Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("VoyageSummary")</h4>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Vessel")</label>
                                <p class="form-control-static">@Model.Vessel?.VesselName</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("VoyageNumber")</label>
                                <p class="form-control-static">@Model.VoyageNumber</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DeparturePort")</label>
                                <p class="form-control-static">@Model.DeparturePort</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("ArrivalPort")</label>
                                <p class="form-control-static">@Model.ArrivalPort</p>
                            </div>
                        </div>
                    </div>
                    
                    <hr />
                    
                    <!-- Port Calls Table -->
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">
                                @LanguageService.GetText("PortCallsList") 
                                <span class="badge bg-info">@portCalls.Count @LanguageService.GetText("Calls")</span>
                            </h4>
                            
                            @if (portCalls.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>@LanguageService.GetText("Sequence")</th>
                                                <th>@LanguageService.GetText("Port")</th>
                                                <th>@LanguageService.GetText("PortType")</th>
                                                <th>@LanguageService.GetText("ETA")</th>
                                                <th>@LanguageService.GetText("ATA")</th>
                                                <th>@LanguageService.GetText("ETB")</th>
                                                <th>@LanguageService.GetText("ATB")</th>
                                                <th>@LanguageService.GetText("ETD")</th>
                                                <th>@LanguageService.GetText("ATD")</th>
                                                <th>@LanguageService.GetText("Status")</th>
                                                <th>@LanguageService.GetText("Actions")</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var portCall in portCalls)
                                            {
                                                <tr class="@(portCall.IsCompleted ? "table-success" : portCall.IsInPort ? "table-warning" : "")">
                                                    <td>
                                                        <span class="badge bg-primary">@portCall.PortSequence</span>
                                                    </td>
                                                    <td>
                                                        <strong>@portCall.PortName</strong>
                                                        @if (!string.IsNullOrEmpty(portCall.PortCountry))
                                                        {
                                                            <br /><small class="text-muted">@portCall.PortCountry</small>
                                                        }
                                                        @if (!string.IsNullOrEmpty(portCall.BerthName))
                                                        {
                                                            <br /><small class="text-info">@LanguageService.GetText("Berth"): @portCall.BerthName</small>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (!string.IsNullOrEmpty(portCall.PortType))
                                                        {
                                                            <span class="badge bg-secondary">@portCall.PortType</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td class="text-nowrap">
                                                        @if (portCall.Eta.HasValue)
                                                        {
                                                            @portCall.Eta.Value.ToString("yyyy-MM-dd HH:mm")
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td class="text-nowrap">
                                                        @if (portCall.Ata.HasValue)
                                                        {
                                                            @portCall.Ata.Value.ToString("yyyy-MM-dd HH:mm")
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td class="text-nowrap">
                                                        @if (portCall.Etb.HasValue)
                                                        {
                                                            @portCall.Etb.Value.ToString("yyyy-MM-dd HH:mm")
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td class="text-nowrap">
                                                        @if (portCall.Atb.HasValue)
                                                        {
                                                            @portCall.Atb.Value.ToString("yyyy-MM-dd HH:mm")
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td class="text-nowrap">
                                                        @if (portCall.Etd.HasValue)
                                                        {
                                                            @portCall.Etd.Value.ToString("yyyy-MM-dd HH:mm")
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td class="text-nowrap">
                                                        @if (portCall.Atd.HasValue)
                                                        {
                                                            @portCall.Atd.Value.ToString("yyyy-MM-dd HH:mm")
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (portCall.IsCompleted)
                                                        {
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check"></i> @LanguageService.GetText("Completed")
                                                            </span>
                                                        }
                                                        else if (portCall.IsInPort)
                                                        {
                                                            <span class="badge bg-warning">
                                                                <i class="fas fa-anchor"></i> @LanguageService.GetText("InPort")
                                                            </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-primary">
                                                                <i class="fas fa-clock"></i> @LanguageService.GetText("Planned")
                                                            </span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#portCallModal@(portCall.Id)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            @if (!portCall.IsCompleted)
                                                            {
                                                                <button type="button" class="btn btn-warning btn-sm">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                            }
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Port Call Detail Modals -->
                                @foreach (var portCall in portCalls)
                                {
                                    <div class="modal fade" id="portCallModal@(portCall.Id)" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">
                                                        <i class="fas fa-anchor"></i> @portCall.PortDescription - @LanguageService.GetText("Details")
                                                    </h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6>@LanguageService.GetText("PortInformation")</h6>
                                                            <table class="table table-sm">
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("PortName"):</strong></td>
                                                                    <td>@portCall.PortName</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("Country"):</strong></td>
                                                                    <td>@(portCall.PortCountry ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("PortType"):</strong></td>
                                                                    <td>@(portCall.PortType ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("Berth"):</strong></td>
                                                                    <td>@(portCall.BerthName ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("Sequence"):</strong></td>
                                                                    <td>@portCall.PortSequence</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6>@LanguageService.GetText("TimingInformation")</h6>
                                                            <table class="table table-sm">
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("ETA"):</strong></td>
                                                                    <td>@(portCall.Eta?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("ATA"):</strong></td>
                                                                    <td>@(portCall.Ata?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("ETB"):</strong></td>
                                                                    <td>@(portCall.Etb?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("ATB"):</strong></td>
                                                                    <td>@(portCall.Atb?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("ETD"):</strong></td>
                                                                    <td>@(portCall.Etd?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>@LanguageService.GetText("ATD"):</strong></td>
                                                                    <td>@(portCall.Atd?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    @if (!string.IsNullOrEmpty(portCall.AgentName) || !string.IsNullOrEmpty(portCall.AgentContact))
                                                    {
                                                        <div class="row mt-3">
                                                            <div class="col-md-12">
                                                                <h6>@LanguageService.GetText("AgentInformation")</h6>
                                                                @if (!string.IsNullOrEmpty(portCall.AgentName))
                                                                {
                                                                    <p><strong>@LanguageService.GetText("AgentName"):</strong> @portCall.AgentName</p>
                                                                }
                                                                @if (!string.IsNullOrEmpty(portCall.AgentContact))
                                                                {
                                                                    <p><strong>@LanguageService.GetText("AgentContact"):</strong><br />@portCall.AgentContact</p>
                                                                }
                                                            </div>
                                                        </div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(portCall.PortActivities))
                                                    {
                                                        <div class="row mt-3">
                                                            <div class="col-md-12">
                                                                <h6>@LanguageService.GetText("PortActivities")</h6>
                                                                <p>@portCall.PortActivities</p>
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@LanguageService.GetText("Close")</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-center py-5">
                                    <i class="fas fa-anchor fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">@LanguageService.GetText("NoPortCalls")</h5>
                                    <p class="text-muted">@LanguageService.GetText("NoPortCallsDescription")</p>
                                    <a asp-action="AddPortCall" asp-route-voyageId="@Model.Id" class="btn btn-success">
                                        <i class="fas fa-plus"></i> @LanguageService.GetText("AddFirstPortCall")
                                    </a>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>