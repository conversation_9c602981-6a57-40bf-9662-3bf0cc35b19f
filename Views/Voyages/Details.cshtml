@model SMS_Maritime_Web.ViewModels.VoyageViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("VoyageDetails");
    var portCalls = ViewBag.PortCalls as List<SMS_Maritime_Web.Models.VoyagePortCall> ?? new List<SMS_Maritime_Web.Models.VoyagePortCall>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ship"></i> @LanguageService.GetText("VoyageDetails") - @Model.VoyageNumber
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> @LanguageService.GetText("Edit")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Basic Voyage Information -->
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("BasicInformation")</h4>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("VoyageNumber")</label>
                                <p class="form-control-static">@Model.VoyageNumber</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Vessel")</label>
                                <p class="form-control-static">@(Model.VesselName ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("VoyageType")</label>
                                <p class="form-control-static">@(Model.VoyageType ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Status")</label>
                                <p class="form-control-static">
                                    @if (Model.Status == "Planned")
                                    {
                                        <span class="badge bg-primary">@LanguageService.GetText("Planned")</span>
                                    }
                                    else if (Model.Status == "In Progress")
                                    {
                                        <span class="badge bg-warning">@LanguageService.GetText("InProgress")</span>
                                    }
                                    else if (Model.Status == "Completed")
                                    {
                                        <span class="badge bg-success">@LanguageService.GetText("Completed")</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">@Model.Status</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Route Information -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("RouteInformation")</h4>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DeparturePort")</label>
                                <p class="form-control-static">@Model.DeparturePort</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DepartureDate")</label>
                                <p class="form-control-static">@Model.DepartureDate.ToString("yyyy-MM-dd HH:mm")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("ArrivalPort")</label>
                                <p class="form-control-static">@Model.ArrivalPort</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("ETA")</label>
                                <p class="form-control-static">@(Model.Eta?.ToString("yyyy-MM-dd HH:mm") ?? "-")</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("ATA")</label>
                                <p class="form-control-static">@(Model.Ata?.ToString("yyyy-MM-dd HH:mm") ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DistanceMiles")</label>
                                <p class="form-control-static">@(Model.DistanceMiles?.ToString("N0") ?? "-") @LanguageService.GetText("Miles")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CurrentLocation")</label>
                                <p class="form-control-static">@(Model.CurrentLocation ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("PortCalls")</label>
                                <p class="form-control-static">@Model.PortCallCount</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cargo Information -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("CargoInformation")</h4>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CargoDescription")</label>
                                <p class="form-control-static">@(Model.CargoDescription ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CargoQuantity")</label>
                                <p class="form-control-static">@(Model.CargoQuantity?.ToString("N2") ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CargoUnit")</label>
                                <p class="form-control-static">@(Model.CargoUnit ?? "-")</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Charter Information -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("CharterInformation")</h4>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CharterPartyRef")</label>
                                <p class="form-control-static">@(Model.CharterPartyRef ?? "-")</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Remarks")</label>
                                <p class="form-control-static">@(Model.Remarks ?? "-")</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Port Calls -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("PortCalls")</h4>
                            @if (portCalls.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead>
                                            <tr>
                                                <th>@LanguageService.GetText("Sequence")</th>
                                                <th>@LanguageService.GetText("Port")</th>
                                                <th>@LanguageService.GetText("PortType")</th>
                                                <th>@LanguageService.GetText("ETA")</th>
                                                <th>@LanguageService.GetText("ATA")</th>
                                                <th>@LanguageService.GetText("ETD")</th>
                                                <th>@LanguageService.GetText("ATD")</th>
                                                <th>@LanguageService.GetText("Status")</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var portCall in portCalls.OrderBy(pc => pc.PortSequence))
                                            {
                                                <tr>
                                                    <td>@portCall.PortSequence</td>
                                                    <td>@portCall.PortDescription</td>
                                                    <td>@(portCall.PortType ?? "-")</td>
                                                    <td>@(portCall.Eta?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                    <td>@(portCall.Ata?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                    <td>@(portCall.Etd?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                    <td>@(portCall.Atd?.ToString("yyyy-MM-dd HH:mm") ?? "-")</td>
                                                    <td>
                                                        @if (portCall.IsCompleted)
                                                        {
                                                            <span class="badge bg-success">@LanguageService.GetText("Completed")</span>
                                                        }
                                                        else if (portCall.IsInPort)
                                                        {
                                                            <span class="badge bg-warning">@LanguageService.GetText("InPort")</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-primary">@LanguageService.GetText("Planned")</span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">@LanguageService.GetText("NoPortCalls")</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>