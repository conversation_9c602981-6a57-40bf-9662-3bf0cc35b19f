@model SMS_Maritime_Web.ViewModels.VoyageViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CreateVoyage");
    var vessels = ViewData["Vessels"] as List<SMS_Maritime_Web.Models.Vessel> ?? new List<SMS_Maritime_Web.Models.Vessel>();
    var statuses = ViewData["Statuses"] as List<SMS_Maritime_Web.Models.VoyageStatus> ?? new List<SMS_Maritime_Web.Models.VoyageStatus>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-route"></i> @LanguageService.GetText("CreateVoyage")
                    </h3>
                </div>
                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("BasicInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VesselId" class="control-label">@LanguageService.GetText("Vessel") *</label>
                                    <select asp-for="VesselId" class="form-control" required>
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var vessel in vessels)
                                        {
                                            <option value="@vessel.Id">@vessel.VesselName (@vessel.VesselCode)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="VesselId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VoyageNumber" class="control-label">@LanguageService.GetText("VoyageNumber") *</label>
                                    <input asp-for="VoyageNumber" class="form-control" />
                                    <span asp-validation-for="VoyageNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VoyageType" class="control-label">@LanguageService.GetText("VoyageType")</label>
                                    <input asp-for="VoyageType" class="form-control" />
                                    <span asp-validation-for="VoyageType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Status" class="control-label">@LanguageService.GetText("Status")</label>
                                    <select asp-for="Status" class="form-control">
                                        @foreach (var status in statuses)
                                        {
                                            <option value="@status.Code">@LanguageService.GetText(status.Code)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Route Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("RouteInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DeparturePort" class="control-label">@LanguageService.GetText("DeparturePort") *</label>
                                    <input asp-for="DeparturePort" class="form-control" />
                                    <span asp-validation-for="DeparturePort" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DepartureDate" class="control-label">@LanguageService.GetText("DepartureDate") *</label>
                                    <input asp-for="DepartureDate" type="datetime-local" class="form-control" />
                                    <span asp-validation-for="DepartureDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="ArrivalPort" class="control-label">@LanguageService.GetText("ArrivalPort") *</label>
                                    <input asp-for="ArrivalPort" class="form-control" />
                                    <span asp-validation-for="ArrivalPort" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Eta" class="control-label">@LanguageService.GetText("ETA")</label>
                                    <input asp-for="Eta" type="datetime-local" class="form-control" />
                                    <span asp-validation-for="Eta" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DistanceMiles" class="control-label">@LanguageService.GetText("Distance") (@LanguageService.GetText("Miles"))</label>
                                    <input asp-for="DistanceMiles" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="DistanceMiles" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Cargo Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("CargoInformation")</h4>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CargoDescription" class="control-label">@LanguageService.GetText("CargoDescription")</label>
                                    <textarea asp-for="CargoDescription" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="CargoDescription" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="CargoQuantity" class="control-label">@LanguageService.GetText("CargoQuantity")</label>
                                    <input asp-for="CargoQuantity" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="CargoQuantity" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="CargoUnit" class="control-label">@LanguageService.GetText("CargoUnit")</label>
                                    <input asp-for="CargoUnit" class="form-control" placeholder="MT, CBM, etc." />
                                    <span asp-validation-for="CargoUnit" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="CharterPartyRef" class="control-label">@LanguageService.GetText("CharterPartyRef")</label>
                                    <input asp-for="CharterPartyRef" class="form-control" />
                                    <span asp-validation-for="CharterPartyRef" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label asp-for="Remarks" class="control-label">@LanguageService.GetText("Remarks")</label>
                                    <textarea asp-for="Remarks" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Remarks" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Calculate ETA based on departure date and average speed
            $('#DepartureDate, #DistanceMiles').on('change', function() {
                calculateEta();
            });
            
            function calculateEta() {
                var departureDate = $('#DepartureDate').val();
                var distance = $('#DistanceMiles').val();
                
                if (departureDate && distance) {
                    var avgSpeed = 14; // Average speed in knots
                    var hours = distance / avgSpeed;
                    var departure = new Date(departureDate);
                    departure.setHours(departure.getHours() + hours);
                    
                    // Format datetime for input
                    var eta = departure.toISOString().slice(0, 16);
                    $('#Eta').val(eta);
                }
            }
            
        });
    </script>
}