@model SMS_Maritime_Web.ViewModels.VesselViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("EditVessel");
    var vesselTypes = ViewData["VesselTypes"] as List<SMS_Maritime_Web.Models.VesselType> ?? new List<SMS_Maritime_Web.Models.VesselType>();
    var vesselStatuses = ViewData["VesselStatuses"] as List<SMS_Maritime_Web.Models.VesselStatus> ?? new List<SMS_Maritime_Web.Models.VesselStatus>();
    var vesselClasses = ViewData["VesselClasses"] as List<SMS_Maritime_Web.Models.VesselClass> ?? new List<SMS_Maritime_Web.Models.VesselClass>();
    var flagStates = ViewData["FlagStates"] as List<SMS_Maritime_Web.Models.FlagState> ?? new List<SMS_Maritime_Web.Models.FlagState>();
    var fleets = ViewData["Fleets"] as List<SMS_Maritime_Web.Models.Fleet> ?? new List<SMS_Maritime_Web.Models.Fleet>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ship"></i> @LanguageService.GetText("EditVessel")
                    </h3>
                </div>
                <form asp-action="Edit" method="post">
                    <input type="hidden" asp-for="Id" />
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Navigation Tabs -->
                        <ul class="nav nav-tabs" id="vesselTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="basic-tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">
                                    @LanguageService.GetText("BasicInformation")
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="technical-tab" href="#technical" role="tab" aria-controls="technical" aria-selected="false">
                                    @LanguageService.GetText("TechnicalSpecifications")
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="operational-tab" href="#operational" role="tab" aria-controls="operational" aria-selected="false">
                                    @LanguageService.GetText("OperationalInfo")
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="management-tab" href="#management" role="tab" aria-controls="management" aria-selected="false">
                                    @LanguageService.GetText("Management")
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="environmental-tab" href="#environmental" role="tab" aria-controls="environmental" aria-selected="false">
                                    @LanguageService.GetText("Environmental")
                                </a>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-3">
                            <!-- Basic Information Tab -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="VesselCode" class="control-label">@LanguageService.GetText("VesselCode") *</label>
                                            <input asp-for="VesselCode" class="form-control" />
                                            <span asp-validation-for="VesselCode" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="VesselName" class="control-label">@LanguageService.GetText("VesselName") *</label>
                                            <input asp-for="VesselName" class="form-control" />
                                            <span asp-validation-for="VesselName" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="ImoNumber" class="control-label">@LanguageService.GetText("IMONumber") *</label>
                                            <input asp-for="ImoNumber" class="form-control" />
                                            <span asp-validation-for="ImoNumber" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="MmsiNumber" class="control-label">@LanguageService.GetText("MMSINumber")</label>
                                            <input asp-for="MmsiNumber" class="form-control" />
                                            <span asp-validation-for="MmsiNumber" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="CallSign" class="control-label">@LanguageService.GetText("CallSign")</label>
                                            <input asp-for="CallSign" class="form-control" />
                                            <span asp-validation-for="CallSign" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="VesselTypeId" class="control-label">@LanguageService.GetText("VesselType")</label>
                                            <select asp-for="VesselTypeId" class="form-control">
                                                <option value="">-- @LanguageService.GetText("Select") --</option>
                                                @foreach (var type in vesselTypes)
                                                {
                                                    <option value="@type.Id">@type.Name</option>
                                                }
                                            </select>
                                            <span asp-validation-for="VesselTypeId" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="VesselType" class="control-label">@LanguageService.GetText("VesselTypeText")</label>
                                            <input asp-for="VesselType" class="form-control" />
                                            <span asp-validation-for="VesselType" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="VesselClassId" class="control-label">@LanguageService.GetText("VesselClass")</label>
                                            <select asp-for="VesselClassId" class="form-control">
                                                <option value="">-- @LanguageService.GetText("Select") --</option>
                                                @foreach (var vesselClass in vesselClasses)
                                                {
                                                    <option value="@vesselClass.Id">@vesselClass.Name</option>
                                                }
                                            </select>
                                            <span asp-validation-for="VesselClassId" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="FlagStateId" class="control-label">@LanguageService.GetText("FlagState")</label>
                                            <select asp-for="FlagStateId" class="form-control select2-flags">
                                                <option value="">-- @LanguageService.GetText("Select") --</option>
                                                @foreach (var flag in flagStates)
                                                {
                                                    <option value="@flag.Id" data-flag="@flag.CountryCode">@flag.CountryName</option>
                                                }
                                            </select>
                                            <span asp-validation-for="FlagStateId" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="Flag" class="control-label">@LanguageService.GetText("FlagText")</label>
                                            <input asp-for="Flag" class="form-control" />
                                            <span asp-validation-for="Flag" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="PortOfRegistry" class="control-label">@LanguageService.GetText("PortOfRegistry")</label>
                                            <input asp-for="PortOfRegistry" class="form-control" />
                                            <span asp-validation-for="PortOfRegistry" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="ClassSociety" class="control-label">@LanguageService.GetText("ClassSociety")</label>
                                            <input asp-for="ClassSociety" class="form-control" />
                                            <span asp-validation-for="ClassSociety" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label asp-for="FormerNames" class="control-label">@LanguageService.GetText("FormerNames")</label>
                                            <textarea asp-for="FormerNames" class="form-control" rows="2"></textarea>
                                            <span asp-validation-for="FormerNames" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr />
                                <h5>@LanguageService.GetText("BuildInformation")</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="BuilderName" class="control-label">@LanguageService.GetText("BuilderName")</label>
                                            <input asp-for="BuilderName" class="form-control" />
                                            <span asp-validation-for="BuilderName" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="BuilderYard" class="control-label">@LanguageService.GetText("BuilderYard")</label>
                                            <input asp-for="BuilderYard" class="form-control" />
                                            <span asp-validation-for="BuilderYard" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label asp-for="BuildYear" class="control-label">@LanguageService.GetText("BuildYear")</label>
                                            <input asp-for="BuildYear" type="number" class="form-control" min="1900" max="2100" />
                                            <span asp-validation-for="BuildYear" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label asp-for="DeliveryDate" class="control-label">@LanguageService.GetText("DeliveryDate")</label>
                                            <input asp-for="DeliveryDate" type="date" class="form-control" />
                                            <span asp-validation-for="DeliveryDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label asp-for="HullNumber" class="control-label">@LanguageService.GetText("HullNumber")</label>
                                            <input asp-for="HullNumber" class="form-control" />
                                            <span asp-validation-for="HullNumber" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Technical Specifications Tab -->
                            <div class="tab-pane fade" id="technical" role="tabpanel" aria-labelledby="technical-tab">
                                <h5>@LanguageService.GetText("Dimensions")</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="LengthOverall" class="control-label">@LanguageService.GetText("LengthOverall") (m)</label>
                                            <input asp-for="LengthOverall" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="LengthOverall" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="BreadthMoulded" class="control-label">@LanguageService.GetText("BreadthMoulded") (m)</label>
                                            <input asp-for="BreadthMoulded" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="BreadthMoulded" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="Beam" class="control-label">@LanguageService.GetText("Beam") (m)</label>
                                            <input asp-for="Beam" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="Beam" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="DepthMoulded" class="control-label">@LanguageService.GetText("DepthMoulded") (m)</label>
                                            <input asp-for="DepthMoulded" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="DepthMoulded" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="DraftSummer" class="control-label">@LanguageService.GetText("DraftSummer") (m)</label>
                                            <input asp-for="DraftSummer" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="DraftSummer" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="Draft" class="control-label">@LanguageService.GetText("Draft") (m)</label>
                                            <input asp-for="Draft" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="Draft" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr />
                                <h5>@LanguageService.GetText("TonnageAndCapacity")</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="GrossTonnage" class="control-label">@LanguageService.GetText("GrossTonnage")</label>
                                            <input asp-for="GrossTonnage" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="GrossTonnage" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="NetTonnage" class="control-label">@LanguageService.GetText("NetTonnage")</label>
                                            <input asp-for="NetTonnage" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="NetTonnage" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="DeadweightSummer" class="control-label">@LanguageService.GetText("DeadweightSummer") (t)</label>
                                            <input asp-for="DeadweightSummer" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="DeadweightSummer" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="Deadweight" class="control-label">@LanguageService.GetText("Deadweight") (t)</label>
                                            <input asp-for="Deadweight" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="Deadweight" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="CargoCapacityGrain" class="control-label">@LanguageService.GetText("CargoCapacityGrain") (m³)</label>
                                            <input asp-for="CargoCapacityGrain" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="CargoCapacityGrain" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="CargoCapacityBale" class="control-label">@LanguageService.GetText("CargoCapacityBale") (m³)</label>
                                            <input asp-for="CargoCapacityBale" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="CargoCapacityBale" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="CargoHolds" class="control-label">@LanguageService.GetText("CargoHolds")</label>
                                            <input asp-for="CargoHolds" type="number" class="form-control" />
                                            <span asp-validation-for="CargoHolds" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="CargoHatches" class="control-label">@LanguageService.GetText("CargoHatches")</label>
                                            <input asp-for="CargoHatches" type="number" class="form-control" />
                                            <span asp-validation-for="CargoHatches" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="TeuCapacity" class="control-label">@LanguageService.GetText("TEUCapacity")</label>
                                            <input asp-for="TeuCapacity" type="number" class="form-control" />
                                            <span asp-validation-for="TeuCapacity" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="PassengersCapacity" class="control-label">@LanguageService.GetText("PassengersCapacity")</label>
                                            <input asp-for="PassengersCapacity" type="number" class="form-control" />
                                            <span asp-validation-for="PassengersCapacity" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr />
                                <h5>@LanguageService.GetText("EngineInformation")</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="MainEngineMaker" class="control-label">@LanguageService.GetText("MainEngineMaker")</label>
                                            <input asp-for="MainEngineMaker" class="form-control" />
                                            <span asp-validation-for="MainEngineMaker" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="MainEngineModel" class="control-label">@LanguageService.GetText("MainEngineModel")</label>
                                            <input asp-for="MainEngineModel" class="form-control" />
                                            <span asp-validation-for="MainEngineModel" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="MainEnginePowerKw" class="control-label">@LanguageService.GetText("MainEnginePower") (kW)</label>
                                            <input asp-for="MainEnginePowerKw" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="MainEnginePowerKw" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="EngineType" class="control-label">@LanguageService.GetText("EngineType")</label>
                                            <input asp-for="EngineType" class="form-control" />
                                            <span asp-validation-for="EngineType" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="EnginePower" class="control-label">@LanguageService.GetText("EnginePower")</label>
                                            <input asp-for="EnginePower" class="form-control" />
                                            <span asp-validation-for="EnginePower" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="SpeedService" class="control-label">@LanguageService.GetText("ServiceSpeed") (kn)</label>
                                            <input asp-for="SpeedService" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="SpeedService" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="SpeedMaximum" class="control-label">@LanguageService.GetText("MaximumSpeed") (kn)</label>
                                            <input asp-for="SpeedMaximum" type="number" step="0.01" class="form-control" />
                                            <span asp-validation-for="SpeedMaximum" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Operational Information Tab -->
                            <div class="tab-pane fade" id="operational" role="tabpanel" aria-labelledby="operational-tab">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="VesselStatusId" class="control-label">@LanguageService.GetText("VesselStatus")</label>
                                            <select asp-for="VesselStatusId" class="form-control">
                                                <option value="">-- @LanguageService.GetText("Select") --</option>
                                                @foreach (var status in vesselStatuses)
                                                {
                                                    <option value="@status.Id">@status.Name</option>
                                                }
                                            </select>
                                            <span asp-validation-for="VesselStatusId" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="Status" class="control-label">@LanguageService.GetText("StatusText")</label>
                                            <input asp-for="Status" class="form-control" />
                                            <span asp-validation-for="Status" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="FleetId" class="control-label">@LanguageService.GetText("Fleet")</label>
                                            <select asp-for="FleetId" class="form-control">
                                                <option value="">-- @LanguageService.GetText("Select") --</option>
                                                @foreach (var fleet in fleets)
                                                {
                                                    <option value="@fleet.Id">@fleet.FleetName</option>
                                                }
                                            </select>
                                            <span asp-validation-for="FleetId" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="CurrentLocation" class="control-label">@LanguageService.GetText("CurrentLocation")</label>
                                            <input asp-for="CurrentLocation" class="form-control" />
                                            <span asp-validation-for="CurrentLocation" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="NextPort" class="control-label">@LanguageService.GetText("NextPort")</label>
                                            <input asp-for="NextPort" class="form-control" />
                                            <span asp-validation-for="NextPort" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="Eta" class="control-label">@LanguageService.GetText("ETA")</label>
                                            <input asp-for="Eta" type="datetime-local" class="form-control" />
                                            <span asp-validation-for="Eta" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="LastDrydockDate" class="control-label">@LanguageService.GetText("LastDrydockDate")</label>
                                            <input asp-for="LastDrydockDate" type="date" class="form-control" />
                                            <span asp-validation-for="LastDrydockDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label asp-for="NextDrydockDate" class="control-label">@LanguageService.GetText("NextDrydockDate")</label>
                                            <input asp-for="NextDrydockDate" type="date" class="form-control" />
                                            <span asp-validation-for="NextDrydockDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr />
                                <h5>@LanguageService.GetText("TradingInformation")</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="TradingArea" class="control-label">@LanguageService.GetText("TradingArea")</label>
                                            <input asp-for="TradingArea" class="form-control" />
                                            <span asp-validation-for="TradingArea" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="TradeType" class="control-label">@LanguageService.GetText("TradeType")</label>
                                            <input asp-for="TradeType" class="form-control" />
                                            <span asp-validation-for="TradeType" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr />
                                <h5>@LanguageService.GetText("StatusFlags")</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                            <label asp-for="IsActive" class="form-check-label">
                                                @LanguageService.GetText("IsActive")
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input asp-for="IsOwned" class="form-check-input" type="checkbox" />
                                            <label asp-for="IsOwned" class="form-check-label">
                                                @LanguageService.GetText("IsOwned")
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input asp-for="IsInFleet" class="form-check-input" type="checkbox" />
                                            <label asp-for="IsInFleet" class="form-check-label">
                                                @LanguageService.GetText("IsInFleet")
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Management Tab -->
                            <div class="tab-pane fade" id="management" role="tabpanel" aria-labelledby="management-tab">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="TechnicalManagerCompany" class="control-label">@LanguageService.GetText("TechnicalManager")</label>
                                            <input asp-for="TechnicalManagerCompany" class="form-control" />
                                            <span asp-validation-for="TechnicalManagerCompany" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="CommercialManagerCompany" class="control-label">@LanguageService.GetText("CommercialManager")</label>
                                            <input asp-for="CommercialManagerCompany" class="form-control" />
                                            <span asp-validation-for="CommercialManagerCompany" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label asp-for="CrewManagerCompany" class="control-label">@LanguageService.GetText("CrewManager")</label>
                                            <input asp-for="CrewManagerCompany" class="form-control" />
                                            <span asp-validation-for="CrewManagerCompany" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr />
                                <h5>@LanguageService.GetText("Communication")</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="SatellitePhone" class="control-label">@LanguageService.GetText("SatellitePhone")</label>
                                            <input asp-for="SatellitePhone" class="form-control" />
                                            <span asp-validation-for="SatellitePhone" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label asp-for="SatelliteEmail" class="control-label">@LanguageService.GetText("SatelliteEmail")</label>
                                            <input asp-for="SatelliteEmail" type="email" class="form-control" />
                                            <span asp-validation-for="SatelliteEmail" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Environmental Tab -->
                            <div class="tab-pane fade" id="environmental" role="tabpanel" aria-labelledby="environmental-tab">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input asp-for="EcoDesign" class="form-check-input" type="checkbox" />
                                            <label asp-for="EcoDesign" class="form-check-label">
                                                @LanguageService.GetText("EcoDesign")
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input asp-for="ScrubberFitted" class="form-check-input" type="checkbox" />
                                            <label asp-for="ScrubberFitted" class="form-check-label">
                                                @LanguageService.GetText("ScrubberFitted")
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input asp-for="BallastWaterTreatment" class="form-check-input" type="checkbox" />
                                            <label asp-for="BallastWaterTreatment" class="form-check-label">
                                                @LanguageService.GetText("BallastWaterTreatment")
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="~/js/vessel-tabs.js" asp-append-version="true"></script>
    
    <style>
        /* Hide all tab panes by default */
        .tab-pane {
            display: none;
        }
        /* Show active tab pane */
        .tab-pane.show.active {
            display: block;
        }
    </style>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for flag selection with custom template
            $('.select2-flags').select2({
                templateResult: formatFlag,
                templateSelection: formatFlag,
                escapeMarkup: function(m) { return m; }
            });
            
            function formatFlag(state) {
                if (!state.id) {
                    return state.text;
                }
                var flagCode = $(state.element).data('flag');
                if (flagCode) {
                    var $state = $(
                        '<span><span class="flag-icon flag-icon-' + flagCode.toLowerCase() + '"></span> ' + state.text + '</span>'
                    );
                    return $state;
                }
                return state.text;
            }
        });
    </script>
}
