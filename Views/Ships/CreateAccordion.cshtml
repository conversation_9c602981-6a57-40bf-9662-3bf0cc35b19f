@model SMS_Maritime_Web.ViewModels.VesselViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CreateVessel");
    var vesselTypes = ViewData["VesselTypes"] as List<SMS_Maritime_Web.Models.VesselType> ?? new List<SMS_Maritime_Web.Models.VesselType>();
    var vesselStatuses = ViewData["VesselStatuses"] as List<SMS_Maritime_Web.Models.VesselStatus> ?? new List<SMS_Maritime_Web.Models.VesselStatus>();
    var vesselClasses = ViewData["VesselClasses"] as List<SMS_Maritime_Web.Models.VesselClass> ?? new List<SMS_Maritime_Web.Models.VesselClass>();
    var flagStates = ViewData["FlagStates"] as List<SMS_Maritime_Web.Models.FlagState> ?? new List<SMS_Maritime_Web.Models.FlagState>();
    var fleets = ViewData["Fleets"] as List<SMS_Maritime_Web.Models.Fleet> ?? new List<SMS_Maritime_Web.Models.Fleet>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ship"></i> @LanguageService.GetText("CreateVessel")
                    </h3>
                </div>
                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="accordion" id="vesselAccordion">
                            <!-- Basic Information -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingBasic">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBasic" aria-expanded="true" aria-controls="collapseBasic">
                                        <i class="fas fa-info-circle me-2"></i> @LanguageService.GetText("BasicInformation")
                                    </button>
                                </h2>
                                <div id="collapseBasic" class="accordion-collapse collapse show" aria-labelledby="headingBasic" data-bs-parent="#vesselAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="VesselCode" class="control-label">@LanguageService.GetText("VesselCode") *</label>
                                                    <input asp-for="VesselCode" class="form-control" />
                                                    <span asp-validation-for="VesselCode" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="VesselName" class="control-label">@LanguageService.GetText("VesselName") *</label>
                                                    <input asp-for="VesselName" class="form-control" />
                                                    <span asp-validation-for="VesselName" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="ImoNumber" class="control-label">@LanguageService.GetText("IMONumber") *</label>
                                                    <input asp-for="ImoNumber" class="form-control" />
                                                    <span asp-validation-for="ImoNumber" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="MmsiNumber" class="control-label">@LanguageService.GetText("MMSINumber")</label>
                                                    <input asp-for="MmsiNumber" class="form-control" />
                                                    <span asp-validation-for="MmsiNumber" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="CallSign" class="control-label">@LanguageService.GetText("CallSign")</label>
                                                    <input asp-for="CallSign" class="form-control" />
                                                    <span asp-validation-for="CallSign" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="VesselTypeId" class="control-label">@LanguageService.GetText("VesselType")</label>
                                                    <select asp-for="VesselTypeId" class="form-control">
                                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                                        @foreach (var type in vesselTypes)
                                                        {
                                                            <option value="@type.Id">@type.Name</option>
                                                        }
                                                    </select>
                                                    <span asp-validation-for="VesselTypeId" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="VesselType" class="control-label">@LanguageService.GetText("VesselTypeText")</label>
                                                    <input asp-for="VesselType" class="form-control" />
                                                    <span asp-validation-for="VesselType" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="VesselClassId" class="control-label">@LanguageService.GetText("VesselClass")</label>
                                                    <select asp-for="VesselClassId" class="form-control">
                                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                                        @foreach (var vesselClass in vesselClasses)
                                                        {
                                                            <option value="@vesselClass.Id">@vesselClass.Name</option>
                                                        }
                                                    </select>
                                                    <span asp-validation-for="VesselClassId" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Technical Specifications -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingTechnical">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTechnical" aria-expanded="false" aria-controls="collapseTechnical">
                                        <i class="fas fa-cogs me-2"></i> @LanguageService.GetText("TechnicalSpecifications")
                                    </button>
                                </h2>
                                <div id="collapseTechnical" class="accordion-collapse collapse" aria-labelledby="headingTechnical" data-bs-parent="#vesselAccordion">
                                    <div class="accordion-body">
                                        <h5>@LanguageService.GetText("Dimensions")</h5>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="LengthOverall" class="control-label">@LanguageService.GetText("LengthOverall") (m)</label>
                                                    <input asp-for="LengthOverall" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="LengthOverall" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="BreadthMoulded" class="control-label">@LanguageService.GetText("BreadthMoulded") (m)</label>
                                                    <input asp-for="BreadthMoulded" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="BreadthMoulded" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="Beam" class="control-label">@LanguageService.GetText("Beam") (m)</label>
                                                    <input asp-for="Beam" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="Beam" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="DepthMoulded" class="control-label">@LanguageService.GetText("DepthMoulded") (m)</label>
                                                    <input asp-for="DepthMoulded" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="DepthMoulded" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <hr />
                                        <h5>@LanguageService.GetText("TonnageAndCapacity")</h5>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="GrossTonnage" class="control-label">@LanguageService.GetText("GrossTonnage")</label>
                                                    <input asp-for="GrossTonnage" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="GrossTonnage" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="NetTonnage" class="control-label">@LanguageService.GetText("NetTonnage")</label>
                                                    <input asp-for="NetTonnage" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="NetTonnage" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="DeadweightSummer" class="control-label">@LanguageService.GetText("DeadweightSummer") (t)</label>
                                                    <input asp-for="DeadweightSummer" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="DeadweightSummer" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="Deadweight" class="control-label">@LanguageService.GetText("Deadweight") (t)</label>
                                                    <input asp-for="Deadweight" type="number" step="0.01" class="form-control" />
                                                    <span asp-validation-for="Deadweight" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Operational Information -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingOperational">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOperational" aria-expanded="false" aria-controls="collapseOperational">
                                        <i class="fas fa-anchor me-2"></i> @LanguageService.GetText("OperationalInfo")
                                    </button>
                                </h2>
                                <div id="collapseOperational" class="accordion-collapse collapse" aria-labelledby="headingOperational" data-bs-parent="#vesselAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="VesselStatusId" class="control-label">@LanguageService.GetText("VesselStatus")</label>
                                                    <select asp-for="VesselStatusId" class="form-control">
                                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                                        @foreach (var status in vesselStatuses)
                                                        {
                                                            <option value="@status.Id">@status.Name</option>
                                                        }
                                                    </select>
                                                    <span asp-validation-for="VesselStatusId" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="Status" class="control-label">@LanguageService.GetText("StatusText")</label>
                                                    <input asp-for="Status" class="form-control" />
                                                    <span asp-validation-for="Status" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label asp-for="FleetId" class="control-label">@LanguageService.GetText("Fleet")</label>
                                                    <select asp-for="FleetId" class="form-control">
                                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                                        @foreach (var fleet in fleets)
                                                        {
                                                            <option value="@fleet.Id">@fleet.FleetName</option>
                                                        }
                                                    </select>
                                                    <span asp-validation-for="FleetId" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Management -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingManagement">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseManagement" aria-expanded="false" aria-controls="collapseManagement">
                                        <i class="fas fa-building me-2"></i> @LanguageService.GetText("Management")
                                    </button>
                                </h2>
                                <div id="collapseManagement" class="accordion-collapse collapse" aria-labelledby="headingManagement" data-bs-parent="#vesselAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label asp-for="TechnicalManagerCompany" class="control-label">@LanguageService.GetText("TechnicalManager")</label>
                                                    <input asp-for="TechnicalManagerCompany" class="form-control" />
                                                    <span asp-validation-for="TechnicalManagerCompany" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label asp-for="CommercialManagerCompany" class="control-label">@LanguageService.GetText("CommercialManager")</label>
                                                    <input asp-for="CommercialManagerCompany" class="form-control" />
                                                    <span asp-validation-for="CommercialManagerCompany" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label asp-for="CrewManagerCompany" class="control-label">@LanguageService.GetText("CrewManager")</label>
                                                    <input asp-for="CrewManagerCompany" class="form-control" />
                                                    <span asp-validation-for="CrewManagerCompany" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Environmental -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingEnvironmental">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseEnvironmental" aria-expanded="false" aria-controls="collapseEnvironmental">
                                        <i class="fas fa-leaf me-2"></i> @LanguageService.GetText("Environmental")
                                    </button>
                                </h2>
                                <div id="collapseEnvironmental" class="accordion-collapse collapse" aria-labelledby="headingEnvironmental" data-bs-parent="#vesselAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input asp-for="EcoDesign" class="form-check-input" type="checkbox" />
                                                    <label asp-for="EcoDesign" class="form-check-label">
                                                        @LanguageService.GetText("EcoDesign")
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input asp-for="ScrubberFitted" class="form-check-input" type="checkbox" />
                                                    <label asp-for="ScrubberFitted" class="form-check-label">
                                                        @LanguageService.GetText("ScrubberFitted")
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input asp-for="BallastWaterTreatment" class="form-check-input" type="checkbox" />
                                                    <label asp-for="BallastWaterTreatment" class="form-check-label">
                                                        @LanguageService.GetText("BallastWaterTreatment")
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for flag selection with custom template
            $('.select2-flags').select2({
                templateResult: formatFlag,
                templateSelection: formatFlag,
                escapeMarkup: function(m) { return m; }
            });
            
            function formatFlag(state) {
                if (!state.id) {
                    return state.text;
                }
                var flagCode = $(state.element).data('flag');
                if (flagCode) {
                    var $state = $(
                        '<span><span class="flag-icon flag-icon-' + flagCode.toLowerCase() + '"></span> ' + state.text + '</span>'
                    );
                    return $state;
                }
                return state.text;
            }
        });
    </script>
}