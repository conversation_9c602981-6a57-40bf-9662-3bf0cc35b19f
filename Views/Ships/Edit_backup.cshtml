@model SMS_Maritime_Web.ViewModels.VesselViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("EditVessel");
    var vesselTypes = ViewData["VesselTypes"] as List<SMS_Maritime_Web.Models.VesselType> ?? new List<SMS_Maritime_Web.Models.VesselType>();
    var vesselStatuses = ViewData["VesselStatuses"] as List<SMS_Maritime_Web.Models.VesselStatus> ?? new List<SMS_Maritime_Web.Models.VesselStatus>();
    var vesselClasses = ViewData["VesselClasses"] as List<SMS_Maritime_Web.Models.VesselClass> ?? new List<SMS_Maritime_Web.Models.VesselClass>();
    var flagStates = ViewData["FlagStates"] as List<SMS_Maritime_Web.Models.FlagState> ?? new List<SMS_Maritime_Web.Models.FlagState>();
    var fleets = ViewData["Fleets"] as List<SMS_Maritime_Web.Models.Fleet> ?? new List<SMS_Maritime_Web.Models.Fleet>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ship"></i> @LanguageService.GetText("EditVessel")
                    </h3>
                </div>
                <form asp-action="Edit" method="post">
                    <input type="hidden" asp-for="Id" />
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("BasicInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VesselCode" class="control-label">@LanguageService.GetText("VesselCode") *</label>
                                    <input asp-for="VesselCode" class="form-control" />
                                    <span asp-validation-for="VesselCode" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VesselName" class="control-label">@LanguageService.GetText("VesselName") *</label>
                                    <input asp-for="VesselName" class="form-control" />
                                    <span asp-validation-for="VesselName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="ImoNumber" class="control-label">@LanguageService.GetText("IMONumber") *</label>
                                    <input asp-for="ImoNumber" class="form-control" />
                                    <span asp-validation-for="ImoNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="MmsiNumber" class="control-label">@LanguageService.GetText("MMSINumber")</label>
                                    <input asp-for="MmsiNumber" class="form-control" />
                                    <span asp-validation-for="MmsiNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="CallSign" class="control-label">@LanguageService.GetText("CallSign")</label>
                                    <input asp-for="CallSign" class="form-control" />
                                    <span asp-validation-for="CallSign" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VesselTypeId" class="control-label">@LanguageService.GetText("VesselType")</label>
                                    <select asp-for="VesselTypeId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var type in vesselTypes)
                                        {
                                            <option value="@type.Id">@type.Name</option>
                                        }
                                    </select>
                                    <span asp-validation-for="VesselTypeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VesselStatusId" class="control-label">@LanguageService.GetText("Status")</label>
                                    <select asp-for="VesselStatusId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var status in vesselStatuses)
                                        {
                                            <option value="@status.Id">@status.Name</option>
                                        }
                                    </select>
                                    <span asp-validation-for="VesselStatusId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="VesselClassId" class="control-label">@LanguageService.GetText("VesselClass")</label>
                                    <select asp-for="VesselClassId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var vesselClass in vesselClasses)
                                        {
                                            <option value="@vesselClass.Id">@vesselClass.Name</option>
                                        }
                                    </select>
                                    <span asp-validation-for="VesselClassId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Registration Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("RegistrationInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="FlagStateId" class="control-label">@LanguageService.GetText("Flag")</label>
                                    <select asp-for="FlagStateId" class="form-control select2-flags">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var flag in flagStates)
                                        {
                                            <option value="@flag.Id" data-flag="@flag.CountryCode">@flag.CountryName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="FlagStateId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="PortOfRegistry" class="control-label">@LanguageService.GetText("PortOfRegistry")</label>
                                    <input asp-for="PortOfRegistry" class="form-control" />
                                    <span asp-validation-for="PortOfRegistry" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="BuildYear" class="control-label">@LanguageService.GetText("BuildYear")</label>
                                    <input asp-for="BuildYear" type="number" class="form-control" min="1900" max="2100" />
                                    <span asp-validation-for="BuildYear" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DeliveryDate" class="control-label">@LanguageService.GetText("DeliveryDate")</label>
                                    <input asp-for="DeliveryDate" type="date" class="form-control" />
                                    <span asp-validation-for="DeliveryDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Dimensions -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("Dimensions")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="LengthOverall" class="control-label">@LanguageService.GetText("LengthOverall") (m)</label>
                                    <input asp-for="LengthOverall" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="LengthOverall" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="BreadthMoulded" class="control-label">@LanguageService.GetText("Breadth") (m)</label>
                                    <input asp-for="BreadthMoulded" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="BreadthMoulded" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DepthMoulded" class="control-label">@LanguageService.GetText("Depth") (m)</label>
                                    <input asp-for="DepthMoulded" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="DepthMoulded" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DraftSummer" class="control-label">@LanguageService.GetText("SummerDraft") (m)</label>
                                    <input asp-for="DraftSummer" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="DraftSummer" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tonnage and Capacity -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("TonnageAndCapacity")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="GrossTonnage" class="control-label">@LanguageService.GetText("GrossTonnage")</label>
                                    <input asp-for="GrossTonnage" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="GrossTonnage" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="NetTonnage" class="control-label">@LanguageService.GetText("NetTonnage")</label>
                                    <input asp-for="NetTonnage" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="NetTonnage" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="DeadweightSummer" class="control-label">@LanguageService.GetText("Deadweight") (t)</label>
                                    <input asp-for="DeadweightSummer" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="DeadweightSummer" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="SpeedService" class="control-label">@LanguageService.GetText("ServiceSpeed") (kn)</label>
                                    <input asp-for="SpeedService" type="number" step="0.01" class="form-control" />
                                    <span asp-validation-for="SpeedService" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Status -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        @LanguageService.GetText("IsActive")
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2 for flag selection with custom template
            $('.select2-flags').select2({
                templateResult: formatFlag,
                templateSelection: formatFlag,
                escapeMarkup: function(m) { return m; }
            });
            
            function formatFlag(state) {
                if (!state.id) {
                    return state.text;
                }
                var flagCode = $(state.element).data('flag');
                if (flagCode) {
                    var $state = $(
                        '<span><span class="flag-icon flag-icon-' + flagCode.toLowerCase() + '"></span> ' + state.text + '</span>'
                    );
                    return $state;
                }
                return state.text;
            }
        });
    </script>
}