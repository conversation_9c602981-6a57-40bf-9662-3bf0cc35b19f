@model IEnumerable<SMS_Maritime_Web.Models.Vessel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Ships");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ship"></i> @LanguageService.GetText("Ships")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="vesselsTable">
                            <thead>
                                <tr>
                                    <th>@LanguageService.GetText("VesselCode")</th>
                                    <th>@LanguageService.GetText("VesselName")</th>
                                    <th>@LanguageService.GetText("IMONumber")</th>
                                    <th>@LanguageService.GetText("VesselType")</th>
                                    <th>@LanguageService.GetText("Flag")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("Active")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var vessel in Model)
                                {
                                    <tr>
                                        <td>@vessel.VesselCode</td>
                                        <td>@vessel.VesselName</td>
                                        <td>@vessel.ImoNumber</td>
                                        <td>@vessel.VesselTypeNavigation?.Name</td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(vessel.FlagState?.CountryCode))
                                            {
                                                <span class="flag-icon <EMAIL>()"></span>
                                                @vessel.FlagState.CountryName
                                            }
                                        </td>
                                        <td>
                                            @if (vessel.VesselStatus != null)
                                            {
                                                <span class="badge bg-@(vessel.VesselStatus.Code == "ACTIVE" ? "success" : vessel.VesselStatus.Code == "INACTIVE" ? "danger" : "warning")">
                                                    @vessel.VesselStatus.Name
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            @if (vessel.IsActive)
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("Yes")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">@LanguageService.GetText("No")</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@vessel.Id" class="btn btn-info btn-sm" title="@LanguageService.GetText("Details")">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@vessel.Id" class="btn btn-warning btn-sm" title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete('@vessel.Id', '@vessel.VesselName')" title="@LanguageService.GetText("Delete")">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">@LanguageService.GetText("ConfirmDelete")</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>@LanguageService.GetText("AreYouSureDelete") <strong id="vesselNameToDelete"></strong>?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@LanguageService.GetText("Cancel")</button>
                <form id="deleteForm" asp-action="Delete" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="vesselIdToDelete" name="id" />
                    <button type="submit" class="btn btn-danger">@LanguageService.GetText("Delete")</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#vesselsTable').DataTable({
                "language": {
                    "url": "/lib/datatables/i18n/@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName).json"
                },
                "order": [[1, "asc"]],
                "pageLength": 25,
                "responsive": true
            });
        });

        function confirmDelete(id, name) {
            $('#vesselIdToDelete').val(id);
            $('#vesselNameToDelete').text(name);
            $('#deleteModal').modal('show');
        }
    </script>
}