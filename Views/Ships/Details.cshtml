@model SMS_Maritime_Web.Models.Vessel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("VesselDetails");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ship"></i> @LanguageService.GetText("VesselDetails") - @Model.VesselName
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> @LanguageService.GetText("Edit")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="vesselTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">
                                @LanguageService.GetText("BasicInformation")
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="technical-tab" href="#technical" role="tab" aria-controls="technical" aria-selected="false">
                                @LanguageService.GetText("TechnicalSpecifications")
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="operational-tab" href="#operational" role="tab" aria-controls="operational" aria-selected="false">
                                @LanguageService.GetText("OperationalInfo")
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="management-tab" href="#management" role="tab" aria-controls="management" aria-selected="false">
                                @LanguageService.GetText("Management")
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="environmental-tab" href="#environmental" role="tab" aria-controls="environmental" aria-selected="false">
                                @LanguageService.GetText("Environmental")
                            </a>
                        </li>
                    </ul>
                    
                    <div class="tab-content mt-3">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("VesselCode")</label>
                                        <p class="form-control-static"><strong>@Model.VesselCode</strong></p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("VesselName")</label>
                                        <p class="form-control-static"><strong>@Model.VesselName</strong></p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("IMONumber")</label>
                                        <p class="form-control-static"><strong>@Model.ImoNumber</strong></p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("MMSINumber")</label>
                                        <p class="form-control-static">@(Model.MmsiNumber ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CallSign")</label>
                                        <p class="form-control-static">@(Model.CallSign ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("VesselType")</label>
                                        <p class="form-control-static">@(Model.VesselTypeNavigation?.Name ?? Model.VesselType ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("VesselClass")</label>
                                        <p class="form-control-static">@(Model.VesselClass?.Name ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("ClassSociety")</label>
                                        <p class="form-control-static">@(Model.ClassSociety ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("Flag")</label>
                                        <p class="form-control-static">
                                            @if (Model.FlagState != null)
                                            {
                                                <span class="flag-icon <EMAIL>()"></span>
                                                @Model.FlagState.CountryName
                                            }
                                            else if (!string.IsNullOrEmpty(Model.Flag))
                                            {
                                                @Model.Flag
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("PortOfRegistry")</label>
                                        <p class="form-control-static">@(Model.PortOfRegistry ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("FormerNames")</label>
                                        <p class="form-control-static">@(Model.FormerNames ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("BuildInformation")</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("BuilderName")</label>
                                        <p class="form-control-static">@(Model.BuilderName ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("BuilderYard")</label>
                                        <p class="form-control-static">@(Model.BuilderYard ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("BuildYear")</label>
                                        <p class="form-control-static">@(Model.BuildYear?.ToString() ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("DeliveryDate")</label>
                                        <p class="form-control-static">@(Model.DeliveryDate?.ToString("yyyy-MM-dd") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("HullNumber")</label>
                                        <p class="form-control-static">@(Model.HullNumber ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Technical Specifications Tab -->
                        <div class="tab-pane fade" id="technical" role="tabpanel" aria-labelledby="technical-tab">
                            <h5>@LanguageService.GetText("Dimensions")</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("LengthOverall") (m)</label>
                                        <p class="form-control-static">@(Model.LengthOverall?.ToString("F2") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("BreadthMoulded") (m)</label>
                                        <p class="form-control-static">@(Model.BreadthMoulded?.ToString("F2") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("Beam") (m)</label>
                                        <p class="form-control-static">@(Model.Beam?.ToString("F2") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("DepthMoulded") (m)</label>
                                        <p class="form-control-static">@(Model.DepthMoulded?.ToString("F2") ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("DraftSummer") (m)</label>
                                        <p class="form-control-static">@(Model.DraftSummer?.ToString("F2") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("Draft") (m)</label>
                                        <p class="form-control-static">@(Model.Draft?.ToString("F2") ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("TonnageAndCapacity")</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("GrossTonnage")</label>
                                        <p class="form-control-static">@(Model.GrossTonnage?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("NetTonnage")</label>
                                        <p class="form-control-static">@(Model.NetTonnage?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("DeadweightSummer") (t)</label>
                                        <p class="form-control-static">@(Model.DeadweightSummer?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("Deadweight") (t)</label>
                                        <p class="form-control-static">@(Model.Deadweight?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CargoCapacityGrain") (m³)</label>
                                        <p class="form-control-static">@(Model.CargoCapacityGrain?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CargoCapacityBale") (m³)</label>
                                        <p class="form-control-static">@(Model.CargoCapacityBale?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CargoHolds")</label>
                                        <p class="form-control-static">@(Model.CargoHolds?.ToString() ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CargoHatches")</label>
                                        <p class="form-control-static">@(Model.CargoHatches?.ToString() ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("TEUCapacity")</label>
                                        <p class="form-control-static">@(Model.TeuCapacity?.ToString() ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("PassengersCapacity")</label>
                                        <p class="form-control-static">@(Model.PassengersCapacity?.ToString() ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("EngineInformation")</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("MainEngineMaker")</label>
                                        <p class="form-control-static">@(Model.MainEngineMaker ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("MainEngineModel")</label>
                                        <p class="form-control-static">@(Model.MainEngineModel ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("MainEnginePower") (kW)</label>
                                        <p class="form-control-static">@(Model.MainEnginePowerKw?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("EngineType")</label>
                                        <p class="form-control-static">@(Model.EngineType ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("EnginePower")</label>
                                        <p class="form-control-static">@(Model.EnginePower?.ToString("N0") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("ServiceSpeed") (kn)</label>
                                        <p class="form-control-static">@(Model.SpeedService?.ToString("F1") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("MaximumSpeed") (kn)</label>
                                        <p class="form-control-static">@(Model.SpeedMaximum?.ToString("F1") ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Operational Information Tab -->
                        <div class="tab-pane fade" id="operational" role="tabpanel" aria-labelledby="operational-tab">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("VesselStatus")</label>
                                        <p class="form-control-static">
                                            @if (Model.VesselStatus != null)
                                            {
                                                <span class="badge bg-@(Model.VesselStatus.Code == "ACTIVE" ? "success" : Model.VesselStatus.Code == "INACTIVE" ? "danger" : "warning")">
                                                    @Model.VesselStatus.Name
                                                </span>
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("StatusText")</label>
                                        <p class="form-control-static">@(Model.Status ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("Fleet")</label>
                                        <p class="form-control-static">@(Model.Fleet?.FleetName ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CurrentLocation")</label>
                                        <p class="form-control-static">@(Model.CurrentLocation ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("NextPort")</label>
                                        <p class="form-control-static">@(Model.NextPort ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("ETA")</label>
                                        <p class="form-control-static">@(Model.Eta?.ToString("yyyy-MM-dd HH:mm") ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("LastDrydockDate")</label>
                                        <p class="form-control-static">@(Model.LastDrydockDate?.ToString("yyyy-MM-dd") ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("NextDrydockDate")</label>
                                        <p class="form-control-static">@(Model.NextDrydockDate?.ToString("yyyy-MM-dd") ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("TradingInformation")</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("TradingArea")</label>
                                        <p class="form-control-static">@(Model.TradingArea ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("TradeType")</label>
                                        <p class="form-control-static">@(Model.TradeType ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("StatusFlags")</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("IsActive")</label>
                                        <p class="form-control-static">
                                            @if (Model.IsActive)
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("Yes")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">@LanguageService.GetText("No")</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("IsOwned")</label>
                                        <p class="form-control-static">
                                            @if (Model.IsOwned)
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("Yes")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@LanguageService.GetText("No")</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("IsInFleet")</label>
                                        <p class="form-control-static">
                                            @if (Model.IsInFleet)
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("Yes")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@LanguageService.GetText("No")</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Management Tab -->
                        <div class="tab-pane fade" id="management" role="tabpanel" aria-labelledby="management-tab">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("TechnicalManager")</label>
                                        <p class="form-control-static">@(Model.TechnicalManagerCompany ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CommercialManager")</label>
                                        <p class="form-control-static">@(Model.CommercialManagerCompany ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CrewManager")</label>
                                        <p class="form-control-static">@(Model.CrewManagerCompany ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("Communication")</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("SatellitePhone")</label>
                                        <p class="form-control-static">@(Model.SatellitePhone ?? "-")</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("SatelliteEmail")</label>
                                        <p class="form-control-static">@(Model.SatelliteEmail ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("AuditInformation")</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("CreatedDate")</label>
                                        <p class="form-control-static">@Model.CreatedDate.ToString("yyyy-MM-dd HH:mm")</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("ModifiedDate")</label>
                                        <p class="form-control-static">@(Model.ModifiedDate?.ToString("yyyy-MM-dd HH:mm") ?? "-")</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Environmental Tab -->
                        <div class="tab-pane fade" id="environmental" role="tabpanel" aria-labelledby="environmental-tab">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("EcoDesign")</label>
                                        <p class="form-control-static">
                                            @if (Model.EcoDesign)
                                            {
                                                <span class="badge bg-success"><i class="fas fa-check"></i> @LanguageService.GetText("Yes")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@LanguageService.GetText("No")</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("ScrubberFitted")</label>
                                        <p class="form-control-static">
                                            @if (Model.ScrubberFitted)
                                            {
                                                <span class="badge bg-success"><i class="fas fa-check"></i> @LanguageService.GetText("Yes")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@LanguageService.GetText("No")</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>@LanguageService.GetText("BallastWaterTreatment")</label>
                                        <p class="form-control-static">
                                            @if (Model.BallastWaterTreatment)
                                            {
                                                <span class="badge bg-success"><i class="fas fa-check"></i> @LanguageService.GetText("Yes")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">@LanguageService.GetText("No")</span>
                                            }
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <link href="https://cdn.jsdelivr.net/npm/flag-icon-css@3.5.0/css/flag-icon.min.css" rel="stylesheet">
    <script src="~/js/vessel-tabs.js" asp-append-version="true"></script>
    
    <style>
        /* Style for readonly form display */
        .form-control-static {
            padding-top: 7px;
            padding-bottom: 7px;
            margin-bottom: 0;
            min-height: 34px;
        }
        
        /* Hide all tab panes by default */
        .tab-pane {
            display: none;
        }
        /* Show active tab pane */
        .tab-pane.show.active {
            display: block;
        }
    </style>
}