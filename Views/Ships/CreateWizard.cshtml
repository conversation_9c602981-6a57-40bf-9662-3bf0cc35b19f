@model SMS_Maritime_Web.ViewModels.VesselViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CreateVessel");
    var vesselTypes = ViewData["VesselTypes"] as List<SMS_Maritime_Web.Models.VesselType> ?? new List<SMS_Maritime_Web.Models.VesselType>();
    var vesselStatuses = ViewData["VesselStatuses"] as List<SMS_Maritime_Web.Models.VesselStatus> ?? new List<SMS_Maritime_Web.Models.VesselStatus>();
    var vesselClasses = ViewData["VesselClasses"] as List<SMS_Maritime_Web.Models.VesselClass> ?? new List<SMS_Maritime_Web.Models.VesselClass>();
    var flagStates = ViewData["FlagStates"] as List<SMS_Maritime_Web.Models.FlagState> ?? new List<SMS_Maritime_Web.Models.FlagState>();
    var fleets = ViewData["Fleets"] as List<SMS_Maritime_Web.Models.Fleet> ?? new List<SMS_Maritime_Web.Models.Fleet>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ship"></i> @LanguageService.GetText("CreateVessel")
                    </h3>
                </div>
                <form asp-action="Create" method="post" id="vesselForm">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Progress Bar -->
                        <div class="progress mb-4" style="height: 30px;">
                            <div class="progress-bar" role="progressbar" style="width: 20%;" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                                <span class="step-title">@LanguageService.GetText("BasicInformation")</span>
                            </div>
                        </div>
                        
                        <!-- Step 1: Basic Information -->
                        <div class="step-content" id="step1" style="display: block;">
                            <h4 class="mb-3">@LanguageService.GetText("BasicInformation")</h4>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="VesselCode" class="control-label">@LanguageService.GetText("VesselCode") *</label>
                                        <input asp-for="VesselCode" class="form-control" />
                                        <span asp-validation-for="VesselCode" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="VesselName" class="control-label">@LanguageService.GetText("VesselName") *</label>
                                        <input asp-for="VesselName" class="form-control" />
                                        <span asp-validation-for="VesselName" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="ImoNumber" class="control-label">@LanguageService.GetText("IMONumber") *</label>
                                        <input asp-for="ImoNumber" class="form-control" />
                                        <span asp-validation-for="ImoNumber" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="MmsiNumber" class="control-label">@LanguageService.GetText("MMSINumber")</label>
                                        <input asp-for="MmsiNumber" class="form-control" />
                                        <span asp-validation-for="MmsiNumber" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="CallSign" class="control-label">@LanguageService.GetText("CallSign")</label>
                                        <input asp-for="CallSign" class="form-control" />
                                        <span asp-validation-for="CallSign" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="VesselTypeId" class="control-label">@LanguageService.GetText("VesselType")</label>
                                        <select asp-for="VesselTypeId" class="form-control">
                                            <option value="">-- @LanguageService.GetText("Select") --</option>
                                            @foreach (var type in vesselTypes)
                                            {
                                                <option value="@type.Id">@type.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="VesselTypeId" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="VesselType" class="control-label">@LanguageService.GetText("VesselTypeText")</label>
                                        <input asp-for="VesselType" class="form-control" />
                                        <span asp-validation-for="VesselType" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="VesselClassId" class="control-label">@LanguageService.GetText("VesselClass")</label>
                                        <select asp-for="VesselClassId" class="form-control">
                                            <option value="">-- @LanguageService.GetText("Select") --</option>
                                            @foreach (var vesselClass in vesselClasses)
                                            {
                                                <option value="@vesselClass.Id">@vesselClass.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="VesselClassId" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="FlagStateId" class="control-label">@LanguageService.GetText("FlagState")</label>
                                        <select asp-for="FlagStateId" class="form-control select2-flags">
                                            <option value="">-- @LanguageService.GetText("Select") --</option>
                                            @foreach (var flag in flagStates)
                                            {
                                                <option value="@flag.Id" data-flag="@flag.FlagCode">@flag.CountryName</option>
                                            }
                                        </select>
                                        <span asp-validation-for="FlagStateId" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="Flag" class="control-label">@LanguageService.GetText("FlagText")</label>
                                        <input asp-for="Flag" class="form-control" />
                                        <span asp-validation-for="Flag" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="PortOfRegistry" class="control-label">@LanguageService.GetText("PortOfRegistry")</label>
                                        <input asp-for="PortOfRegistry" class="form-control" />
                                        <span asp-validation-for="PortOfRegistry" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="ClassSociety" class="control-label">@LanguageService.GetText("ClassSociety")</label>
                                        <input asp-for="ClassSociety" class="form-control" />
                                        <span asp-validation-for="ClassSociety" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 2: Technical Specifications -->
                        <div class="step-content" id="step2" style="display: none;">
                            <h4 class="mb-3">@LanguageService.GetText("TechnicalSpecifications")</h4>
                            <h5>@LanguageService.GetText("Dimensions")</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="LengthOverall" class="control-label">@LanguageService.GetText("LengthOverall") (m)</label>
                                        <input asp-for="LengthOverall" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="LengthOverall" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="BreadthMoulded" class="control-label">@LanguageService.GetText("BreadthMoulded") (m)</label>
                                        <input asp-for="BreadthMoulded" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="BreadthMoulded" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="Beam" class="control-label">@LanguageService.GetText("Beam") (m)</label>
                                        <input asp-for="Beam" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="Beam" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="DepthMoulded" class="control-label">@LanguageService.GetText("DepthMoulded") (m)</label>
                                        <input asp-for="DepthMoulded" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="DepthMoulded" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <hr />
                            <h5>@LanguageService.GetText("TonnageAndCapacity")</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="GrossTonnage" class="control-label">@LanguageService.GetText("GrossTonnage")</label>
                                        <input asp-for="GrossTonnage" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="GrossTonnage" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="NetTonnage" class="control-label">@LanguageService.GetText("NetTonnage")</label>
                                        <input asp-for="NetTonnage" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="NetTonnage" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="DeadweightSummer" class="control-label">@LanguageService.GetText("DeadweightSummer") (t)</label>
                                        <input asp-for="DeadweightSummer" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="DeadweightSummer" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="Deadweight" class="control-label">@LanguageService.GetText("Deadweight") (t)</label>
                                        <input asp-for="Deadweight" type="number" step="0.01" class="form-control" />
                                        <span asp-validation-for="Deadweight" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 3: Operational Information -->
                        <div class="step-content" id="step3" style="display: none;">
                            <h4 class="mb-3">@LanguageService.GetText("OperationalInfo")</h4>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="VesselStatusId" class="control-label">@LanguageService.GetText("VesselStatus")</label>
                                        <select asp-for="VesselStatusId" class="form-control">
                                            <option value="">-- @LanguageService.GetText("Select") --</option>
                                            @foreach (var status in vesselStatuses)
                                            {
                                                <option value="@status.Id">@status.Name</option>
                                            }
                                        </select>
                                        <span asp-validation-for="VesselStatusId" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="Status" class="control-label">@LanguageService.GetText("StatusText")</label>
                                        <input asp-for="Status" class="form-control" />
                                        <span asp-validation-for="Status" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label asp-for="FleetId" class="control-label">@LanguageService.GetText("Fleet")</label>
                                        <select asp-for="FleetId" class="form-control">
                                            <option value="">-- @LanguageService.GetText("Select") --</option>
                                            @foreach (var fleet in fleets)
                                            {
                                                <option value="@fleet.Id">@fleet.FleetName</option>
                                            }
                                        </select>
                                        <span asp-validation-for="FleetId" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 4: Management -->
                        <div class="step-content" id="step4" style="display: none;">
                            <h4 class="mb-3">@LanguageService.GetText("Management")</h4>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label asp-for="TechnicalManagerCompany" class="control-label">@LanguageService.GetText("TechnicalManager")</label>
                                        <input asp-for="TechnicalManagerCompany" class="form-control" />
                                        <span asp-validation-for="TechnicalManagerCompany" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label asp-for="CommercialManagerCompany" class="control-label">@LanguageService.GetText("CommercialManager")</label>
                                        <input asp-for="CommercialManagerCompany" class="form-control" />
                                        <span asp-validation-for="CommercialManagerCompany" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label asp-for="CrewManagerCompany" class="control-label">@LanguageService.GetText("CrewManager")</label>
                                        <input asp-for="CrewManagerCompany" class="form-control" />
                                        <span asp-validation-for="CrewManagerCompany" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 5: Environmental -->
                        <div class="step-content" id="step5" style="display: none;">
                            <h4 class="mb-3">@LanguageService.GetText("Environmental")</h4>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input asp-for="EcoDesign" class="form-check-input" type="checkbox" />
                                        <label asp-for="EcoDesign" class="form-check-label">
                                            @LanguageService.GetText("EcoDesign")
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input asp-for="ScrubberFitted" class="form-check-input" type="checkbox" />
                                        <label asp-for="ScrubberFitted" class="form-check-label">
                                            @LanguageService.GetText("ScrubberFitted")
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input asp-for="BallastWaterTreatment" class="form-check-input" type="checkbox" />
                                        <label asp-for="BallastWaterTreatment" class="form-check-label">
                                            @LanguageService.GetText("BallastWaterTreatment")
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeStep(-1)" style="display: none;">
                                    <i class="fas fa-arrow-left"></i> @LanguageService.GetText("Previous")
                                </button>
                            </div>
                            <div>
                                <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeStep(1)">
                                    @LanguageService.GetText("Next") <i class="fas fa-arrow-right"></i>
                                </button>
                                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                                    <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                                </button>
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <style>
        .progress-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: width 0.3s ease;
        }
        .step-content {
            min-height: 300px;
        }
    </style>
    
    <script>
        var currentStep = 1;
        var totalSteps = 5;
        var stepTitles = [
            '@LanguageService.GetText("BasicInformation")',
            '@LanguageService.GetText("TechnicalSpecifications")',
            '@LanguageService.GetText("OperationalInfo")',
            '@LanguageService.GetText("Management")',
            '@LanguageService.GetText("Environmental")'
        ];
        
        $(document).ready(function() {
            // Initialize Select2 for flag selection with custom template
            $('.select2-flags').select2({
                templateResult: formatFlag,
                templateSelection: formatFlag,
                escapeMarkup: function(m) { return m; }
            });
            
            function formatFlag(state) {
                if (!state.id) {
                    return state.text;
                }
                var flagCode = $(state.element).data('flag');
                if (flagCode) {
                    var $state = $(
                        '<span><span class="flag-icon flag-icon-' + flagCode.toLowerCase() + '"></span> ' + state.text + '</span>'
                    );
                    return $state;
                }
                return state.text;
            }
        });
        
        function changeStep(direction) {
            // Hide current step
            $('#step' + currentStep).hide();
            
            // Update step number
            currentStep += direction;
            
            // Show new step
            $('#step' + currentStep).show();
            
            // Update progress bar
            var progressPercent = (currentStep / totalSteps) * 100;
            $('.progress-bar').css('width', progressPercent + '%')
                             .attr('aria-valuenow', progressPercent)
                             .html('<span class="step-title">' + stepTitles[currentStep - 1] + '</span>');
            
            // Update buttons
            if (currentStep === 1) {
                $('#prevBtn').hide();
            } else {
                $('#prevBtn').show();
            }
            
            if (currentStep === totalSteps) {
                $('#nextBtn').hide();
                $('#submitBtn').show();
            } else {
                $('#nextBtn').show();
                $('#submitBtn').hide();
            }
        }
    </script>
}