@model SMS_Maritime_Web.ViewModels.DepartmentViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("EditDepartment");
    var departmentTypes = ViewData["DepartmentTypes"] as List<SMS_Maritime_Web.Models.DepartmentType> ?? new List<SMS_Maritime_Web.Models.DepartmentType>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit"></i> @LanguageService.GetText("EditDepartment") - @Model.Name
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Details" asp-route-id="@Model.DepartmentId" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> @LanguageService.GetText("Details")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <form asp-action="Edit" method="post">
                    <input type="hidden" asp-for="DepartmentId" />
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Name" class="control-label">@LanguageService.GetText("Name") *</label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DepartmentTypeId" class="control-label">@LanguageService.GetText("DepartmentType") *</label>
                                    <select asp-for="DepartmentTypeId" class="form-control" required>
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var departmentType in departmentTypes)
                                        {
                                            <option value="@departmentType.DepartmentTypeId" selected="@(departmentType.DepartmentTypeId == Model.DepartmentTypeId ? "selected" : null)">
                                                @departmentType.Name
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="DepartmentTypeId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="IsCritical" class="control-label">@LanguageService.GetText("CriticalStatus")</label>
                                    <div class="form-check">
                                        <input asp-for="IsCritical" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsCritical" class="form-check-label">
                                            @LanguageService.GetText("IsCritical")
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">@LanguageService.GetText("CriticalDepartmentDescription")</small>
                                    <span asp-validation-for="IsCritical" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.DepartmentId" class="btn btn-info">
                            <i class="fas fa-eye"></i> @LanguageService.GetText("Details")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}