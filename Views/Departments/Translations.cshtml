@model SMS_Maritime_Web.ViewModels.DepartmentTranslationViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Translations");
    Html.EnableClientValidation(false);
    Html.EnableUnobtrusiveJavaScript(false);
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-language"></i> @LanguageService.GetText("Translations") - @Model.OriginalName
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Details" asp-route-id="@Model.DepartmentId" class="btn btn-info btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToDetails")
                        </a>
                    </div>
                </div>
                <form action="/Departments/Translations/@Model.DepartmentId" method="post" id="translationsForm">
                    @Html.AntiForgeryToken()
                    <input type="hidden" name="DepartmentId" value="@Model.DepartmentId" />
                    <input type="hidden" name="OriginalName" value="@Model.OriginalName" />
                    <input type="hidden" name="DepartmentTypeName" value="@Model.DepartmentTypeName" />
                    
                    <div class="card-body">
                        
                        <!-- Original Values -->
                        <div class="alert alert-info">
                            <h5>@LanguageService.GetText("OriginalValues")</h5>
                            <strong>@LanguageService.GetText("Name"):</strong> @Model.OriginalName<br />
                            <strong>@LanguageService.GetText("DepartmentType"):</strong> @Model.DepartmentTypeName
                        </div>
                        
                        <!-- Translations -->
                        <h4>@LanguageService.GetText("TranslationsByLanguage")</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th width="25%">@LanguageService.GetText("Language")</th>
                                        <th width="75%">@LanguageService.GetText("Name")</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (int i = 0; i < Model.Translations.Count; i++)
                                    {
                                        <tr>
                                            <td>
                                                <input type="hidden" name="Translations[@i].LanguageId" value="@Model.Translations[i].LanguageId" />
                                                <input type="hidden" name="Translations[@i].LanguageCode" value="@Model.Translations[i].LanguageCode" />
                                                <input type="hidden" name="Translations[@i].LanguageName" value="@Model.Translations[i].LanguageName" />
                                                <img src="https://flagcdn.com/16x12/@(Model.Translations[i].LanguageCode == "en" ? "gb" : Model.Translations[i].LanguageCode).png" 
                                                     alt="@Model.Translations[i].LanguageCode" class="mr-2" />
                                                @Model.Translations[i].LanguageName
                                            </td>
                                            <td>
                                                <input name="Translations[@i].Name" 
                                                       value="@(Model.Translations[i].Name ?? "")" 
                                                       class="form-control translation-input" 
                                                       placeholder="@LanguageService.GetText("EnterTranslation")" />
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="button" id="saveButton" class="btn btn-success">
                            <i class="fas fa-save"></i> @LanguageService.GetText("SaveTranslations")
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.DepartmentId" class="btn btn-secondary">
                            <i class="fas fa-times"></i> @LanguageService.GetText("Cancel")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Handle save button click
            $('#saveButton').click(function(e) {
                e.preventDefault();
                
                // Submit the form directly
                document.getElementById('translationsForm').submit();
            });
        });
    </script>
}

