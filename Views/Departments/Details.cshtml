@model SMS_Maritime_Web.ViewModels.DepartmentViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("DepartmentDetails");
    var employees = ViewBag.Employees as List<SMS_Maritime_Web.Models.User> ?? new List<SMS_Maritime_Web.Models.User>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building"></i> @LanguageService.GetText("DepartmentDetails") - @Model.Name
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Translations" asp-route-id="@Model.DepartmentId" class="btn btn-info btn-sm">
                            <i class="fas fa-language"></i> @LanguageService.GetText("Translations")
                        </a>
                        <a asp-action="Edit" asp-route-id="@Model.DepartmentId" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> @LanguageService.GetText("Edit")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Department Information -->
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("DepartmentInformation")</h4>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Name")</label>
                                <p class="form-control-static">@Model.Name</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DepartmentType")</label>
                                <p class="form-control-static">@Model.DepartmentTypeName</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CriticalStatus")</label>
                                <p class="form-control-static">
                                    @if (Model.IsCritical)
                                    {
                                        <span class="badge bg-warning text-dark">@LanguageService.GetText("Critical")</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">@LanguageService.GetText("NonCritical")</span>
                                    }
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("EmployeeCount")</label>
                                <p class="form-control-static">
                                    @if (Model.EmployeeCount > 0)
                                    {
                                        <span class="badge bg-info">@Model.EmployeeCount @LanguageService.GetText("Employees")</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">@LanguageService.GetText("NoEmployees")</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Creation Information -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">@LanguageService.GetText("CreationInformation")</h4>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CreatedBy")</label>
                                <p class="form-control-static">@Model.CreatorUserName</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>@LanguageService.GetText("CreationDate")</label>
                                <p class="form-control-static">@Model.CreationTime.ToString("yyyy-MM-dd HH:mm")</p>
                            </div>
                        </div>
                        @if (Model.LastModificationTime.HasValue)
                        {
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>@LanguageService.GetText("LastModifiedBy")</label>
                                    <p class="form-control-static">@(Model.LastModifierUserName ?? "-")</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>@LanguageService.GetText("LastModificationDate")</label>
                                    <p class="form-control-static">@Model.LastModificationTime.Value.ToString("yyyy-MM-dd HH:mm")</p>
                                </div>
                            </div>
                        }
                    </div>
                    
                    <!-- Employees List -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="mb-3">
                                @LanguageService.GetText("Employees") 
                                <span class="badge bg-info">@employees.Count</span>
                            </h4>
                            
                            @if (employees.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>@LanguageService.GetText("EmployeeCode")</th>
                                                <th>@LanguageService.GetText("Name")</th>
                                                <th>@LanguageService.GetText("Email")</th>
                                                <th>@LanguageService.GetText("HireDate")</th>
                                                <th>@LanguageService.GetText("Status")</th>
                                                <th>@LanguageService.GetText("Actions")</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var employee in employees)
                                            {
                                                <tr>
                                                    <td>@(employee.EmployeeCode ?? "-")</td>
                                                    <td>
                                                        <strong>@employee.FirstName @employee.LastName</strong>
                                                        @if (!string.IsNullOrEmpty(employee.DisplayName))
                                                        {
                                                            <br /><small class="text-muted">(@employee.DisplayName)</small>
                                                        }
                                                    </td>
                                                    <td>@employee.Email</td>
                                                    <td>@(employee.HireDate?.ToString("yyyy-MM-dd") ?? "-")</td>
                                                    <td>
                                                        @if (employee.IsActive)
                                                        {
                                                            <span class="badge bg-success">@LanguageService.GetText("Active")</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-danger">@LanguageService.GetText("Inactive")</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <a asp-controller="Users" asp-action="Details" asp-route-id="@employee.Id" class="btn btn-info btn-sm" title="@LanguageService.GetText("Details")">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">@LanguageService.GetText("NoEmployeesInDepartment")</h5>
                                    <p class="text-muted">@LanguageService.GetText("NoEmployeesInDepartmentDescription")</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    @if (Model.EmployeeCount == 0)
                    {
                        <button type="button" class="btn btn-danger" onclick="confirmDelete('@Model.DepartmentId', '@Model.Name')">
                            <i class="fas fa-trash"></i> @LanguageService.GetText("Delete")
                        </button>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@LanguageService.GetText("ConfirmDeletion")</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>@LanguageService.GetText("AreYouSureDeleteDepartment") <strong id="deleteItemName"></strong>?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@LanguageService.GetText("Cancel")</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">@LanguageService.GetText("Delete")</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteItemName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete", "Departments")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
}