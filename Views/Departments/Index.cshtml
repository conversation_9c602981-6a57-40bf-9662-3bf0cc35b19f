@model IEnumerable<SMS_Maritime_Web.ViewModels.DepartmentViewModel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Departments");
    var departmentTypes = ViewBag.DepartmentTypes as List<SMS_Maritime_Web.Models.DepartmentType> ?? new List<SMS_Maritime_Web.Models.DepartmentType>();
    var selectedDepartmentTypeId = ViewBag.SelectedDepartmentTypeId as Guid?;
    var selectedIsCritical = ViewBag.SelectedIsCritical as bool?;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building"></i> @LanguageService.GetText("Departments")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Create" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                        <a asp-controller="DepartmentTypes" asp-action="Index" class="btn btn-info btn-sm">
                            <i class="fas fa-sitemap"></i> @LanguageService.GetText("DepartmentTypes")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- Filters -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>@LanguageService.GetText("DepartmentType")</label>
                                    <select name="departmentTypeId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllDepartmentTypes") --</option>
                                        @foreach (var departmentType in departmentTypes)
                                        {
                                            <option value="@departmentType.DepartmentTypeId" selected="@(selectedDepartmentTypeId == departmentType.DepartmentTypeId ? "selected" : null)">
                                                @departmentType.Name
                                            </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>@LanguageService.GetText("CriticalStatus")</label>
                                    <select name="isCritical" class="form-control">
                                        <option value="">-- @LanguageService.GetText("All") --</option>
                                        <option value="true" selected="@(selectedIsCritical == true ? "selected" : null)">@LanguageService.GetText("Critical")</option>
                                        <option value="false" selected="@(selectedIsCritical == false ? "selected" : null)">@LanguageService.GetText("NonCritical")</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter"></i> @LanguageService.GetText("Filter")
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="departmentsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>@LanguageService.GetText("Name")</th>
                                    <th>@LanguageService.GetText("DepartmentType")</th>
                                    <th>@LanguageService.GetText("EmployeeCount")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("CreatedBy")</th>
                                    <th>@LanguageService.GetText("CreationDate")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@item.Name</strong>
                                            @if (item.IsCritical)
                                            {
                                                <span class="badge bg-warning text-dark ms-1">@LanguageService.GetText("Critical")</span>
                                            }
                                        </td>
                                        <td>@item.DepartmentTypeName</td>
                                        <td>
                                            @if (item.EmployeeCount > 0)
                                            {
                                                <span class="badge bg-info">@item.EmployeeCount @LanguageService.GetText("Employees")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">@LanguageService.GetText("NoEmployees")</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-success">@LanguageService.GetText("Active")</span>
                                        </td>
                                        <td>@item.CreatorUserName</td>
                                        <td>@item.CreationTime.ToString("yyyy-MM-dd HH:mm")</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a asp-action="Details" asp-route-id="@item.DepartmentId" class="btn btn-info btn-sm" title="@LanguageService.GetText("Details")">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.DepartmentId" class="btn btn-warning btn-sm" title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if (item.EmployeeCount == 0)
                                                {
                                                    <button type="button" class="btn btn-danger btn-sm" title="@LanguageService.GetText("Delete")" 
                                                            onclick="confirmDelete('@item.DepartmentId', '@item.Name')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@LanguageService.GetText("ConfirmDeletion")</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>@LanguageService.GetText("AreYouSureDeleteDepartment") <strong id="deleteItemName"></strong>?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@LanguageService.GetText("Cancel")</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">@LanguageService.GetText("Delete")</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteItemName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete", "Departments")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        $(document).ready(function() {
            $('#departmentsTable').DataTable({
                responsive: true,
                order: [[0, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [6] }
                ]
            });
        });
    </script>
}