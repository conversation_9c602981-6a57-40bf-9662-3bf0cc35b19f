@model SMS_Maritime_Web.ViewModels.VesselDocumentViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CreateVesselCertificate");
    var vessels = ViewData["Vessels"] as List<SMS_Maritime_Web.Models.Vessel> ?? new List<SMS_Maritime_Web.Models.Vessel>();
    var certificateTypes = ViewData["CertificateTypes"] as List<SMS_Maritime_Web.Models.CertificateType> ?? new List<SMS_Maritime_Web.Models.CertificateType>();
    var statuses = ViewData["Statuses"] as List<string> ?? new List<string>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-certificate"></i> @LanguageService.GetText("CreateVesselCertificate")
                    </h3>
                </div>
                <form asp-action="CreateVesselDocument" method="post" enctype="multipart/form-data">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("BasicInformation")</h4>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="VesselId" class="control-label">@LanguageService.GetText("Vessel") *</label>
                                    <select asp-for="VesselId" class="form-control select2" required>
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var vessel in vessels)
                                        {
                                            <option value="@vessel.Id">@vessel.VesselName (@vessel.VesselCode)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="VesselId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="CertificateTypeId" class="control-label">@LanguageService.GetText("CertificateType") *</label>
                                    <select asp-for="CertificateTypeId" class="form-control" required>
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var certType in certificateTypes.GroupBy(ct => ct.Category))
                                        {
                                            <optgroup label="@(certType.Key ?? "Other")">
                                                @foreach (var type in certType.OrderBy(t => t.SortOrder))
                                                {
                                                    <option value="@type.Id">@type.Name</option>
                                                }
                                            </optgroup>
                                        }
                                    </select>
                                    <span asp-validation-for="CertificateTypeId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="CertificateNumber" class="control-label">@LanguageService.GetText("CertificateNumber")</label>
                                    <input asp-for="CertificateNumber" class="form-control" />
                                    <span asp-validation-for="CertificateNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Dates -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="IssueDate" class="control-label">@LanguageService.GetText("IssueDate") *</label>
                                    <input asp-for="IssueDate" type="date" class="form-control" />
                                    <span asp-validation-for="IssueDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="ExpiryDate" class="control-label">@LanguageService.GetText("ExpiryDate") *</label>
                                    <input asp-for="ExpiryDate" type="date" class="form-control" />
                                    <span asp-validation-for="ExpiryDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Status" class="control-label">@LanguageService.GetText("Status")</label>
                                    <select asp-for="Status" class="form-control">
                                        @foreach (var status in statuses)
                                        {
                                            <option value="@status">@LanguageService.GetText(status)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input asp-for="IsOriginal" type="checkbox" class="form-check-input" />
                                        <label asp-for="IsOriginal" class="form-check-label">@LanguageService.GetText("OriginalDocument")</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Survey Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("SurveyInformation")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="LastEndorsementDate" class="control-label">@LanguageService.GetText("LastEndorsementDate")</label>
                                    <input asp-for="LastEndorsementDate" type="date" class="form-control" />
                                    <span asp-validation-for="LastEndorsementDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="NextEndorsementDate" class="control-label">@LanguageService.GetText("NextEndorsementDate")</label>
                                    <input asp-for="NextEndorsementDate" type="date" class="form-control" />
                                    <span asp-validation-for="NextEndorsementDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="LastIntermediateDate" class="control-label">@LanguageService.GetText("LastIntermediateDate")</label>
                                    <input asp-for="LastIntermediateDate" type="date" class="form-control" />
                                    <span asp-validation-for="LastIntermediateDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="NextIntermediateDate" class="control-label">@LanguageService.GetText("NextIntermediateDate")</label>
                                    <input asp-for="NextIntermediateDate" type="date" class="form-control" />
                                    <span asp-validation-for="NextIntermediateDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Issuing Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("IssuingDetails")</h4>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IssuedBy" class="control-label">@LanguageService.GetText("IssuedBy")</label>
                                    <input asp-for="IssuedBy" class="form-control" />
                                    <span asp-validation-for="IssuedBy" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IssuedAt" class="control-label">@LanguageService.GetText("IssuedAt")</label>
                                    <input asp-for="IssuedAt" class="form-control" />
                                    <span asp-validation-for="IssuedAt" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IssuingAuthority" class="control-label">@LanguageService.GetText("IssuingAuthority")</label>
                                    <input asp-for="IssuingAuthority" class="form-control" />
                                    <span asp-validation-for="IssuingAuthority" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyType" class="control-label">@LanguageService.GetText("SurveyType")</label>
                                    <input asp-for="SurveyType" class="form-control" />
                                    <span asp-validation-for="SurveyType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyorName" class="control-label">@LanguageService.GetText("SurveyorName")</label>
                                    <input asp-for="SurveyorName" class="form-control" />
                                    <span asp-validation-for="SurveyorName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="SurveyCompany" class="control-label">@LanguageService.GetText("SurveyCompany")</label>
                                    <input asp-for="SurveyCompany" class="form-control" />
                                    <span asp-validation-for="SurveyCompany" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Remarks and Upload -->
                        <hr />
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Remarks" class="control-label">@LanguageService.GetText("Remarks")</label>
                                    <textarea asp-for="Remarks" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Remarks" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DocumentFile" class="control-label">@LanguageService.GetText("CertificateFile")</label>
                                    <input asp-for="DocumentFile" type="file" class="form-control" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" />
                                    <span asp-validation-for="DocumentFile" class="text-danger"></span>
                                    <small class="text-muted">
                                        @LanguageService.GetText("AllowedFormats"): PDF, JPG, PNG, DOC, DOCX. 
                                        @LanguageService.GetText("MaxFileSize"): 10MB
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="VesselDocuments" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        });
    </script>
}