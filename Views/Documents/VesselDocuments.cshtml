@model IEnumerable<SMS_Maritime_Web.ViewModels.VesselDocumentViewModel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("VesselCertificates");
    var filter = ViewBag.Filter as SMS_Maritime_Web.ViewModels.DocumentFilterViewModel ?? new SMS_Maritime_Web.ViewModels.DocumentFilterViewModel();
    var vessels = ViewBag.Vessels as List<SMS_Maritime_Web.Models.Vessel> ?? new List<SMS_Maritime_Web.Models.Vessel>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-certificate"></i> @LanguageService.GetText("VesselCertificates")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="CreateVesselDocument" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("Back")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <!-- Filter Form -->
                    <form method="get" asp-action="VesselDocuments" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <input type="text" name="SearchTerm" value="@filter.SearchTerm" 
                                           class="form-control" placeholder="@LanguageService.GetText("SearchCertificates")" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <select name="VesselId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllVessels") --</option>
                                        @foreach (var vessel in vessels)
                                        {
                                            @if (filter.VesselId == vessel.Id)
                                            {
                                                <option value="@vessel.Id" selected>@vessel.VesselName (@vessel.VesselCode)</option>
                                            }
                                            else
                                            {
                                                <option value="@vessel.Id">@vessel.VesselName (@vessel.VesselCode)</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <select name="Status" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllStatus") --</option>
                                        @if (filter.Status == "Valid")
                                        {
                                            <option value="Valid" selected>@LanguageService.GetText("Valid")</option>
                                        }
                                        else
                                        {
                                            <option value="Valid">@LanguageService.GetText("Valid")</option>
                                        }
                                        @if (filter.Status == "Expired")
                                        {
                                            <option value="Expired" selected>@LanguageService.GetText("Expired")</option>
                                        }
                                        else
                                        {
                                            <option value="Expired">@LanguageService.GetText("Expired")</option>
                                        }
                                        @if (filter.Status == "Suspended")
                                        {
                                            <option value="Suspended" selected>@LanguageService.GetText("Suspended")</option>
                                        }
                                        else
                                        {
                                            <option value="Suspended">@LanguageService.GetText("Suspended")</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group form-check">
                                    <input type="checkbox" name="ShowExpired" value="true" 
                                           class="form-check-input" id="showExpired"
                                           @(filter.ShowExpired == true ? "checked" : "") />
                                    <label class="form-check-label" for="showExpired">
                                        @LanguageService.GetText("Expired")
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-search"></i> @LanguageService.GetText("Filter")
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="certificatesTable">
                            <thead>
                                <tr>
                                    <th>@LanguageService.GetText("Vessel")</th>
                                    <th>@LanguageService.GetText("CertificateType")</th>
                                    <th>@LanguageService.GetText("CertificateNumber")</th>
                                    <th>@LanguageService.GetText("IssuingAuthority")</th>
                                    <th>@LanguageService.GetText("IssueDate")</th>
                                    <th>@LanguageService.GetText("ExpiryDate")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("NextSurvey")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var certificate in Model)
                                {
                                    <tr>
                                        <td>
                                            <a asp-controller="Ships" asp-action="Details" asp-route-id="@certificate.VesselId">
                                                @certificate.VesselName
                                            </a>
                                        </td>
                                        <td>
                                            @certificate.CertificateTypeName
                                            @if (!string.IsNullOrEmpty(certificate.CertificateCategory))
                                            {
                                                <br />
                                                <small class="text-muted">@certificate.CertificateCategory</small>
                                            }
                                        </td>
                                        <td>@certificate.CertificateNumber</td>
                                        <td>@certificate.IssuingAuthority</td>
                                        <td>@certificate.IssueDate.ToString("dd.MM.yyyy")</td>
                                        <td>
                                            <span class="@(certificate.IsExpired ? "text-danger" : certificate.IsExpiringSoon ? "text-warning" : "")">
                                                @certificate.ExpiryDate.ToString("dd.MM.yyyy")
                                            </span>
                                            @if (certificate.IsExpired)
                                            {
                                                <span class="badge bg-danger">@LanguageService.GetText("Expired")</span>
                                            }
                                            else if (certificate.IsExpiringSoon)
                                            {
                                                <span class="badge bg-warning">
                                                    @certificate.DaysUntilExpiry @LanguageService.GetText("Days")
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            @switch (certificate.Status)
                                            {
                                                case "Valid":
                                                    <span class="badge bg-success">@LanguageService.GetText("Valid")</span>
                                                    break;
                                                case "Expired":
                                                    <span class="badge bg-danger">@LanguageService.GetText("Expired")</span>
                                                    break;
                                                case "Suspended":
                                                    <span class="badge bg-warning">@LanguageService.GetText("Suspended")</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-secondary">@certificate.Status</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            @if (certificate.NextEndorsementDate.HasValue)
                                            {
                                                <small>
                                                    @LanguageService.GetText("Endorsement"): 
                                                    <span class="@(certificate.NextEndorsementDate.Value <= DateTime.UtcNow.Date.AddDays(30) ? "text-warning" : "")">
                                                        @certificate.NextEndorsementDate.Value.ToString("dd.MM.yyyy")
                                                    </span>
                                                </small>
                                            }
                                            @if (certificate.NextIntermediateDate.HasValue)
                                            {
                                                <br />
                                                <small>
                                                    @LanguageService.GetText("Intermediate"): 
                                                    <span class="@(certificate.NextIntermediateDate.Value <= DateTime.UtcNow.Date.AddDays(30) ? "text-warning" : "")">
                                                        @certificate.NextIntermediateDate.Value.ToString("dd.MM.yyyy")
                                                    </span>
                                                </small>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (!string.IsNullOrEmpty(certificate.DocumentPath))
                                                {
                                                    <a asp-action="Download" asp-route-id="@certificate.Id" asp-route-type="vessel" 
                                                       class="btn btn-info btn-sm" title="@LanguageService.GetText("Download")">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                }
                                                <a asp-action="EditVesselDocument" asp-route-id="@certificate.Id" 
                                                   class="btn btn-warning btn-sm" title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#certificatesTable').DataTable({
                "language": {
                    "url": "/lib/datatables/i18n/@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName).json"
                },
                "order": [[5, "asc"]], // Sort by expiry date
                "pageLength": 25,
                "responsive": true
            });
        });
    </script>
}