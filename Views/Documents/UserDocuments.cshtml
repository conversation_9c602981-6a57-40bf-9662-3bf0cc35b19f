@model IEnumerable<SMS_Maritime_Web.ViewModels.UserDocumentViewModel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CrewDocuments");
    var filter = ViewBag.Filter as SMS_Maritime_Web.ViewModels.DocumentFilterViewModel ?? new SMS_Maritime_Web.ViewModels.DocumentFilterViewModel();
    var documentTypes = ViewBag.DocumentTypes as List<string> ?? new List<string>();
    var users = ViewBag.Users as List<SMS_Maritime_Web.Models.User> ?? new List<SMS_Maritime_Web.Models.User>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-id-card"></i> @LanguageService.GetText("CrewDocuments")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="CreateUserDocument" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("Back")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <!-- Filter Form -->
                    <form method="get" asp-action="UserDocuments" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <input type="text" name="SearchTerm" value="@filter.SearchTerm" 
                                           class="form-control" placeholder="@LanguageService.GetText("SearchDocuments")" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <select name="UserId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllUsers") --</option>
                                        @foreach (var user in users)
                                        {
                                            @if (filter.UserId == user.Id)
                                            {
                                                <option value="@user.Id" selected>@user.FirstName @user.LastName</option>
                                            }
                                            else
                                            {
                                                <option value="@user.Id">@user.FirstName @user.LastName</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <select name="DocumentType" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllDocumentTypes") --</option>
                                        @foreach (var docType in documentTypes)
                                        {
                                            @if (filter.DocumentType == docType)
                                            {
                                                <option value="@docType" selected>@LanguageService.GetText(docType.Replace(" ", ""))</option>
                                            }
                                            else
                                            {
                                                <option value="@docType">@LanguageService.GetText(docType.Replace(" ", ""))</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <select name="Status" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllStatus") --</option>
                                        @if (filter.Status == "Pending")
                                        {
                                            <option value="Pending" selected>@LanguageService.GetText("Pending")</option>
                                        }
                                        else
                                        {
                                            <option value="Pending">@LanguageService.GetText("Pending")</option>
                                        }
                                        @if (filter.Status == "Verified")
                                        {
                                            <option value="Verified" selected>@LanguageService.GetText("Verified")</option>
                                        }
                                        else
                                        {
                                            <option value="Verified">@LanguageService.GetText("Verified")</option>
                                        }
                                        @if (filter.Status == "Rejected")
                                        {
                                            <option value="Rejected" selected>@LanguageService.GetText("Rejected")</option>
                                        }
                                        else
                                        {
                                            <option value="Rejected">@LanguageService.GetText("Rejected")</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group form-check">
                                    <input type="checkbox" name="ShowExpired" value="true" 
                                           class="form-check-input" id="showExpired"
                                           @(filter.ShowExpired == true ? "checked" : "") />
                                    <label class="form-check-label" for="showExpired">
                                        @LanguageService.GetText("Expired")
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group form-check">
                                    <input type="checkbox" name="ShowExpiringSoon" value="true" 
                                           class="form-check-input" id="showExpiring"
                                           @(filter.ShowExpiringSoon == true ? "checked" : "") />
                                    <label class="form-check-label" for="showExpiring">
                                        @LanguageService.GetText("ExpiringSoon")
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-search"></i> @LanguageService.GetText("Filter")
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="documentsTable">
                            <thead>
                                <tr>
                                    <th>@LanguageService.GetText("CrewMember")</th>
                                    <th>@LanguageService.GetText("DocumentType")</th>
                                    <th>@LanguageService.GetText("DocumentNumber")</th>
                                    <th>@LanguageService.GetText("IssuingAuthority")</th>
                                    <th>@LanguageService.GetText("IssueDate")</th>
                                    <th>@LanguageService.GetText("ExpiryDate")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("VerificationStatus")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var document in Model)
                                {
                                    <tr>
                                        <td>
                                            <a asp-controller="Users" asp-action="Details" asp-route-id="@document.UserId">
                                                @document.UserFullName
                                            </a>
                                        </td>
                                        <td>@LanguageService.GetText(document.DocumentType.Replace(" ", ""))</td>
                                        <td>@document.DocumentNumber</td>
                                        <td>@document.IssuingAuthority</td>
                                        <td>@document.IssueDate.ToString("dd.MM.yyyy")</td>
                                        <td>
                                            @if (document.ExpiryDate.HasValue)
                                            {
                                                <span class="@(document.IsExpired ? "text-danger" : document.IsExpiringSoon ? "text-warning" : "")">
                                                    @document.ExpiryDate.Value.ToString("dd.MM.yyyy")
                                                </span>
                                                @if (document.IsExpired)
                                                {
                                                    <span class="badge bg-danger">@LanguageService.GetText("Expired")</span>
                                                }
                                                else if (document.IsExpiringSoon)
                                                {
                                                    <span class="badge bg-warning">
                                                        @document.DaysUntilExpiry @LanguageService.GetText("Days")
                                                    </span>
                                                }
                                            }
                                            else
                                            {
                                                <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (document.IsExpired)
                                            {
                                                <span class="badge bg-danger">@LanguageService.GetText("Expired")</span>
                                            }
                                            else if (document.IsExpiringSoon)
                                            {
                                                <span class="badge bg-warning">@LanguageService.GetText("ExpiringSoon")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("Valid")</span>
                                            }
                                        </td>
                                        <td>
                                            @switch (document.VerificationStatus)
                                            {
                                                case "Verified":
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle"></i> @LanguageService.GetText("Verified")
                                                    </span>
                                                    break;
                                                case "Rejected":
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times-circle"></i> @LanguageService.GetText("Rejected")
                                                    </span>
                                                    break;
                                                default:
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-clock"></i> @LanguageService.GetText("Pending")
                                                    </span>
                                                    break;
                                            }
                                            @if (document.VerifiedDate.HasValue)
                                            {
                                                <br />
                                                <small class="text-muted">
                                                    @document.VerifiedDate.Value.ToString("dd.MM.yyyy")
                                                    @if (!string.IsNullOrEmpty(document.VerifiedByName))
                                                    {
                                                        <span>by @document.VerifiedByName</span>
                                                    }
                                                </small>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (!string.IsNullOrEmpty(document.DocumentPath))
                                                {
                                                    <a asp-action="Download" asp-route-id="@document.Id" asp-route-type="user" 
                                                       class="btn btn-info btn-sm" title="@LanguageService.GetText("Download")">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                }
                                                <button type="button" class="btn btn-primary btn-sm" 
                                                        data-bs-toggle="modal" data-bs-target="#verifyModal"
                                                        data-id="@document.Id" 
                                                        data-doctype="@document.DocumentType"
                                                        data-docnumber="@document.DocumentNumber"
                                                        title="@LanguageService.GetText("Verify")">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <a asp-action="EditUserDocument" asp-route-id="@document.Id" 
                                                   class="btn btn-warning btn-sm" title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Verify Modal -->
<div class="modal fade" id="verifyModal" tabindex="-1" aria-labelledby="verifyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form asp-action="Verify" method="post">
                @Html.AntiForgeryToken()
                <div class="modal-header">
                    <h5 class="modal-title" id="verifyModalLabel">@LanguageService.GetText("VerifyDocument")</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="id" id="documentId" />
                    <div class="mb-3">
                        <label class="form-label">@LanguageService.GetText("Document")</label>
                        <p class="form-control-plaintext" id="documentInfo"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">@LanguageService.GetText("VerificationStatus")</label>
                        <select name="status" class="form-control" required>
                            <option value="Verified">@LanguageService.GetText("Verified")</option>
                            <option value="Rejected">@LanguageService.GetText("Rejected")</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">@LanguageService.GetText("Notes")</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        @LanguageService.GetText("Cancel")
                    </button>
                    <button type="submit" class="btn btn-primary">
                        @LanguageService.GetText("Save")
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#documentsTable').DataTable({
                "language": {
                    "url": "/lib/datatables/i18n/@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName).json"
                },
                "order": [[5, "asc"]], // Sort by expiry date
                "pageLength": 25,
                "responsive": true
            });
            
            // Verify modal
            $('#verifyModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var id = button.data('id');
                var docType = button.data('doctype');
                var docNumber = button.data('docnumber');
                
                var modal = $(this);
                modal.find('#documentId').val(id);
                modal.find('#documentInfo').text(docType + ' - ' + docNumber);
            });
        });
    </script>
}