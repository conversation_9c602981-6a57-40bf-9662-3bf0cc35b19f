@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("DocumentManagement");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-alt"></i> @LanguageService.GetText("DocumentManagement")
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                @LanguageService.GetText("CrewDocuments")</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                @LanguageService.GetText("ManageCrewCertificatesAndDocuments")
                                            </div>
                                            <div class="mt-3">
                                                <a asp-action="UserDocuments" class="btn btn-primary">
                                                    <i class="fas fa-users"></i> @LanguageService.GetText("ViewCrewDocuments")
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-id-card fa-3x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                @LanguageService.GetText("VesselCertificates")</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                @LanguageService.GetText("ManageVesselCertificatesAndDocuments")
                                            </div>
                                            <div class="mt-3">
                                                <a asp-action="VesselDocuments" class="btn btn-success">
                                                    <i class="fas fa-ship"></i> @LanguageService.GetText("ViewVesselCertificates")
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-certificate fa-3x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-exclamation-triangle"></i> @LanguageService.GetText("DocumentAlerts")
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="info-box bg-danger">
                                                <span class="info-box-icon"><i class="fas fa-times-circle"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">@LanguageService.GetText("ExpiredDocuments")</span>
                                                    <span class="info-box-number" id="expiredCount">0</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="info-box bg-warning">
                                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">@LanguageService.GetText("ExpiringIn30Days")</span>
                                                    <span class="info-box-number" id="expiring30Count">0</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="info-box bg-info">
                                                <span class="info-box-icon"><i class="fas fa-calendar-alt"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">@LanguageService.GetText("ExpiringIn90Days")</span>
                                                    <span class="info-box-number" id="expiring90Count">0</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="info-box bg-success">
                                                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">@LanguageService.GetText("ValidDocuments")</span>
                                                    <span class="info-box-number" id="validCount">0</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Load document statistics
            loadDocumentStats();
        });
        
        function loadDocumentStats() {
            // This would typically make an AJAX call to get real statistics
            // For now, using placeholder values
            $('#expiredCount').text('0');
            $('#expiring30Count').text('0');
            $('#expiring90Count').text('0');
            $('#validCount').text('0');
        }
    </script>
}