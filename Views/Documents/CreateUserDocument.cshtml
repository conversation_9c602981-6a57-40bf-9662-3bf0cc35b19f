@model SMS_Maritime_Web.ViewModels.UserDocumentViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CreateUserDocument");
    var users = ViewData["Users"] as List<SMS_Maritime_Web.Models.User> ?? new List<SMS_Maritime_Web.Models.User>();
    var countries = ViewData["Countries"] as List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> ?? new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();
    var documentTypes = ViewData["DocumentTypes"] as List<string> ?? new List<string>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-upload"></i> @LanguageService.GetText("CreateUserDocument")
                    </h3>
                </div>
                <form asp-action="CreateUserDocument" method="post" enctype="multipart/form-data">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("BasicInformation")</h4>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="UserId" class="control-label">@LanguageService.GetText("CrewMember") *</label>
                                    <select asp-for="UserId" class="form-control select2" required>
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var user in users)
                                        {
                                            <option value="@user.Id">@user.FirstName @user.LastName (@user.Username)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="UserId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="DocumentType" class="control-label">@LanguageService.GetText("DocumentType") *</label>
                                    <select asp-for="DocumentType" class="form-control" required>
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                        @foreach (var docType in documentTypes)
                                        {
                                            <option value="@docType">@LanguageService.GetText(docType.Replace(" ", ""))</option>
                                        }
                                    </select>
                                    <span asp-validation-for="DocumentType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="DocumentNumber" class="control-label">@LanguageService.GetText("DocumentNumber") *</label>
                                    <input asp-for="DocumentNumber" class="form-control" />
                                    <span asp-validation-for="DocumentNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Issuing Information -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("IssuingInformation")</h4>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IssuingAuthority" class="control-label">@LanguageService.GetText("IssuingAuthority") *</label>
                                    <input asp-for="IssuingAuthority" class="form-control" />
                                    <span asp-validation-for="IssuingAuthority" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IssuingCountry" class="control-label">@LanguageService.GetText("IssuingCountry") *</label>
                                    <select asp-for="IssuingCountry" asp-items="countries" class="form-control">
                                        <option value="">-- @LanguageService.GetText("Select") --</option>
                                    </select>
                                    <span asp-validation-for="IssuingCountry" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IssuingPlace" class="control-label">@LanguageService.GetText("IssuingPlace")</label>
                                    <input asp-for="IssuingPlace" class="form-control" />
                                    <span asp-validation-for="IssuingPlace" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IssueDate" class="control-label">@LanguageService.GetText("IssueDate") *</label>
                                    <input asp-for="IssueDate" type="date" class="form-control" />
                                    <span asp-validation-for="IssueDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="ExpiryDate" class="control-label">@LanguageService.GetText("ExpiryDate")</label>
                                    <input asp-for="ExpiryDate" type="date" class="form-control" />
                                    <span asp-validation-for="ExpiryDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="control-label">@LanguageService.GetText("Options")</label>
                                    <div class="form-check">
                                        <input asp-for="IsMandatory" type="checkbox" class="form-check-input" />
                                        <label asp-for="IsMandatory" class="form-check-label">@LanguageService.GetText("MandatoryDocument")</label>
                                    </div>
                                    <div class="form-check">
                                        <input asp-for="IsOriginalSeen" type="checkbox" class="form-check-input" />
                                        <label asp-for="IsOriginalSeen" class="form-check-label">@LanguageService.GetText("OriginalDocumentSeen")</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Flag Endorsement (if applicable) -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("FlagEndorsement")</h4>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input asp-for="RequiresFlagEndorsement" type="checkbox" class="form-check-input" id="requiresEndorsement" />
                                        <label asp-for="RequiresFlagEndorsement" class="form-check-label">
                                            @LanguageService.GetText("RequiresFlagEndorsement")
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="FlagEndorsementNumber" class="control-label">@LanguageService.GetText("EndorsementNumber")</label>
                                    <input asp-for="FlagEndorsementNumber" class="form-control endorsement-field" disabled />
                                    <span asp-validation-for="FlagEndorsementNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="FlagEndorsementDate" class="control-label">@LanguageService.GetText("EndorsementDate")</label>
                                    <input asp-for="FlagEndorsementDate" type="date" class="form-control endorsement-field" disabled />
                                    <span asp-validation-for="FlagEndorsementDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="FlagEndorsementExpiry" class="control-label">@LanguageService.GetText("EndorsementExpiry")</label>
                                    <input asp-for="FlagEndorsementExpiry" type="date" class="form-control endorsement-field" disabled />
                                    <span asp-validation-for="FlagEndorsementExpiry" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Document Upload -->
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("DocumentUpload")</h4>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DocumentFile" class="control-label">@LanguageService.GetText("SelectFile")</label>
                                    <input asp-for="DocumentFile" type="file" class="form-control" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" />
                                    <span asp-validation-for="DocumentFile" class="text-danger"></span>
                                    <small class="text-muted">
                                        @LanguageService.GetText("AllowedFormats"): PDF, JPG, PNG, DOC, DOCX. 
                                        @LanguageService.GetText("MaxFileSize"): 10MB
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="UserDocuments" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
            
            // Toggle flag endorsement fields
            $('#requiresEndorsement').change(function() {
                $('.endorsement-field').prop('disabled', !this.checked);
                if (!this.checked) {
                    $('.endorsement-field').val('');
                }
            });
        });
    </script>
}