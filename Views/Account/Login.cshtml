@model SMS_Maritime_Web.ViewModels.LoginViewModel
@using SMS_Maritime_Web.Services
@inject ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Login");
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-box">
        <div class="login-logo">
            <img src="~/images/sms-maritime-logo.png" alt="SMS Maritime" class="img-fluid" />
        </div>
        <h2 class="text-center mb-4">@LanguageService.GetText("Login")</h2>
        
        <form asp-action="Login" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
            <input type="hidden" asp-for="ReturnUrl" />
            
            <div class="mb-3">
                <label asp-for="Username" class="form-label">@LanguageService.GetText("Username")</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input asp-for="Username" class="form-control" placeholder="@LanguageService.GetText("EnterUsername")" />
                </div>
                <span asp-validation-for="Username" class="text-danger"></span>
            </div>
            
            <div class="mb-3">
                <label asp-for="Password" class="form-label">@LanguageService.GetText("Password")</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input asp-for="Password" class="form-control" placeholder="@LanguageService.GetText("EnterPassword")" />
                </div>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
            
            <div class="mb-3 form-check">
                <input asp-for="RememberMe" class="form-check-input" />
                <label asp-for="RememberMe" class="form-check-label">
                    @LanguageService.GetText("RememberMe")
                </label>
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> @LanguageService.GetText("SignIn")
                </button>
            </div>
        </form>
        
        <div class="language-selector mt-4">
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-block dropdown-toggle d-flex align-items-center justify-content-between" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    @{
                        SMS_Maritime_Web.Models.Language? currentLang = null;
                        if (ViewBag.Languages != null)
                        {
                            foreach (SMS_Maritime_Web.Models.Language lang in ViewBag.Languages)
                            {
                                if (lang.Code == ViewBag.CurrentLanguage)
                                {
                                    currentLang = lang;
                                    break;
                                }
                            }
                        }
                        
                        if (currentLang != null)
                        {
                            <span class="d-flex align-items-center">
                                <span class="fi <EMAIL> me-2"></span>
                                @currentLang.Name
                            </span>
                        }
                        else
                        {
                            <span>Select Language</span>
                        }
                    }
                </button>
                <ul class="dropdown-menu w-100" aria-labelledby="languageDropdown">
                    @if (ViewBag.Languages != null)
                    {
                        @foreach (var language in ViewBag.Languages)
                    {
                        <li>
                            <a class="dropdown-item d-flex align-items-center @(language.Code == ViewBag.CurrentLanguage ? "active" : "")" 
                               href="#" onclick="changeLanguage('@language.Code'); return false;">
                                <span class="fi <EMAIL> me-2"></span>
                                @language.Name
                                @if (!string.IsNullOrEmpty(language.NativeName) && language.NativeName != language.Name)
                                {
                                    <small class="text-muted ms-2">(@language.NativeName)</small>
                                }
                            </a>
                        </li>
                    }
                    }
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        function changeLanguage(culture) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("SetLanguage", "Account")';
            
            var cultureInput = document.createElement('input');
            cultureInput.type = 'hidden';
            cultureInput.name = 'culture';
            cultureInput.value = culture;
            form.appendChild(cultureInput);
            
            var returnUrlInput = document.createElement('input');
            returnUrlInput.type = 'hidden';
            returnUrlInput.name = 'returnUrl';
            returnUrlInput.value = window.location.pathname + window.location.search;
            form.appendChild(returnUrlInput);
            
            var tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '__RequestVerificationToken';
            tokenInput.value = document.querySelector('input[name="__RequestVerificationToken"]').value;
            form.appendChild(tokenInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    </script>
}