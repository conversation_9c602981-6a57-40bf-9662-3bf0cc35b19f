@model SMS_Maritime_Web.ViewModels.DepartmentTypeViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("CreateDepartmentType");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus"></i> @LanguageService.GetText("CreateDepartmentType")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <form asp-action="Create" method="post">
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Name" class="control-label">@LanguageService.GetText("Name") *</label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="IsActive" class="control-label">@LanguageService.GetText("Status")</label>
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsActive" class="form-check-label">
                                            @LanguageService.GetText("IsActive")
                                        </label>
                                    </div>
                                    <span asp-validation-for="IsActive" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label asp-for="Description" class="control-label">@LanguageService.GetText("Description")</label>
                                    <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-times"></i> @LanguageService.GetText("Cancel")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}