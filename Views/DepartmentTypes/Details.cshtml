@model SMS_Maritime_Web.ViewModels.DepartmentTypeViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("DepartmentTypeDetails");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sitemap"></i> @LanguageService.GetText("DepartmentTypeDetails") - @Model.Name
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Translations" asp-route-id="@Model.DepartmentTypeId" class="btn btn-info btn-sm">
                            <i class="fas fa-language"></i> @LanguageService.GetText("Translations")
                        </a>
                        <a asp-action="Edit" asp-route-id="@Model.DepartmentTypeId" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> @LanguageService.GetText("Edit")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Name")</label>
                                <p class="form-control-static">@Model.Name</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Status")</label>
                                <p class="form-control-static">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">@LanguageService.GetText("Active")</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">@LanguageService.GetText("Inactive")</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>@LanguageService.GetText("Description")</label>
                                <p class="form-control-static">@(Model.Description ?? "-")</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@LanguageService.GetText("DepartmentCount")</label>
                                <p class="form-control-static">
                                    @if (Model.DepartmentCount > 0)
                                    {
                                        <span class="badge bg-info">@Model.DepartmentCount @LanguageService.GetText("Departments")</span>
                                        <a asp-controller="Departments" asp-action="Index" asp-route-departmentTypeId="@Model.DepartmentTypeId" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-eye"></i> @LanguageService.GetText("ViewDepartments")
                                        </a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">@LanguageService.GetText("NoDepartments")</span>
                                        <a asp-controller="Departments" asp-action="Create" class="btn btn-sm btn-outline-success ms-2">
                                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddDepartment")
                                        </a>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>