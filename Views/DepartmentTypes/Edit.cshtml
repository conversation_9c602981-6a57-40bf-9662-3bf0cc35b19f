@model SMS_Maritime_Web.ViewModels.DepartmentTypeViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("EditDepartmentType");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-edit"></i> @LanguageService.GetText("EditDepartmentType") - @Model.Name
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Details" asp-route-id="@Model.DepartmentTypeId" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> @LanguageService.GetText("Details")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <form asp-action="Edit" method="post">
                    <input type="hidden" asp-for="DepartmentTypeId" />
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Name" class="control-label">@LanguageService.GetText("Name") *</label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="IsActive" class="control-label">@LanguageService.GetText("Status")</label>
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsActive" class="form-check-label">
                                            @LanguageService.GetText("IsActive")
                                        </label>
                                    </div>
                                    <span asp-validation-for="IsActive" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label asp-for="Description" class="control-label">@LanguageService.GetText("Description")</label>
                                    <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> @LanguageService.GetText("Save")
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.DepartmentTypeId" class="btn btn-info">
                            <i class="fas fa-eye"></i> @LanguageService.GetText("Details")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}