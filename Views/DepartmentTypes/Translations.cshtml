@model SMS_Maritime_Web.ViewModels.DepartmentTypeTranslationViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Translations");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-language"></i> @LanguageService.GetText("Translations") - @Model.OriginalName
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Details" asp-route-id="@Model.DepartmentTypeId" class="btn btn-info btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToDetails")
                        </a>
                    </div>
                </div>
                <form asp-action="Translations" method="post">
                    <input type="hidden" asp-for="DepartmentTypeId" />
                    <input type="hidden" asp-for="OriginalName" />
                    <input type="hidden" asp-for="OriginalDescription" />
                    
                    <div class="card-body">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Original Values -->
                        <div class="alert alert-info">
                            <h5>@LanguageService.GetText("OriginalValues")</h5>
                            <strong>@LanguageService.GetText("Name"):</strong> @Model.OriginalName<br />
                            <strong>@LanguageService.GetText("Description"):</strong> @(Model.OriginalDescription ?? "-")
                        </div>
                        
                        <!-- Translations -->
                        <h4>@LanguageService.GetText("TranslationsByLanguage")</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th width="20%">@LanguageService.GetText("Language")</th>
                                        <th width="35%">@LanguageService.GetText("Name")</th>
                                        <th width="45%">@LanguageService.GetText("Description")</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (int i = 0; i < Model.Translations.Count; i++)
                                    {
                                        <tr>
                                            <td>
                                                <input type="hidden" asp-for="Translations[i].LanguageId" />
                                                <input type="hidden" asp-for="Translations[i].LanguageCode" />
                                                <input type="hidden" asp-for="Translations[i].LanguageName" />
                                                <img src="https://flagcdn.com/16x12/@(Model.Translations[i].LanguageCode == "en" ? "gb" : Model.Translations[i].LanguageCode).png" 
                                                     alt="@Model.Translations[i].LanguageCode" class="mr-2" />
                                                @Model.Translations[i].LanguageName
                                            </td>
                                            <td>
                                                <input asp-for="Translations[i].Name" class="form-control" 
                                                       placeholder="@LanguageService.GetText("EnterTranslation")" />
                                                <span asp-validation-for="Translations[i].Name" class="text-danger"></span>
                                            </td>
                                            <td>
                                                <textarea asp-for="Translations[i].Description" class="form-control" rows="2"
                                                          placeholder="@LanguageService.GetText("EnterTranslation")"></textarea>
                                                <span asp-validation-for="Translations[i].Description" class="text-danger"></span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> @LanguageService.GetText("SaveTranslations")
                        </button>
                        <a asp-action="Details" asp-route-id="@Model.DepartmentTypeId" class="btn btn-secondary">
                            <i class="fas fa-times"></i> @LanguageService.GetText("Cancel")
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

