@model IEnumerable<SMS_Maritime_Web.ViewModels.DepartmentTypeViewModel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("DepartmentTypes");
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sitemap"></i> @LanguageService.GetText("DepartmentTypes")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Create" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="departmentTypesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>@LanguageService.GetText("Name")</th>
                                    <th>@LanguageService.GetText("Description")</th>
                                    <th>@LanguageService.GetText("DepartmentCount")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@item.Name</strong>
                                        </td>
                                        <td>@(item.Description ?? "-")</td>
                                        <td>
                                            @if (item.DepartmentCount > 0)
                                            {
                                                <span class="badge bg-info">@item.DepartmentCount @LanguageService.GetText("Departments")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">@LanguageService.GetText("NoDepartments")</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("Active")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">@LanguageService.GetText("Inactive")</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a asp-action="Details" asp-route-id="@item.DepartmentTypeId" class="btn btn-info btn-sm" title="@LanguageService.GetText("Details")">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.DepartmentTypeId" class="btn btn-warning btn-sm" title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if (item.DepartmentCount == 0)
                                                {
                                                    <button type="button" class="btn btn-danger btn-sm" title="@LanguageService.GetText("Delete")" 
                                                            onclick="confirmDelete('@item.DepartmentTypeId', '@item.Name')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@LanguageService.GetText("ConfirmDeletion")</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>@LanguageService.GetText("AreYouSureDeleteDepartmentType") <strong id="deleteItemName"></strong>?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@LanguageService.GetText("Cancel")</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">@LanguageService.GetText("Delete")</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteItemName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete", "DepartmentTypes")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        $(document).ready(function() {
            $('#departmentTypesTable').DataTable({
                responsive: true,
                order: [[0, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [4] }
                ]
            });
        });
    </script>
}