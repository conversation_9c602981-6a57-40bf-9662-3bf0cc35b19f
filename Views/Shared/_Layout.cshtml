@using SMS_Maritime_Web.Services
@inject ILanguageService LanguageService
@inject IMenuService MenuService

<!DOCTYPE html>
<html lang="@System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - SMS Maritime</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.0.0/css/flag-icons.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/layout.css" asp-append-version="true" />
</head>
<body>
    @if (User.Identity?.IsAuthenticated == true)
    {
        <div class="wrapper">
            <nav class="sidebar">
                <div class="sidebar-header">
                    <h3>SMS Maritime</h3>
                </div>
                <ul class="list-unstyled components">
                    @{
                        var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                        var menus = await MenuService.GetMenusByUserAsync(userId);
                        foreach (var menu in menus)
                        {
                            if (menu.SubMenus.Any())
                            {
                                <li class="nav-item">
                                    <a href="#<EMAIL>" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                                        <i class="@menu.Icon"></i>
                                        <span>@LanguageService.GetText(menu.TextKey)</span>
                                    </a>
                                    <ul class="collapse list-unstyled" id="<EMAIL>">
                                        @foreach (var subMenu in menu.SubMenus.Where(sm => sm.IsActive).OrderBy(sm => sm.DisplayOrder))
                                        {
                                            <li>
                                                <a href="@subMenu.Url" class="dropdown-item">
                                                    <i class="@subMenu.Icon"></i>
                                                    <span>@LanguageService.GetText(subMenu.TextKey)</span>
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </li>
                            }
                            else
                            {
                                <li class="nav-item">
                                    <a href="@menu.Url" class="nav-link">
                                        <i class="@menu.Icon"></i>
                                        <span>@LanguageService.GetText(menu.TextKey)</span>
                                    </a>
                                </li>
                            }
                        }
                    }
                </ul>
            </nav>

            <div class="content">
                <nav class="navbar navbar-expand-lg navbar-light bg-light">
                    <div class="container-fluid">
                        <button type="button" id="sidebarCollapse" class="btn btn-info">
                            <i class="fas fa-align-left"></i>
                        </button>
                        
                        <div class="ms-auto d-flex align-items-center">
                            <div class="dropdown me-3">
                                @{
                                    var languages = await LanguageService.GetActiveLanguagesAsync();
                                    var currentLangCode = System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                                    var currentLanguage = languages.FirstOrDefault(l => l.Code == currentLangCode);
                                }
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" id="languageDropdown" data-bs-toggle="dropdown">
                                    @if (currentLanguage != null && !string.IsNullOrEmpty(currentLanguage.FlagCode))
                                    {
                                        <span class="fi <EMAIL> me-2"></span>
                                    }
                                    <span>@(currentLanguage?.Name ?? System.Globalization.CultureInfo.CurrentUICulture.DisplayName)</span>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                                    @foreach (var lang in languages)
                                    {
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center @(lang.Code == currentLangCode ? "active" : "")" 
                                               href="#" onclick="changeLanguage('@lang.Code'); return false;">
                                                @if (!string.IsNullOrEmpty(lang.FlagCode))
                                                {
                                                    <span class="fi <EMAIL> me-2"></span>
                                                }
                                                <span>@lang.Name</span>
                                                @if (!string.IsNullOrEmpty(lang.NativeName) && lang.NativeName != lang.Name)
                                                {
                                                    <small class="text-muted ms-2">(@lang.NativeName)</small>
                                                }
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                            
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> @User.Identity.Name
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="m-0">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-sign-out-alt"></i> @LanguageService.GetText("Logout")
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>

                <main role="main" class="p-4">
                    @RenderBody()
                </main>
            </div>
        </div>
    }
    else
    {
        <main role="main">
            @RenderBody()
        </main>
    }

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('.sidebar').toggleClass('active');
                $('.content').toggleClass('active');
            });
        });
        
        function changeLanguage(culture) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("SetLanguage", "Home")';
            
            var cultureInput = document.createElement('input');
            cultureInput.type = 'hidden';
            cultureInput.name = 'culture';
            cultureInput.value = culture;
            form.appendChild(cultureInput);
            
            var returnUrlInput = document.createElement('input');
            returnUrlInput.type = 'hidden';
            returnUrlInput.name = 'returnUrl';
            returnUrlInput.value = window.location.pathname + window.location.search;
            form.appendChild(returnUrlInput);
            
            var tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '__RequestVerificationToken';
            tokenInput.value = '@Html.AntiForgeryToken()'.split('value="')[1].split('"')[0];
            form.appendChild(tokenInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    </script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>