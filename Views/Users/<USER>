@model IEnumerable<SMS_Maritime_Web.ViewModels.UserViewModel>
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = LanguageService.GetText("Users");
    var departments = ViewBag.Departments as List<SMS_Maritime_Web.Models.CrewDepartment> ?? new List<SMS_Maritime_Web.Models.CrewDepartment>();
    var roles = ViewBag.Roles as List<SMS_Maritime_Web.Models.Role> ?? new List<SMS_Maritime_Web.Models.Role>();
    var filter = ViewBag.Filter as SMS_Maritime_Web.ViewModels.UserFilterViewModel ?? new SMS_Maritime_Web.ViewModels.UserFilterViewModel();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i> @LanguageService.GetText("Users")
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> @LanguageService.GetText("AddNew")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <!-- Filter Form -->
                    <form method="get" asp-action="Index" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <input type="text" name="SearchTerm" value="@filter.SearchTerm" 
                                           class="form-control" placeholder="@LanguageService.GetText("SearchByNameEmailUsername")" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <select name="DepartmentId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllDepartments") --</option>
                                        @foreach (var dept in departments)
                                        {
                                            @if (filter.DepartmentId == dept.Id)
                                            {
                                                <option value="@dept.Id" selected>@dept.Name</option>
                                            }
                                            else
                                            {
                                                <option value="@dept.Id">@dept.Name</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <select name="RoleId" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllRoles") --</option>
                                        @foreach (var role in roles)
                                        {
                                            @if (filter.RoleId == role.Id)
                                            {
                                                <option value="@role.Id" selected>@role.Name</option>
                                            }
                                            else
                                            {
                                                <option value="@role.Id">@role.Name</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <select name="IsActive" class="form-control">
                                        <option value="">-- @LanguageService.GetText("AllStatus") --</option>
                                        @if (filter.IsActive == true)
                                        {
                                            <option value="true" selected>@LanguageService.GetText("Active")</option>
                                        }
                                        else
                                        {
                                            <option value="true">@LanguageService.GetText("Active")</option>
                                        }
                                        @if (filter.IsActive == false)
                                        {
                                            <option value="false" selected>@LanguageService.GetText("Inactive")</option>
                                        }
                                        else
                                        {
                                            <option value="false">@LanguageService.GetText("Inactive")</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group form-check">
                                    <input type="checkbox" name="HasExpiredDocuments" value="true" 
                                           class="form-check-input" id="expiredDocs"
                                           @(filter.HasExpiredDocuments == true ? "checked" : "")/>
                                    <label class="form-check-label" for="expiredDocs">
                                        @LanguageService.GetText("ExpiredDocuments")
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-search"></i> @LanguageService.GetText("Filter")
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="usersTable">
                            <thead>
                                <tr>
                                    <th>@LanguageService.GetText("Username")</th>
                                    <th>@LanguageService.GetText("Name")</th>
                                    <th>@LanguageService.GetText("Email")</th>
                                    <th>@LanguageService.GetText("Department")</th>
                                    <th>@LanguageService.GetText("EmployeeCode")</th>
                                    <th>@LanguageService.GetText("Roles")</th>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <th>@LanguageService.GetText("Documents")</th>
                                    <th>@LanguageService.GetText("Actions")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var user in Model)
                                {
                                    <tr>
                                        <td>
                                            <a asp-action="Details" asp-route-id="@user.Id">
                                                <strong>@user.Username</strong>
                                            </a>
                                        </td>
                                        <td>@user.FirstName @user.LastName</td>
                                        <td>
                                            @user.Email
                                            @if (user.EmailConfirmed)
                                            {
                                                <i class="fas fa-check-circle text-success" title="@LanguageService.GetText("EmailConfirmed")"></i>
                                            }
                                        </td>
                                        <td>@user.DepartmentName</td>
                                        <td>@user.EmployeeCode</td>
                                        <td>
                                            @if (user.RoleNames.Any())
                                            {
                                                @string.Join(", ", user.RoleNames)
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (user.IsActive)
                                            {
                                                <span class="badge bg-success">@LanguageService.GetText("Active")</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">@LanguageService.GetText("Inactive")</span>
                                            }
                                        </td>
                                        <td>
                                            @{
                                                var today = DateTime.UtcNow.Date;
                                                var hasExpiredPassport = user.PassportExpiryDate.HasValue && user.PassportExpiryDate.Value <= today;
                                                var hasExpiredSeamanBook = user.SeamanBookExpiryDate.HasValue && user.SeamanBookExpiryDate.Value <= today;
                                                var expiringPassport = user.PassportExpiryDate.HasValue && user.PassportExpiryDate.Value <= today.AddDays(90) && !hasExpiredPassport;
                                                var expiringSeamanBook = user.SeamanBookExpiryDate.HasValue && user.SeamanBookExpiryDate.Value <= today.AddDays(90) && !hasExpiredSeamanBook;
                                            }
                                            
                                            @if (hasExpiredPassport || hasExpiredSeamanBook)
                                            {
                                                <span class="badge bg-danger" title="@LanguageService.GetText("ExpiredDocuments")">
                                                    <i class="fas fa-exclamation-triangle"></i> @LanguageService.GetText("Expired")
                                                </span>
                                            }
                                            else if (expiringPassport || expiringSeamanBook)
                                            {
                                                <span class="badge bg-warning" title="@LanguageService.GetText("ExpiringDocuments")">
                                                    <i class="fas fa-clock"></i> @LanguageService.GetText("ExpiringSoon")
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check"></i> @LanguageService.GetText("Valid")
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@user.Id" class="btn btn-info btn-sm" 
                                                   title="@LanguageService.GetText("Details")">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@user.Id" class="btn btn-warning btn-sm" 
                                                   title="@LanguageService.GetText("Edit")">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form asp-action="ToggleActive" asp-route-id="@user.Id" method="post" 
                                                      style="display: inline;" onsubmit="return confirm('@LanguageService.GetText("AreYouSure")');">
                                                    @Html.AntiForgeryToken()
                                                    @if (user.IsActive)
                                                    {
                                                        <button type="submit" class="btn btn-danger btn-sm" 
                                                                title="@LanguageService.GetText("Deactivate")">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    }
                                                    else
                                                    {
                                                        <button type="submit" class="btn btn-success btn-sm" 
                                                                title="@LanguageService.GetText("Activate")">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    }
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#usersTable').DataTable({
                "language": {
                    "url": "/lib/datatables/i18n/@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName).json"
                },
                "order": [[1, "asc"]], // Sort by name
                "pageLength": 25,
                "responsive": true
            });
        });
    </script>
}