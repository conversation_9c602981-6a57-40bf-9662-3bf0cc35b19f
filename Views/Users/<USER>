@model SMS_Maritime_Web.ViewModels.UserViewModel
@inject SMS_Maritime_Web.Services.ILanguageService LanguageService

@{
    ViewData["Title"] = $"{Model.FirstName} {Model.LastName}";
    var vesselAssignments = ViewBag.VesselAssignments as List<SMS_Maritime_Web.Models.VesselCrewAssignment> ?? new List<SMS_Maritime_Web.Models.VesselCrewAssignment>();
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user"></i> @Model.FirstName @Model.LastName
                    </h3>
                    <div class="card-tools">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> @LanguageService.GetText("Edit")
                        </a>
                        <a asp-action="Index" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> @LanguageService.GetText("BackToList")
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }
                    
                    <div class="row">
                        <!-- Account Information -->
                        <div class="col-md-6">
                            <h4 class="mb-3">@LanguageService.GetText("AccountInformation")</h4>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 40%">@LanguageService.GetText("Username")</th>
                                    <td>@Model.Username</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("Email")</th>
                                    <td>
                                        @Model.Email
                                        @if (Model.EmailConfirmed)
                                        {
                                            <i class="fas fa-check-circle text-success" title="@LanguageService.GetText("EmailConfirmed")"></i>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("Status")</th>
                                    <td>
                                        @if (Model.IsActive)
                                        {
                                            <span class="badge bg-success">@LanguageService.GetText("Active")</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">@LanguageService.GetText("Inactive")</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("PreferredLanguage")</th>
                                    <td>@(Model.PreferredLanguage == "tr" ? "Türkçe" : "English")</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("Roles")</th>
                                    <td>
                                        @if (Model.RoleNames.Any())
                                        {
                                            @string.Join(", ", Model.RoleNames)
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h4 class="mb-3">@LanguageService.GetText("PersonalInformation")</h4>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 40%">@LanguageService.GetText("FullName")</th>
                                    <td>@Model.FirstName @Model.LastName</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("DisplayName")</th>
                                    <td>@(Model.DisplayName ?? "-")</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("PhoneNumber")</th>
                                    <td>@(Model.PhoneNumber ?? "-")</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("DateOfBirth")</th>
                                    <td>@(Model.DateOfBirth?.ToString("dd.MM.yyyy") ?? "-")</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("Nationality")</th>
                                    <td>@(Model.Nationality ?? "-")</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("PlaceOfBirth")</th>
                                    <td>@(Model.PlaceOfBirth ?? "-")</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <hr />
                    
                    <div class="row">
                        <!-- Employment Information -->
                        <div class="col-md-6">
                            <h4 class="mb-3">@LanguageService.GetText("EmploymentInformation")</h4>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 40%">@LanguageService.GetText("EmployeeCode")</th>
                                    <td>@(Model.EmployeeCode ?? "-")</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("Department")</th>
                                    <td>@(Model.DepartmentName ?? "-")</td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("HireDate")</th>
                                    <td>@(Model.HireDate?.ToString("dd.MM.yyyy") ?? "-")</td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Maritime Documents -->
                        <div class="col-md-6">
                            <h4 class="mb-3">@LanguageService.GetText("MaritimeDocuments")</h4>
                            <table class="table table-sm">
                                <tr>
                                    <th style="width: 40%">@LanguageService.GetText("PassportNumber")</th>
                                    <td>
                                        @(Model.PassportNumber ?? "-")
                                        @if (Model.PassportExpiryDate.HasValue)
                                        {
                                            <br />
                                            <small class="text-muted">
                                                @LanguageService.GetText("ExpiryDate"): @Model.PassportExpiryDate.Value.ToString("dd.MM.yyyy")
                                                @if (Model.PassportExpiryDate.Value <= DateTime.UtcNow.Date)
                                                {
                                                    <span class="badge bg-danger">@LanguageService.GetText("Expired")</span>
                                                }
                                                else if (Model.PassportExpiryDate.Value <= DateTime.UtcNow.Date.AddDays(90))
                                                {
                                                    <span class="badge bg-warning">@LanguageService.GetText("ExpiringSoon")</span>
                                                }
                                            </small>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <th>@LanguageService.GetText("SeamanBookNumber")</th>
                                    <td>
                                        @(Model.SeamanBookNumber ?? "-")
                                        @if (Model.SeamanBookExpiryDate.HasValue)
                                        {
                                            <br />
                                            <small class="text-muted">
                                                @LanguageService.GetText("ExpiryDate"): @Model.SeamanBookExpiryDate.Value.ToString("dd.MM.yyyy")
                                                @if (Model.SeamanBookExpiryDate.Value <= DateTime.UtcNow.Date)
                                                {
                                                    <span class="badge bg-danger">@LanguageService.GetText("Expired")</span>
                                                }
                                                else if (Model.SeamanBookExpiryDate.Value <= DateTime.UtcNow.Date.AddDays(90))
                                                {
                                                    <span class="badge bg-warning">@LanguageService.GetText("ExpiringSoon")</span>
                                                }
                                            </small>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <a asp-controller="Documents" asp-action="CreateUserDocument" asp-route-userId="@Model.Id" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-file-upload"></i> @LanguageService.GetText("UploadDocument")
                                        </a>
                                        <a asp-controller="Documents" asp-action="UserDocuments" asp-route-userId="@Model.Id" 
                                           class="btn btn-info btn-sm">
                                            <i class="fas fa-folder-open"></i> @LanguageService.GetText("ViewAllDocuments")
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Vessel Assignments -->
                    @if (vesselAssignments.Any())
                    {
                        <hr />
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="mb-3">@LanguageService.GetText("VesselAssignments")</h4>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>@LanguageService.GetText("Vessel")</th>
                                                <th>@LanguageService.GetText("Rank")</th>
                                                <th>@LanguageService.GetText("SignOnDate")</th>
                                                <th>@LanguageService.GetText("SignOffDate")</th>
                                                <th>@LanguageService.GetText("Status")</th>
                                                <th>@LanguageService.GetText("Duration")</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var assignment in vesselAssignments)
                                            {
                                                <tr>
                                                    <td>
                                                        <a asp-controller="Ships" asp-action="Details" asp-route-id="@assignment.VesselId">
                                                            @assignment.Vessel?.VesselName
                                                        </a>
                                                    </td>
                                                    <td>@assignment.Rank?.Name</td>
                                                    <td>@assignment.SignOnDate.ToString("dd.MM.yyyy")</td>
                                                    <td>
                                                        @if (assignment.ActualSignOffDate.HasValue)
                                                        {
                                                            @assignment.ActualSignOffDate.Value.ToString("dd.MM.yyyy")
                                                        }
                                                        else if (assignment.ExpectedSignOffDate.HasValue)
                                                        {
                                                            <span class="text-muted">@assignment.ExpectedSignOffDate.Value.ToString("dd.MM.yyyy") (Expected)</span>
                                                        }
                                                        else
                                                        {
                                                            <span>-</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (assignment.Status == "Onboard")
                                                        {
                                                            <span class="badge bg-success">@LanguageService.GetText("Onboard")</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-secondary">@assignment.Status</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @{
                                                            var endDate = assignment.ActualSignOffDate ?? DateTime.UtcNow;
                                                            var duration = (int)(endDate - assignment.SignOnDate).TotalDays;
                                                        }
                                                        @duration @LanguageService.GetText("Days")
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    }
                    
                    <!-- Audit Information -->
                    <hr />
                    <div class="row">
                        <div class="col-md-12">
                            <small class="text-muted">
                                @LanguageService.GetText("CreatedDate"): @Model.CreatedDate.ToString("dd.MM.yyyy HH:mm")
                                @if (Model.ModifiedDate.HasValue)
                                {
                                    <span> | @LanguageService.GetText("ModifiedDate"): @Model.ModifiedDate.Value.ToString("dd.MM.yyyy HH:mm")</span>
                                }
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add any necessary JavaScript here
        });
    </script>
}