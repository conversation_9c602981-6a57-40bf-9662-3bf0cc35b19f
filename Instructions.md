# SMS Maritime Web - G<PERSON>ş<PERSON>rme <PERSON>, SMS Maritime Web projesinde standart geliştirme süreçlerini tanımlar.

## 1. Temel Prensipler

### 1.1 <PERSON><PERSON><PERSON>
- **Controller**: Sadece HTTP is<PERSON>, ser<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. İş mantığı içermez.
- **Service**: Tüm iş mantığı burada. Controller'lar DI ile erişir.
- **ViewModel**: View'a özel modeller. Entity'ler direkt View'a gönderilmez.
- **Model**: EF Core entity'leri, veritabanı tablolarını temsil eder.

### 1.2 Veritabanı Yönetimi
- Tüm şema değişiklikleri **EF Core Migrations** ile yapılır
- PostgreSQL kull<PERSON>ır, manuel tablo <PERSON>ği yasaktır
- DateTime değerleri UTC olarak saklanır
- Mevcut tablolara yeni alan e<PERSON> önce `shipmanagement_schema.sql` kontrol edilir

### 1.3 Teknoloji Kuralları
- Frontend: Bootstrap 5, jQuery, DataTables (React/Vue/Angular eklenmez)
- Backend: ASP.NET Core 8, Entity Framework Core
- Yeni kütüphane eklemeden önce takım onayı alınır
- Mevcut yapıya uygun çözümler üretilir

## 2. Kodlama Standartları

### 2.1 Genel Kurallar
```csharp
// Async metotlar için Task suffix kullanılır
public async Task<List<User>> GetUsersAsync()

// Repository pattern kullanılmaz, DbContext direkt kullanılır
// SOLID prensipleri uygulanır ama over-engineering'den kaçınılır
```

### 2.2 Model Tanımlamaları
```csharp
[Table("users")]  // PostgreSQL tablo adı
public class User
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
}
```

### 2.3 ViewModel Kullanımı
```csharp
// Entity'den ViewModel'e dönüşüm Select ile yapılır
var users = await _context.Users
    .Select(u => new UserViewModel
    {
        Id = u.Id,
        FullName = $"{u.FirstName} {u.LastName}"
    })
    .ToListAsync();
```

## 3. Geliştirme Ortamı

### 3.1 Gereksinimler
- .NET 8 SDK
- PostgreSQL 15+
- Visual Studio 2022 / VS Code / Rider

### 3.2 İlk Kurulum
```bash
# Bağımlılıkları yükle
dotnet restore

# appsettings.Development.json'da connection string düzenle
# Veritabanını oluştur
dotnet ef database update

# Uygulamayı başlat
dotnet run
```

## 4. Çözüm Patterns

### 4.1 Önbellekleme
```csharp
public class LanguageService : ILanguageService
{
    private readonly IMemoryCache _cache;
    
    public async Task<List<Language>> GetLanguagesAsync()
    {
        return await _cache.GetOrCreateAsync("languages", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
            return await _context.Languages.ToListAsync();
        });
    }
}
```

### 4.2 Feature Flags
```json
// appsettings.json
"Features": {
  "EnableNewDashboard": true,
  "ShowBetaFeatures": false
}
```

```csharp
// Kullanım
if (_configuration.GetValue<bool>("Features:EnableNewDashboard"))
{
    // Yeni özellik
}
```

### 4.3 Development Authentication Bypass
```csharp
// Sadece Development ortamında
if (app.Environment.IsDevelopment())
{
    // Test kullanıcısı otomatik login
}
```

## 5. Önemli Notlar

### 5.1 DateTime Yönetimi
- Tüm tarihler UTC olarak saklanır
- Form inputları UTC'ye çevrilir
- PostgreSQL timestamp with time zone kullanılır

### 5.2 Multi-Language Support
- `LanguageService` üzerinden tüm metinler yönetilir
- Yeni metin eklenirken `language_texts` tablosu güncellenir
- View'larda `@LanguageService.GetText("key")` kullanılır

### 5.3 Hata Yönetimi
- Try-catch blokları servis katmanında
- Global exception handler kullanılır
- Kullanıcıya anlamlı hata mesajları gösterilir

## 6. Git Workflow
- Feature branch'leri `feature/module-name` formatında
- Commit mesajları anlamlı ve İngilizce
- Pull request açmadan önce kod review

## 7. Performans
- LINQ sorgularında `AsNoTracking()` kullan (okuma işlemleri için)
- Eager loading ile N+1 probleminden kaçın
- Büyük veri setleri için pagination kullan

## 8. Güvenlik
- SQL Injection: EF Core parametreli sorgular kullanır
- XSS: Razor otomatik encode eder
- CSRF: Form'larda `@Html.AntiForgeryToken()`
- Authorization: `[Authorize]` attribute kullan

Bu doküman canlı bir dokümandır ve proje geliştikçe güncellenmelidir.