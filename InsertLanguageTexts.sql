-- Get language IDs
DO $$
DECLARE
    tr_id UUID;
    en_id UUID;
BEGIN
    SELECT id INTO tr_id FROM languages WHERE code = 'tr' LIMIT 1;
    SELECT id INTO en_id FROM languages WHERE code = 'en' LIMIT 1;
    
    -- Insert Turkish texts
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), tr_id, 'Login', '<PERSON><PERSON><PERSON>', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'Username', '<PERSON>llanıcı Adı', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'Password', 'Şifre', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'RememberMe', '<PERSON>i <PERSON>ı<PERSON>', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'SignIn', '<PERSON><PERSON><PERSON>', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'Logout', '<PERSON><PERSON><PERSON><PERSON><PERSON> Ya<PERSON>', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'EnterUsername', 'Kullanıcı adınızı girin', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'EnterPassword', 'Şifrenizi girin', 'Account', NOW()),
    (gen_random_uuid(), tr_id, 'Dashboard', 'Ana Sayfa', 'Common', NOW()),
    (gen_random_uuid(), tr_id, 'Welcome', 'Hoş Geldiniz', 'Common', NOW()),
    (gen_random_uuid(), tr_id, 'ShipManagementSystem', 'Gemi Yönetim Sistemi', 'Common', NOW()),
    (gen_random_uuid(), tr_id, 'WelcomeMessage', 'SMS Maritime gemi yönetim sistemine hoş geldiniz.', 'Common', NOW()),
    (gen_random_uuid(), tr_id, 'TotalShips', 'Toplam Gemi', 'Dashboard', NOW()),
    (gen_random_uuid(), tr_id, 'ActiveVoyages', 'Aktif Seferler', 'Dashboard', NOW()),
    (gen_random_uuid(), tr_id, 'PendingTasks', 'Bekleyen Görevler', 'Dashboard', NOW()),
    (gen_random_uuid(), tr_id, 'TotalCrew', 'Toplam Mürettebat', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;
    
    -- Insert English texts
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), en_id, 'Login', 'Login', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'Username', 'Username', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'Password', 'Password', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'RememberMe', 'Remember Me', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'SignIn', 'Sign In', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'Logout', 'Logout', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'EnterUsername', 'Enter your username', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'EnterPassword', 'Enter your password', 'Account', NOW()),
    (gen_random_uuid(), en_id, 'Dashboard', 'Dashboard', 'Common', NOW()),
    (gen_random_uuid(), en_id, 'Welcome', 'Welcome', 'Common', NOW()),
    (gen_random_uuid(), en_id, 'ShipManagementSystem', 'Ship Management System', 'Common', NOW()),
    (gen_random_uuid(), en_id, 'WelcomeMessage', 'Welcome to SMS Maritime ship management system.', 'Common', NOW()),
    (gen_random_uuid(), en_id, 'TotalShips', 'Total Ships', 'Dashboard', NOW()),
    (gen_random_uuid(), en_id, 'ActiveVoyages', 'Active Voyages', 'Dashboard', NOW()),
    (gen_random_uuid(), en_id, 'PendingTasks', 'Pending Tasks', 'Dashboard', NOW()),
    (gen_random_uuid(), en_id, 'TotalCrew', 'Total Crew', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;
END $$;