-- First check if menus already exist
SELECT COUNT(*) FROM menus;

-- Insert menu items if they don't exist
INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
SELECT 'Dashboard', 'Dashboard', '/Home/Index', 'fas fa-tachometer-alt', NULL, 1, true
WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Dashboard');

INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
SELECT 'Ship Management', 'ShipManagement', '#', 'fas fa-ship', NULL, 2, true
WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Ship Management');

INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
SELECT 'Crew Management', 'CrewManagement', '#', 'fas fa-users', NULL, 3, true
WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Crew Management');

INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
SELECT 'Voyage Management', 'VoyageManagement', '#', 'fas fa-route', NULL, 4, true
WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Voyage Management');

INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
SELECT 'Maintenance', 'MaintenanceManagement', '#', 'fas fa-tools', NULL, 5, true
WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Maintenance');

INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
SELECT 'Reports', 'Reports', '/Reports/Index', 'fas fa-chart-bar', NULL, 6, true
WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Reports');

INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
SELECT 'Settings', 'Settings', '#', 'fas fa-cog', NULL, 7, true
WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Settings');

-- Add submenu items
DO $$
DECLARE
    ship_menu_id INTEGER;
    crew_menu_id INTEGER;
    voyage_menu_id INTEGER;
    maintenance_menu_id INTEGER;
    settings_menu_id INTEGER;
BEGIN
    SELECT id INTO ship_menu_id FROM menus WHERE name = 'Ship Management' LIMIT 1;
    SELECT id INTO crew_menu_id FROM menus WHERE name = 'Crew Management' LIMIT 1;
    SELECT id INTO voyage_menu_id FROM menus WHERE name = 'Voyage Management' LIMIT 1;
    SELECT id INTO maintenance_menu_id FROM menus WHERE name = 'Maintenance' LIMIT 1;
    SELECT id INTO settings_menu_id FROM menus WHERE name = 'Settings' LIMIT 1;
    
    -- Insert submenu items if they don't exist
    INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
    SELECT 'Ships', 'Ships', '/Ships/Index', 'fas fa-anchor', ship_menu_id, 1, true
    WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Ships' AND parent_id = ship_menu_id);
    
    INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
    SELECT 'Crew', 'Crew', '/Crew/Index', 'fas fa-user-tie', crew_menu_id, 1, true
    WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Crew' AND parent_id = crew_menu_id);
    
    INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
    SELECT 'Voyages', 'Voyages', '/Voyages/Index', 'fas fa-compass', voyage_menu_id, 1, true
    WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Voyages' AND parent_id = voyage_menu_id);
    
    INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
    SELECT 'Maintenance Tasks', 'Maintenance', '/Maintenance/Index', 'fas fa-wrench', maintenance_menu_id, 1, true
    WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Maintenance Tasks' AND parent_id = maintenance_menu_id);
    
    INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
    SELECT 'Users', 'Users', '/Users/<USER>', 'fas fa-user', settings_menu_id, 1, true
    WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Users' AND parent_id = settings_menu_id);
    
    INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
    SELECT 'Roles', 'Roles', '/Roles/Index', 'fas fa-user-tag', settings_menu_id, 2, true
    WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'Roles' AND parent_id = settings_menu_id);
    
    INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) 
    SELECT 'System Settings', 'SystemSettings', '/Settings/System', 'fas fa-sliders-h', settings_menu_id, 3, true
    WHERE NOT EXISTS (SELECT 1 FROM menus WHERE name = 'System Settings' AND parent_id = settings_menu_id);
END $$;

-- Add menu texts to language_texts
DO $$
DECLARE
    tr_id UUID;
    en_id UUID;
BEGIN
    SELECT id INTO tr_id FROM languages WHERE code = 'tr' LIMIT 1;
    SELECT id INTO en_id FROM languages WHERE code = 'en' LIMIT 1;
    
    -- Turkish menu texts
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), tr_id, 'ShipManagement', 'Gemi Yönetimi', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Ships', 'Gemiler', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'CrewManagement', 'Mürettebat Yönetimi', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Crew', 'Mürettebat', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'VoyageManagement', 'Sefer Yönetimi', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Voyages', 'Seferler', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'MaintenanceManagement', 'Bakım Yönetimi', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Maintenance', 'Bakım', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Reports', 'Raporlar', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Settings', 'Ayarlar', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Users', 'Kullanıcılar', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'Roles', 'Roller', 'Menu', NOW()),
    (gen_random_uuid(), tr_id, 'SystemSettings', 'Sistem Ayarları', 'Menu', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;
    
    -- English menu texts
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), en_id, 'ShipManagement', 'Ship Management', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Ships', 'Ships', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'CrewManagement', 'Crew Management', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Crew', 'Crew', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'VoyageManagement', 'Voyage Management', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Voyages', 'Voyages', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'MaintenanceManagement', 'Maintenance Management', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Maintenance', 'Maintenance', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Reports', 'Reports', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Settings', 'Settings', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Users', 'Users', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'Roles', 'Roles', 'Menu', NOW()),
    (gen_random_uuid(), en_id, 'SystemSettings', 'System Settings', 'Menu', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;
END $$;