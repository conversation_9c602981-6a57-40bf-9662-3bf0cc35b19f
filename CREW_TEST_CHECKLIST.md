# SMS Maritime Web - Crew Management Test Checklist

## <PERSON><PERSON>, SMS Maritime Web uygulamasının Crew (Mürettebat) modülü için kapsamlı test senaryolarını içerir. Test sürecinde Add (Ekleme), Edit (Düzenleme) ve View (Görüntüleme) sayfalarının tüm fonksiyonları kontrol edilecektir. Add sayfası referans alınarak diğer sayfalar senkronize edilecektir.

**Test Başlangıç Tarihi:** 2025-07-06  
**Test URL:** http://localhost:5116/Crew  
**Test Kullanıcısı:** admin / admin123

## 1. Crew Add (Ekleme) Sayfası Testleri

### 1.1 Sayfa Erişimi ve Görünüm
- [x] Crew listesi sayfasından "Add New" butonuna tıklama
- [x] Create Crew Member sayfasının açılma<PERSON>ı
- [x] <PERSON><PERSON> başlığının doğru görün<PERSON><PERSON> ("Create Crew Member")
- [x] Tüm form bölümlerinin görünür olması:
  - [x] Personal Information
  - [x] Contact Information
  - [x] Maritime Documents
  - [x] Status

### 1.2 Personal Information (Kişisel Bilgiler) Alanları
- [x] **Employee Code** (Zorunlu Alan)
  - [x] Boş bırakıldığında hata mesajı kontrolü
  - [ ] Benzersiz değer kontrolü
  - [ ] Özel karakter ve format kontrolü
- [x] **First Name** (Zorunlu Alan)
  - [x] Boş bırakıldığında hata mesajı kontrolü
  - [ ] Maksimum karakter limiti kontrolü
  - [ ] Özel karakter kontrolü
- [x] **Last Name** (Zorunlu Alan)
  - [x] Boş bırakıldığında hata mesajı kontrolü
  - [ ] Maksimum karakter limiti kontrolü
  - [ ] Özel karakter kontrolü
- [ ] **Date of Birth**
  - [ ] Tarih seçici (date picker) çalışması
  - [ ] Gelecek tarih engelleme kontrolü
  - [ ] Yaş sınırı kontrolü (min/max)
- [ ] **Nationality**
  - [ ] Dropdown listesinin çalışması
  - [ ] Tüm ülkelerin listelenmesi
  - [ ] Varsayılan değer kontrolü
- [ ] **Place of Birth**
  - [ ] Serbest metin girişi
  - [ ] Maksimum karakter limiti
- [ ] **Department**
  - [ ] Dropdown listesinin çalışması
  - [ ] Departmanların listelenmesi (Deck, Engine, Catering)
  - [ ] Varsayılan değer kontrolü
- [ ] **Hire Date**
  - [ ] Tarih seçici çalışması
  - [ ] Gelecek tarih girişi kontrolü

### 1.3 Contact Information (İletişim Bilgileri) Alanları
- [x] **Email** (Zorunlu Alan)
  - [x] Boş bırakıldığında hata mesajı
  - [x] Email format validasyonu
  - [ ] Benzersiz email kontrolü
- [ ] **Phone Number**
  - [ ] Telefon format validasyonu
  - [ ] Uluslararası format desteği
- [ ] **Display Name**
  - [ ] Otomatik oluşturma özelliği
  - [ ] Manuel giriş imkanı
  - [ ] Placeholder metin kontrolü

### 1.4 Maritime Documents (Denizcilik Belgeleri) Alanları
- [ ] **Passport Number**
  - [ ] Format validasyonu
  - [ ] Alfanumerik karakter kontrolü
- [ ] **Passport Expiry Date**
  - [ ] Tarih seçici çalışması
  - [ ] Geçmiş tarih uyarısı
- [ ] **Seaman Book Number**
  - [ ] Format validasyonu
  - [ ] Benzersizlik kontrolü
- [ ] **Seaman Book Expiry Date**
  - [ ] Tarih seçici çalışması
  - [ ] Geçmiş tarih uyarısı

### 1.5 Status (Durum) Alanları
- [ ] **Is Active** checkbox
  - [ ] Varsayılan olarak işaretli gelme
  - [ ] İşaretleme/kaldırma fonksiyonu

### 1.6 Form İşlemleri
- [x] **Save** butonu
  - [x] Tüm zorunlu alanlar dolu iken kayıt
  - [x] Başarılı kayıt sonrası yönlendirme
  - [x] Başarı mesajı gösterimi ("Crew member created successfully.")
- [x] **Back to List** butonu
  - [x] Liste sayfasına dönüş kontrolü
  - [ ] Kaydedilmemiş veri uyarısı

### 1.7 Validasyon Testleri
- [ ] Client-side validasyon kontrolleri
- [ ] Server-side validasyon kontrolleri
- [ ] Hata mesajlarının dil desteği
- [ ] Birden fazla hata durumunda tüm hataların gösterimi

## 2. Crew Edit (Düzenleme) Sayfası Testleri

### 2.1 Sayfa Erişimi
- [x] Listeden Edit ikonuna tıklama
- [x] Doğru crew kaydının yüklenmesi
- [x] URL'de ID parametresi kontrolü

### 2.2 Form Alanları Senkronizasyonu
- [x] Tüm alanların Add sayfası ile aynı olması:
  - [x] Personal Information bölümü
  - [x] Contact Information bölümü
  - [x] Maritime Documents bölümü
  - [x] Status bölümü (Düzeltildi - Status başlığı eklendi)

### 2.3 Mevcut Veri Yükleme
- [x] Tüm alanların mevcut verilerle doldurulması
- [x] Tarih alanlarının doğru formatta gösterimi
- [x] Dropdown seçimlerinin doğru yüklenmesi (Department seçili geliyor)

### 2.4 Güncelleme İşlemleri
- [ ] Veri değişikliği yapma
- [ ] Save butonu ile güncelleme
- [ ] Başarılı güncelleme mesajı
- [ ] Değişikliklerin veritabanına yansıması

## 3. Crew Details (Görüntüleme) Sayfası Testleri

### 3.1 Sayfa Erişimi
- [x] Listeden View ikonuna tıklama
- [x] Doğru crew kaydının görüntülenmesi
- [x] Salt okunur mod kontrolü

### 3.2 Bilgi Görüntüleme
- [x] Tüm bilgilerin Add/Edit sayfası ile tutarlı gösterimi:
  - [x] Personal Information (Status bu bölümde gösteriliyor)
  - [x] Contact Information
  - [x] Maritime Documents
  - [x] Assignment History (Ek bölüm)

### 3.3 Sayfa İşlemleri
- [x] Edit butonunun çalışması
- [x] Back to List butonunun çalışması
- [ ] Print/Export özellikleri (varsa)

## 4. Crew List (Liste) Sayfası Testleri

### 4.1 Liste Görünümü
- [ ] Tüm crew kayıtlarının listelenmesi
- [ ] Sütun başlıklarının doğru gösterimi
- [ ] Sayfalama (pagination) çalışması

### 4.2 Liste İşlemleri
- [ ] Arama fonksiyonu
- [ ] Sıralama özellikleri
- [ ] Filtreleme seçenekleri
- [ ] Toplu işlem özellikleri (varsa)

## 5. Genel Fonksiyonel Testler

### 5.1 Yetkilendirme
- [ ] Yetkisiz kullanıcı erişim kontrolü
- [ ] Rol bazlı erişim kontrolleri

### 5.2 Performans
- [ ] Sayfa yükleme süreleri
- [ ] Form gönderim süreleri
- [ ] Liste yükleme performansı

### 5.3 Tarayıcı Uyumluluğu
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

### 5.4 Responsive Tasarım
- [ ] Mobil görünüm kontrolü
- [ ] Tablet görünüm kontrolü
- [ ] Desktop görünüm kontrolü

## 6. Entegrasyon Testleri

### 6.1 Vessel Assignment (Gemi Ataması)
- [ ] Crew'in gemiye atanması
- [ ] Atama geçmişi görüntüleme
- [ ] Mevcut atamaların kontrolü

### 6.2 Raporlama
- [ ] Crew raporlarında görünüm
- [ ] İstatistiksel verilere yansıma

## 7. Hata Durumları ve Edge Case'ler

### 7.1 Veri Bütünlüğü
- [ ] Duplicate kayıt engelleme
- [ ] Referans bütünlüğü kontrolleri
- [ ] Soft delete işlemleri

### 7.2 Concurrent Access
- [ ] Aynı anda birden fazla kullanıcı erişimi
- [ ] Version conflict kontrolleri

## Test Sonuçları ve Notlar

### Bulunan Hatalar:
1. **2025-07-06** - Edit sayfasında Status bölümü başlığı eksikti - **DÜZELTILDI**
2. **2025-07-06** - Yeni eklenen crew üyeleri listede görünmüyor (Seaman Book Number veya vessel assignment olmadığı için)

### İyileştirme Önerileri:
1. Crew listesinde tüm crew üyelerinin görünmesi için filtreleme kriteri gözden geçirilmeli
2. Display Name alanı için otomatik oluşturma özelliği frontend'de de implement edilebilir
3. Tarih alanlarında takvim widget'ı kullanılabilir
4. Passport ve Seaman Book expiry date'leri için yaklaşan tarih uyarıları eklenebilir

### Test Tamamlanma Durumu:
- [x] Add Sayfası Testleri Tamamlandı (Temel testler)
- [x] Edit Sayfası Testleri Tamamlandı (Temel testler)
- [x] View Sayfası Testleri Tamamlandı
- [ ] Liste Sayfası Testleri Tamamlandı (Kısmen)
- [ ] Entegrasyon Testleri Tamamlandı

### Senkronizasyon Durumu:
- **Add ve Edit sayfaları**: Tamamen senkron (Status başlığı düzeltmesi yapıldı)
- **Details sayfası**: Farklı layout ancak tüm alanlar mevcut, ek olarak Assignment History bölümü var

**Son Güncelleme:** 2025-07-06