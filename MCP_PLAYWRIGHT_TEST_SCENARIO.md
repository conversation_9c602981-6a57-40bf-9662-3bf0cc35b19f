# MCP Playwright Test Senaryosu - SMS Maritime Web

Bu dokümanda MCP Playwright araçları kullanılarak SMS Maritime Web uygulamasının nasıl test edileceği gösterilmektedir.

## Test Senaryoları

### 1. Ana Sayfa Testi

```
1. mcp__playwright__browser_navigate
   URL: http://localhost:5001 (veya file:///path/to/simple-test.html)

2. mcp__playwright__browser_snapshot
   - Ana sayfanın yüklendiğini doğrula
   - Navigasyon menüsünün varlığını kontrol et

3. mcp__playwright__browser_take_screenshot
   filename: homepage.png
   - Ana sayfanın ekran görünt<PERSON><PERSON><PERSON><PERSON><PERSON> al
```

### 2. Ships (Gemiler) Sayfası Testi

```
1. mcp__playwright__browser_click
   element: Ships navigation link
   ref: [ships link ref from snapshot]

2. mcp__playwright__browser_wait_for
   text: "Ships Management"
   - <PERSON><PERSON><PERSON><PERSON> yüklen<PERSON>ini bekle

3. mcp__playwright__browser_snapshot
   - <PERSON><PERSON><PERSON> tablos<PERSON>un varlığını kontrol et

4. mcp__playwright__browser_type
   element: Search input
   ref: [search input ref]
   text: "Maritime"
   - Arama kutusuna yazı yaz

5. mcp__playwright__browser_click
   element: Search button
   ref: [search button ref]

6. mcp__playwright__browser_wait_for
   time: 1
   - Arama sonuçlarını bekle

7. mcp__playwright__browser_take_screenshot
   filename: search-results.png
```

### 3. Gemi Detayları Testi

```
1. mcp__playwright__browser_click
   element: View Details link
   ref: [first details link ref]

2. mcp__playwright__browser_snapshot
   - Detay sayfasının açıldığını kontrol et

3. mcp__playwright__browser_take_screenshot
   filename: ship-details.png
```

### 4. Login (Giriş) Testi

```
1. mcp__playwright__browser_click
   element: Login navigation link
   ref: [login link ref]

2. mcp__playwright__browser_type
   element: Email input
   ref: [email input ref]
   text: "<EMAIL>"

3. mcp__playwright__browser_type
   element: Password input
   ref: [password input ref]
   text: "password123"

4. mcp__playwright__browser_click
   element: Login button
   ref: [login button ref]

5. mcp__playwright__browser_wait_for
   text: "Login successful"
   - Başarılı giriş mesajını bekle

6. mcp__playwright__browser_take_screenshot
   filename: login-success.png
```

### 5. Yeni Gemi Ekleme Testi

```
1. mcp__playwright__browser_click
   element: Ships navigation link
   ref: [ships link ref]

2. mcp__playwright__browser_click
   element: Add New Ship button
   ref: [add ship button ref]

3. mcp__playwright__browser_snapshot
   - Yeni geminin eklendiğini kontrol et
```

### 6. Responsive Design Testi

```
1. mcp__playwright__browser_resize
   width: 375
   height: 667
   - Mobile görünüm

2. mcp__playwright__browser_take_screenshot
   filename: mobile-view.png

3. mcp__playwright__browser_resize
   width: 768
   height: 1024
   - Tablet görünüm

4. mcp__playwright__browser_take_screenshot
   filename: tablet-view.png

5. mcp__playwright__browser_resize
   width: 1920
   height: 1080
   - Desktop görünüm
```

### 7. Console ve Network Kontrolü

```
1. mcp__playwright__browser_console_messages
   - Console hatalarını kontrol et

2. mcp__playwright__browser_network_requests
   - Network isteklerini kontrol et
```

### 8. Otomatik Test Script Oluşturma

```
mcp__playwright__browser_generate_playwright_test
name: "SMS Maritime Full Test"
description: "Complete end-to-end test for SMS Maritime Web application"
steps: [
  "Navigate to homepage",
  "Click Ships menu",
  "Search for Maritime",
  "View ship details",
  "Navigate to Login",
  "Fill login form",
  "Submit login",
  "Add new ship"
]
```

## Test Sonuç Raporu

Test tamamlandıktan sonra:
- Tüm ekran görüntüleri kaydedilir
- Console hataları kontrol edilir
- Network istekleri analiz edilir
- Responsive tasarım doğrulanır
- Kullanıcı etkileşimleri test edilir

## Örnek MCP Komutları

```bash
# Ana sayfayı aç
mcp__playwright__browser_navigate --url "file:///Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/simple-test.html"

# Sayfanın snapshot'ını al
mcp__playwright__browser_snapshot

# Ships linkine tıkla
mcp__playwright__browser_click --element "Ships navigation link" --ref "e7"

# Arama yap
mcp__playwright__browser_type --element "Search input" --ref "e15" --text "Maritime"

# Ekran görüntüsü al
mcp__playwright__browser_take_screenshot --filename "test-result.png"
```

## Notlar

- Test sırasında alert/dialog görünürse `mcp__playwright__browser_handle_dialog` kullanın
- Her test adımından sonra `mcp__playwright__browser_snapshot` ile durumu kontrol edin
- Kritik noktalarda `mcp__playwright__browser_take_screenshot` ile görsel kayıt alın
- `mcp__playwright__browser_wait_for` ile asenkron işlemleri bekleyin
- Test sonunda `mcp__playwright__browser_close` ile tarayıcıyı kapatın