

-- Create menus table
CREATE TABLE IF NOT EXISTS menus (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    text_key VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL DEFAULT '',
    icon VARCHAR(100) NOT NULL DEFAULT '',
    parent_id INTEGER,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    FOREIGN KEY (parent_id) REFERENCES menus(id) ON DELETE CASCADE
);

-- Create menu_roles table
CREATE TABLE IF NOT EXISTS menu_roles (
    menu_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    PRIMARY KEY (menu_id, role_id),
    FOREIGN KEY (menu_id) REFERENCES menus(id) ON DELETE CASCADE
);

-- Insert default roles
INSERT INTO roles (name, description) VALUES 
('Admin', 'Administrator'),
('User', 'Standard User'),
('Manager', 'Manager')
ON CONFLICT (name) DO NOTHING;

-- Insert default languages
INSERT INTO languages (code, name, is_active) VALUES 
('tr', 'Türkçe', true),
('en', 'English', true)
ON CONFLICT (code) DO NOTHING;

-- Insert default language texts
INSERT INTO language_texts (language_id, text_key, text_value) VALUES 
-- Turkish texts
(1, 'Login', 'Giriş Yap'),
(1, 'Username', 'Kullanıcı Adı'),
(1, 'Password', 'Şifre'),
(1, 'RememberMe', 'Beni Hatırla'),
(1, 'SignIn', 'Giriş Yap'),
(1, 'Logout', 'Çıkış Yap'),
(1, 'EnterUsername', 'Kullanıcı adınızı girin'),
(1, 'EnterPassword', 'Şifrenizi girin'),
(1, 'Dashboard', 'Ana Sayfa'),
(1, 'Welcome', 'Hoş Geldiniz'),
(1, 'ShipManagement', 'Gemi Yönetimi'),
(1, 'Ships', 'Gemiler'),
(1, 'Crew', 'Mürettebat'),
(1, 'CrewManagement', 'Mürettebat Yönetimi'),
(1, 'Voyages', 'Seferler'),
(1, 'VoyageManagement', 'Sefer Yönetimi'),
(1, 'Maintenance', 'Bakım'),
(1, 'MaintenanceManagement', 'Bakım Yönetimi'),
(1, 'Reports', 'Raporlar'),
(1, 'Settings', 'Ayarlar'),
(1, 'Users', 'Kullanıcılar'),
(1, 'UserManagement', 'Kullanıcı Yönetimi'),
(1, 'Roles', 'Roller'),
(1, 'SystemSettings', 'Sistem Ayarları'),
(1, 'ShipManagementSystem', 'Gemi Yönetim Sistemi'),
(1, 'WelcomeMessage', 'SMS Maritime gemi yönetim sistemine hoş geldiniz.'),
(1, 'TotalShips', 'Toplam Gemi'),
(1, 'ActiveVoyages', 'Aktif Seferler'),
(1, 'PendingTasks', 'Bekleyen Görevler'),
(1, 'TotalCrew', 'Toplam Mürettebat'),
-- English texts
(2, 'Login', 'Login'),
(2, 'Username', 'Username'),
(2, 'Password', 'Password'),
(2, 'RememberMe', 'Remember Me'),
(2, 'SignIn', 'Sign In'),
(2, 'Logout', 'Logout'),
(2, 'EnterUsername', 'Enter your username'),
(2, 'EnterPassword', 'Enter your password'),
(2, 'Dashboard', 'Dashboard'),
(2, 'Welcome', 'Welcome'),
(2, 'ShipManagement', 'Ship Management'),
(2, 'Ships', 'Ships'),
(2, 'Crew', 'Crew'),
(2, 'CrewManagement', 'Crew Management'),
(2, 'Voyages', 'Voyages'),
(2, 'VoyageManagement', 'Voyage Management'),
(2, 'Maintenance', 'Maintenance'),
(2, 'MaintenanceManagement', 'Maintenance Management'),
(2, 'Reports', 'Reports'),
(2, 'Settings', 'Settings'),
(2, 'Users', 'Users'),
(2, 'UserManagement', 'User Management'),
(2, 'Roles', 'Roles'),
(2, 'SystemSettings', 'System Settings'),
(2, 'ShipManagementSystem', 'Ship Management System'),
(2, 'WelcomeMessage', 'Welcome to SMS Maritime ship management system.'),
(2, 'TotalShips', 'Total Ships'),
(2, 'ActiveVoyages', 'Active Voyages'),
(2, 'PendingTasks', 'Pending Tasks'),
(2, 'TotalCrew', 'Total Crew')
ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

-- Insert default menu items
INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) VALUES 
-- Main menu items
('Dashboard', 'Dashboard', '/Home/Index', 'fas fa-tachometer-alt', NULL, 1, true),
('Ship Management', 'ShipManagement', '#', 'fas fa-ship', NULL, 2, true),
('Crew Management', 'CrewManagement', '#', 'fas fa-users', NULL, 3, true),
('Voyage Management', 'VoyageManagement', '#', 'fas fa-route', NULL, 4, true),
('Maintenance', 'MaintenanceManagement', '#', 'fas fa-tools', NULL, 5, true),
('Reports', 'Reports', '/Reports/Index', 'fas fa-chart-bar', NULL, 6, true),
('Settings', 'Settings', '#', 'fas fa-cog', NULL, 7, true);

-- Insert submenu items
INSERT INTO menus (name, text_key, url, icon, parent_id, display_order, is_active) VALUES 
-- Ship Management submenu
('Ships', 'Ships', '/Ships/Index', 'fas fa-anchor', 2, 1, true),
-- Crew Management submenu
('Crew', 'Crew', '/Crew/Index', 'fas fa-user-tie', 3, 1, true),
-- Voyage Management submenu
('Voyages', 'Voyages', '/Voyages/Index', 'fas fa-compass', 4, 1, true),
-- Maintenance submenu
('Maintenance', 'Maintenance', '/Maintenance/Index', 'fas fa-wrench', 5, 1, true),
-- Settings submenu
('Users', 'Users', '/Users/<USER>', 'fas fa-user', 7, 1, true),
('Roles', 'Roles', '/Roles/Index', 'fas fa-user-tag', 7, 2, true),
('System Settings', 'SystemSettings', '/Settings/System', 'fas fa-sliders-h', 7, 3, true);