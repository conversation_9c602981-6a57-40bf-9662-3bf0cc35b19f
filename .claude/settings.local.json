{"permissions": {"allow": ["Bash(grep:*)", "Bash(psql:*)", "Bash(rg:*)", "Bash(ls:*)", "Bash(dotnet add:*)", "Bash(pwsh:*)", "Bash(dotnet tool install:*)", "Bash(export PATH=\"$PATH:/Users/<USER>/.dotnet/tools\")", "<PERSON><PERSON>(playwright install)", "Bash(dotnet build)", "<PERSON><PERSON>(mkdir:*)", "Bash(dotnet new:*)", "Bash(rm:*)", "Bash(dotnet restore:*)", "<PERSON><PERSON>(playwright install:*)", "Bash(dotnet test:*)", "mcp__playwright__browser_navigate", "<PERSON><PERSON>(curl:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_close", "mcp__playwright__browser_press_key", "mcp__playwright__browser_wait_for", "<PERSON><PERSON>(pkill:*)", "mcp__playwright__browser_tab_close", "mcp__playwright__browser_tab_new", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_generate_playwright_test", "mcp__playwright__browser_install", "mcp__playwright__browser_tab_list", "Bash(dotnet ef migrations add:*)", "Bash(dotnet ef database:*)", "Bash(dotnet ef migrations:*)", "Bash(PGPASSWORD=935400 psql -h localhost -U dbadmin -d sms_maritime -f add_missing_columns.sql)", "<PERSON><PERSON>(dotnet script:*)", "Bash(dotnet run:*)", "<PERSON><PERSON>(true)", "Bash(kill:*)", "mcp__playwright__browser_select_option", "mcp__playwright__browser_navigate_back", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(touch:*)", "Bash(cp:*)", "Bash(PGPASSWORD=935400 psql -h localhost -U dbadmin -d sms_maritime -c \"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = ''users'' AND column_name IN (''created_date'', ''modified_date'', ''deleted_date'', ''date_of_birth'', ''hire_date'', ''passport_expiry_date'', ''seaman_book_expiry_date'', ''last_password_change'') ORDER BY column_name;\")", "Bash(PGPASSWORD=935400 psql -h localhost -U dbadmin -d sms_maritime -f UpdateUsersTableDateTimeColumns.sql)", "Bash(PGPASSWORD=935400 psql:*)", "Bash(dotnet clean:*)", "Bash(PGPASSWORD=superSecure2025! psql:*)", "Bash(dotnet build:*)", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\\dt *translation*\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\nCREATE TABLE department_type_translations (\n    department_type_translation_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),\n    department_type_id uuid NOT NULL REFERENCES department_types(department_type_id) ON DELETE CASCADE,\n    language_id uuid NOT NULL REFERENCES languages(id),\n    name varchar(150) NOT NULL,\n    description varchar(500),\n    UNIQUE(department_type_id, language_id)\n);\n\nCREATE TABLE department_translations (\n    department_translation_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),\n    department_id uuid NOT NULL REFERENCES departments(department_id) ON DELETE CASCADE,\n    language_id uuid NOT NULL REFERENCES languages(id),\n    name varchar(150) NOT NULL,\n    UNIQUE(department_id, language_id)\n);\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\\dt *department*\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\\d+ departments\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\\dt department*\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\\dt *language*\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\nCREATE TABLE department_type_translations (\n    department_type_translation_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),\n    department_type_id uuid NOT NULL REFERENCES department_types(department_type_id) ON DELETE CASCADE,\n    language_id uuid NOT NULL REFERENCES languages(id),\n    name varchar(150) NOT NULL,\n    description varchar(500),\n    UNIQUE(department_type_id, language_id)\n);\n\nCREATE TABLE department_translations (\n    department_translation_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),\n    department_id uuid NOT NULL REFERENCES departments_new(department_id) ON DELETE CASCADE,\n    language_id uuid NOT NULL REFERENCES languages(id),\n    name varchar(150) NOT NULL,\n    UNIQUE(department_id, language_id)\n);\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\nINSERT INTO department_types (name, description, is_active) VALUES\n(''Operational'', ''Operational maritime departments'', true),\n(''Technical'', ''Technical and engineering departments'', true),\n(''Administrative'', ''Administrative and support departments'', true),\n(''Commercial'', ''Commercial and business departments'', true),\n(''Safety & Security'', ''Safety and security related departments'', true);\")", "Bash(PGPASSWORD=postgres psql -h localhost -U postgres -d shipmanagement -c \"\n-- İng<PERSON>zce ve Türkçe çeviriler ekle\nINSERT INTO department_type_translations (department_type_id, language_id, name, description) \nSELECT dt.department_type_id, l.id, \n    CASE \n        WHEN dt.name = ''Operational'' AND l.code = ''en'' THEN ''Operational''\n        WHEN dt.name = ''Operational'' AND l.code = ''tr'' THEN ''Operasyonel''\n        WHEN dt.name = ''Technical'' AND l.code = ''en'' THEN ''Technical''\n        WHEN dt.name = ''Technical'' AND l.code = ''tr'' THEN ''Teknik''\n        WHEN dt.name = ''Administrative'' AND l.code = ''en'' THEN ''Administrative''\n        WHEN dt.name = ''Administrative'' AND l.code = ''tr'' THEN ''İdari''\n        WHEN dt.name = ''Commercial'' AND l.code = ''en'' THEN ''Commercial''\n        WHEN dt.name = ''Commercial'' AND l.code = ''tr'' THEN ''Ticari''\n        WHEN dt.name = ''Safety & Security'' AND l.code = ''en'' THEN ''Safety & Security''\n        WHEN dt.name = ''Safety & Security'' AND l.code = ''tr'' THEN ''Emniyet ve Güvenlik''\n    END,\n    CASE \n        WHEN dt.name = ''Operational'' AND l.code = ''en'' THEN ''Operational maritime departments''\n        WHEN dt.name = ''Operational'' AND l.code = ''tr'' THEN ''Operasyonel denizcilik departmanları''\n        WHEN dt.name = ''Technical'' AND l.code = ''en'' THEN ''Technical and engineering departments''\n        WHEN dt.name = ''Technical'' AND l.code = ''tr'' THEN ''Teknik ve mühendislik departmanları''\n        WHEN dt.name = ''Administrative'' AND l.code = ''en'' THEN ''Administrative and support departments''\n        WHEN dt.name = ''Administrative'' AND l.code = ''tr'' THEN ''İdari ve destek departmanları''\n        WHEN dt.name = ''Commercial'' AND l.code = ''en'' THEN ''Commercial and business departments''\n        WHEN dt.name = ''Commercial'' AND l.code = ''tr'' THEN ''Ticari ve iş departmanları''\n        WHEN dt.name = ''Safety & Security'' AND l.code = ''en'' THEN ''Safety and security related departments''\n        WHEN dt.name = ''Safety & Security'' AND l.code = ''tr'' THEN ''Emniyet ve güvenlik ile ilgili departmanlar''\n    END\nFROM department_types dt\nCROSS JOIN languages l\nWHERE l.code IN (''en'', ''tr'');\")", "Bash(PGPASSWORD=postgres psql:*)"], "deny": []}}