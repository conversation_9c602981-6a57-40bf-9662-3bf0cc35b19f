-- Add remaining missing columns to vessels table
DO $$ 
BEGIN
    -- Add deadweight column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'deadweight') THEN
        ALTER TABLE vessels ADD COLUMN deadweight DECIMAL(10,2);
    END IF;

    -- Add draft column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'draft') THEN
        ALTER TABLE vessels ADD COLUMN draft DECIMAL(10,2);
    END IF;

    -- Add engine_power column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'engine_power') THEN
        ALTER TABLE vessels ADD COLUMN engine_power DECIMAL(10,2);
    END IF;

    -- Add engine_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'engine_type') THEN
        ALTER TABLE vessels ADD COLUMN engine_type VARCHAR(100);
    END IF;

    -- Add flag column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'flag') THEN
        ALTER TABLE vessels ADD COLUMN flag VARCHAR(100);
    END IF;

    -- Add is_owned column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'is_owned') THEN
        ALTER TABLE vessels ADD COLUMN is_owned BOOLEAN DEFAULT FALSE;
    END IF;

    -- Add length_overall column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'length_overall') THEN
        ALTER TABLE vessels ADD COLUMN length_overall DECIMAL(10,2);
    END IF;

    -- Add main_engine_maker column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'main_engine_maker') THEN
        ALTER TABLE vessels ADD COLUMN main_engine_maker VARCHAR(200);
    END IF;

    -- Add main_engine_model column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'main_engine_model') THEN
        ALTER TABLE vessels ADD COLUMN main_engine_model VARCHAR(200);
    END IF;

    -- Add main_engine_power_kw column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'main_engine_power_kw') THEN
        ALTER TABLE vessels ADD COLUMN main_engine_power_kw DECIMAL(10,2);
    END IF;
END $$;