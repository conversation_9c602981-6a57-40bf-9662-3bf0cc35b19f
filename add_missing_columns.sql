-- Add missing columns to users table
DO $$ 
BEGIN
    -- Add password_reset_code column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'users' 
                  AND column_name = 'password_reset_code') THEN
        ALTER TABLE users ADD COLUMN password_reset_code VARCHAR(255);
    END IF;

    -- Add is_deleted column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'users' 
                  AND column_name = 'is_deleted') THEN
        ALTER TABLE users ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Add fleets table if it doesn't exist
CREATE TABLE IF NOT EXISTS fleets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fleet_code VARCHAR(50) NOT NULL,
    fleet_name VARCHAR(200) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUI<PERSON>,
    modified_date TIMESTAMP WITH TIME ZONE,
    modified_by UUID
);

-- Add fleet_id column to vessels table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'vessels' 
                  AND column_name = 'fleet_id') THEN
        ALTER TABLE vessels ADD COLUMN fleet_id UUID;
        ALTER TABLE vessels ADD CONSTRAINT fk_vessels_fleet 
            FOREIGN KEY (fleet_id) REFERENCES fleets(id);
    END IF;
END $$;