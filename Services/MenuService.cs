using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;

namespace SMS_Maritime_Web.Services;

public class MenuService : IMenuService
{
    private readonly ApplicationDbContext _context;

    public MenuService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Menu>> GetMenusByUserAsync(string userId)
    {
        if (!Guid.TryParse(userId, out Guid userGuid))
        {
            return new List<Menu>();
        }

        var userRoles = await _context.UserRoles
            .Where(ur => ur.UserId == userGuid && ur.IsActive && (ur.ValidUntil == null || ur.ValidUntil > DateTime.UtcNow))
            .Select(ur => ur.RoleId)
            .ToListAsync();

        var menus = await _context.Menus
            .Include(m => m.SubMenus)
            .Include(m => m.MenuRoles)
            .Where(m => m.IsActive && (m.MenuRoles.Any(mr => userRoles.Contains(mr.RoleId)) || !m.MenuRoles.Any()))
            .OrderBy(m => m.DisplayOrder)
            .ToListAsync();

        return menus.Where(m => m.ParentId == null).ToList();
    }

    public async Task<List<Menu>> GetAllMenusAsync()
    {
        return await _context.Menus
            .Include(m => m.SubMenus)
            .Where(m => m.IsActive && m.ParentId == null)
            .OrderBy(m => m.DisplayOrder)
            .ToListAsync();
    }
}