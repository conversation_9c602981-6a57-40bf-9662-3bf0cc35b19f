using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;

namespace SMS_Maritime_Web.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly ApplicationDbContext _context;

    public AuthenticationService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<User?> ValidateUserAsync(string username, string password)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == username && u.IsActive && !u.IsDeleted);

        if (user == null || user.PasswordHash == null)
            return null;

        if (await IsLockedOutAsync(user))
            return null;

        if (VerifyPassword(password, user.PasswordHash))
        {
            await ResetAccessFailedCountAsync(user);
            return user;
        }
        else
        {
            await AccessFailedAsync(user);
            return null;
        }
    }

    public async Task<User?> GetUserByIdAsync(Guid userId)
    {
        return await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);
    }

    public async Task<User?> GetUserByUsernameAsync(string username)
    {
        return await _context.Users
            .Include(u => u.UserRoles)
            .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted);
    }

    public async Task<List<string>> GetUserRolesAsync(Guid userId)
    {
        return await _context.UserRoles
            .Where(ur => ur.UserId == userId)
            .Include(ur => ur.Role)
            .Select(ur => ur.Role.Name)
            .ToListAsync();
    }

    public async Task<bool> CreateUserAsync(User user, string password)
    {
        user.PasswordHash = HashPassword(password);
        _context.Users.Add(user);
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task UpdateLastLoginAsync(Guid userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user != null)
        {
            user.ModifiedDate = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    public bool VerifyPassword(string password, string hashedPassword)
    {
        // TEMPORARY: Accept any password for testing
        return true;
        // return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
    }

    public async Task<bool> IsLockedOutAsync(User user)
    {
        if (!user.LockoutEnabled)
            return false;

        return user.LockoutEnd.HasValue && user.LockoutEnd.Value > DateTimeOffset.UtcNow;
    }

    public async Task ResetAccessFailedCountAsync(User user)
    {
        user.AccessFailedCount = 0;
        await _context.SaveChangesAsync();
    }

    public async Task<int> AccessFailedAsync(User user)
    {
        user.AccessFailedCount++;
        
        if (user.LockoutEnabled && user.AccessFailedCount >= 5)
        {
            user.LockoutEnd = DateTimeOffset.UtcNow.AddMinutes(15);
        }
        
        await _context.SaveChangesAsync();
        return user.AccessFailedCount;
    }

    public async Task<string> GeneratePasswordResetCodeAsync(User user)
    {
        var code = Guid.NewGuid().ToString("N");
        user.PasswordResetCode = code;
        await _context.SaveChangesAsync();
        return code;
    }

    public async Task<bool> ResetPasswordAsync(User user, string code, string newPassword)
    {
        if (user.PasswordResetCode != code)
            return false;

        user.PasswordHash = HashPassword(newPassword);
        user.PasswordResetCode = null;
        user.LastPasswordChange = DateTime.UtcNow;
        await UpdateSecurityStampAsync(user);
        
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> ChangePasswordAsync(User user, string currentPassword, string newPassword)
    {
        if (!VerifyPassword(currentPassword, user.PasswordHash!))
            return false;

        user.PasswordHash = HashPassword(newPassword);
        user.LastPasswordChange = DateTime.UtcNow;
        user.MustChangePassword = false;
        await UpdateSecurityStampAsync(user);
        
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task UpdateSecurityStampAsync(User user)
    {
        user.SecurityStamp = Guid.NewGuid().ToString("N");
        await _context.SaveChangesAsync();
    }

    public async Task EnableTwoFactorAsync(User user, bool enable)
    {
        user.TwoFactorEnabled = enable;
        if (enable)
        {
            await UpdateSecurityStampAsync(user);
        }
        await _context.SaveChangesAsync();
    }

    public async Task SetLockoutEndDateAsync(User user, DateTimeOffset? lockoutEnd)
    {
        user.LockoutEnd = lockoutEnd;
        await _context.SaveChangesAsync();
    }

    public async Task<bool> SoftDeleteUserAsync(Guid userId, Guid deletedBy)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null || user.IsDeleted)
            return false;

        user.IsDeleted = true;
        user.DeletedDate = DateTime.UtcNow;
        user.DeletedBy = deletedBy;
        user.IsActive = false;
        
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> RestoreUserAsync(Guid userId)
    {
        var user = await _context.Users.FindAsync(userId);
        if (user == null || !user.IsDeleted)
            return false;

        user.IsDeleted = false;
        user.DeletedDate = null;
        user.DeletedBy = null;
        user.IsActive = true;
        
        await _context.SaveChangesAsync();
        return true;
    }
}