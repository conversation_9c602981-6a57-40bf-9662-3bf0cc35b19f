using SMS_Maritime_Web.Models;

namespace SMS_Maritime_Web.Services;

public interface IAuthenticationService
{
    Task<User?> ValidateUserAsync(string username, string password);
    Task<User?> GetUserByIdAsync(Guid userId);
    Task<User?> GetUserByUsernameAsync(string username);
    Task<List<string>> GetUserRolesAsync(Guid userId);
    Task<bool> CreateUserAsync(User user, string password);
    Task UpdateLastLoginAsync(Guid userId);
    string HashPassword(string password);
    bool VerifyPassword(string password, string hashedPassword);
    
    // Security methods
    Task<bool> IsLockedOutAsync(User user);
    Task ResetAccessFailedCountAsync(User user);
    Task<int> AccessFailedAsync(User user);
    Task<string> GeneratePasswordResetCodeAsync(User user);
    Task<bool> ResetPasswordAsync(User user, string code, string newPassword);
    Task<bool> ChangePasswordAsync(User user, string currentPassword, string newPassword);
    Task UpdateSecurityStampAsync(User user);
    Task EnableTwoFactorAsync(User user, bool enable);
    Task SetLockoutEndDateAsync(User user, DateTimeOffset? lockoutEnd);
    
    // Soft delete methods
    Task<bool> SoftDeleteUserAsync(Guid userId, Guid deletedBy);
    Task<bool> RestoreUserAsync(Guid userId);
}