using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using System.Globalization;

namespace SMS_Maritime_Web.Services;

public class LanguageService : ILanguageService
{
    private readonly IServiceProvider _serviceProvider;
    private Dictionary<string, Dictionary<string, string>> _languageTexts = new();

    public LanguageService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _ = LoadLanguageTextsAsync();
    }

    public async Task LoadLanguageTextsAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        var texts = await dbContext.LanguageTexts
            .Include(lt => lt.Language)
            .Where(lt => lt.Language.IsActive)
            .ToListAsync();

        _languageTexts.Clear();

        foreach (var text in texts)
        {
            var langCode = text.Language?.Code ?? string.Empty;
            if (!string.IsNullOrEmpty(langCode))
            {
                if (!_languageTexts.ContainsKey(langCode))
                {
                    _languageTexts[langCode] = new Dictionary<string, string>();
                }

                _languageTexts[langCode][text.TextKey] = text.TextValue;
            }
        }
    }

    public string GetText(string key, string? culture = null)
    {
        culture ??= CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;

        if (_languageTexts.TryGetValue(culture, out var texts) && texts.TryGetValue(key, out var text))
        {
            return text;
        }

        return key;
    }

    public Dictionary<string, string> GetAllTexts(string? culture = null)
    {
        culture ??= CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;

        if (_languageTexts.TryGetValue(culture, out var texts))
        {
            return new Dictionary<string, string>(texts);
        }

        return new Dictionary<string, string>();
    }

    public async Task<List<Language>> GetActiveLanguagesAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        return await dbContext.Languages
            .Where(l => l.IsActive)
            .OrderBy(l => l.SortOrder)
            .ThenBy(l => l.Name)
            .ToListAsync();
    }
}