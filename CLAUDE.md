# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SMS Maritime Web is a comprehensive Ship Management System built with ASP.NET Core 8 and PostgreSQL. It manages maritime operations including vessel management, crew assignments, document compliance, and voyage operations with support for 10 languages.

## Development Commands

### Setup and Build
```bash
# Restore dependencies
dotnet restore

# Apply database migrations (creates/updates database)
dotnet ef database update

# Run the application (development)
dotnet run

# Run with specific profile
dotnet run --launch-profile "Development"
```

### Database Operations
```bash
# Create new migration
dotnet ef migrations add <MigrationName>

# Update database to latest migration
dotnet ef database update

# Revert to specific migration
dotnet ef database update <MigrationName>

# Generate SQL script from migrations
dotnet ef migrations script
```

### Testing
```bash
# The project uses Microsoft Playwright for end-to-end testing
# Test scenarios are documented in MCP_PLAYWRIGHT_TEST_SCENARIO.md
```

## Architecture Overview

### Technology Stack
- **Backend**: ASP.NET Core 8, Entity Framework Core 8.0.11
- **Database**: PostgreSQL with Npgsql provider
- **Frontend**: Bootstrap 5, jQuery, DataTables (no React/Vue/Angular)
- **Authentication**: Cookie-based with custom service
- **Security**: BCrypt.Net for password hashing
- **Testing**: Microsoft Playwright for browser automation

### Core Architectural Patterns

#### MVC with Service Layer
- **Controllers**: Handle HTTP requests only, delegate to services
- **Services**: Business logic layer injected via DI
- **ViewModels**: View-specific models (never expose entities directly)
- **Models**: EF Core entities mapping to PostgreSQL tables

#### Database Design
- **10 modular schemas**: System, Vessel, Crew, Certification, Safety, Procurement, Accounting, Chartering, Maintenance, Communication
- **UUID primary keys**: Using PostgreSQL `gen_random_uuid()`
- **Audit trails**: Created/modified by/date tracking on all entities
- **Soft deletes**: Logical deletion with `IsDeleted` flags
- **UTC timestamps**: All datetime values stored as UTC

#### Security Architecture
- Custom authentication service (not ASP.NET Identity)
- Role-based authorization with dynamic menu system
- BCrypt password hashing with complexity requirements
- Account lockout protection
- CSRF protection enabled

#### Multi-Language Support
- Database-driven translations via `language_texts` table
- 10 supported cultures: tr, en, es, de, fr, ru, zh, ar, pt, nl
- Culture providers: query string, cookie, accept-language header
- Access via `ILanguageService.GetText(key)`

### Key Development Rules

#### Coding Standards (from Instructions.md)
```csharp
// Entity models use PostgreSQL naming
[Table("vessels")]
public class Vessel
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
}

// ViewModels for data transfer
var vessels = await _context.Vessels
    .Select(v => new VesselViewModel
    {
        Id = v.Id,
        Name = v.VesselName
    })
    .ToListAsync();
```

#### Database Management
- All schema changes via EF Core migrations only
- Check `shipmanagement_schema.sql` before adding columns
- DateTime values must be stored as UTC
- Use `AsNoTracking()` for read-only queries

#### Technology Constraints
- No new frontend frameworks (stick to Bootstrap 5 + jQuery)
- No Repository pattern (use DbContext directly)
- No over-engineering (SOLID principles without complexity)
- Team approval required for new packages

## Critical Files and Structure

### Configuration
- `appsettings.json` - Database connection and app settings
- `Program.cs` - Application startup, DI container, localization setup
- `Instructions.md` - Detailed development guidelines (in Turkish)

### Database
- `/Database/` - 10 modular SQL schema files
- `/Data/ApplicationDbContext.cs` - EF Core context
- `/Models/` - 20+ entity models with PostgreSQL mappings
- `/Migrations/` - EF Core database migrations

### Application Logic
- `/Controllers/` - 7 main controllers (Ships, Crew, Documents, Users, Voyages, Account, Home)
- `/Services/` - Business logic (Authentication, Language, Menu services)
- `/ViewModels/` - 13 view-specific models for data transfer

### Views and Assets
- `/Views/` - Razor views organized by controller
- `/wwwroot/` - Static files, Bootstrap 5, jQuery, custom CSS/JS
- Multi-view vessel creation: Create.cshtml (tabs), CreateWizard.cshtml, CreateAccordion.cshtml, CreateSimple.cshtml

## Important Development Notes

### DateTime Handling
```csharp
// Always use UTC for database storage
CreatedDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc)

// PostgreSQL uses timestamp with time zone
[Column("created_date")]
public DateTime CreatedDate { get; set; }
```

### Error Handling Pattern
- Try-catch blocks in service layer only
- Global exception handler configured
- Meaningful error messages to users via TempData

### Performance Considerations
- Use `AsNoTracking()` for read operations
- Implement eager loading to avoid N+1 queries
- Pagination for large datasets
- Memory caching for language texts and lookup data

### Common Patterns

#### Service Registration (Program.cs)
```csharp
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddSingleton<ILanguageService, LanguageService>();
```

#### Controller Pattern
```csharp
[Authorize]
public class ShipsController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;
    
    // Always delegate business logic to services
    // Return ViewModels, never entities
}
```

#### View Pattern
```html
<!-- Use language service for all text -->
@LanguageService.GetText("VesselName")

<!-- Use ViewModels for model binding -->
@model VesselViewModel
```

## Multi-Tenancy Preparation
The database schema includes `tenantid` fields, indicating future multi-tenancy support, but this is not currently implemented in the application logic.

## File Upload Handling
Document uploads are stored in `/wwwroot/uploads/` with organized subdirectories by entity type and date. File security and validation should be considered for production deployments.