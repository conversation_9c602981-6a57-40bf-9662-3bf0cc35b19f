# Database Schema Comparison Report

This document compares the existing database tables (from shipmanagement_schema.sql) with the models currently implemented in the SMS Maritime Web application.

## Table Mapping Summary

| Current Model | Schema Table | Schema Location | Status |
|--------------|--------------|-----------------|---------|
| users | abpusers | ids.abpusers | ⚠️ Different structure |
| vessels | vessels | vsl.vessels | ⚠️ Different structure |
| vessel_crew_assignments | crewonvessels | crw.crewonvessels | ⚠️ Different structure |
| user_compliance_documents | crewcertificates | crw.crewcertificates | ⚠️ Different structure |
| vessel_certificates | vesselcertificates | vsl.vesselcertificates | ⚠️ Different structure |
| certificate_types | certificatetypes | crt.certificatetypes | ⚠️ Different structure |
| crew_ranks | crewvesselranks | crw.crewvesselranks | ⚠️ Different structure |
| crew_departments | departments | public.departments | ⚠️ Different structure |
| vessel_voyages | operationvoyages | ops.operationvoyages | ⚠️ Different structure |
| voyage_port_calls | portcalls | loc.portcalls | ⚠️ Different structure |
| voyage_statuses | - | - | ❌ Not found (status as column) |
| roles | abproles | ids.abproles | ⚠️ Different structure |
| user_roles | abpuserroles | ids.abpuserroles | ⚠️ Different structure |
| languages | languages | sys.languages | ✅ Similar structure |
| language_texts | language_texts | sys.language_texts | ✅ Similar structure |
| menus | - | - | ❌ Not found |
| menu_roles | - | - | ❌ Not found |
| vessel_types | vesseltypes | vsl.vesseltypes | ⚠️ Different structure |
| vessel_statuses | vesselstatuses | vsl.vesselstatuses | ⚠️ Different structure |
| vessel_classes | vesselclasses | vsl.vesselclasses | ⚠️ Different structure |
| flag_states | vesselflags | vsl.vesselflags | ⚠️ Different structure |

## Detailed Field Comparison

### 1. Users Table

**Current Model (users)**
```
- id (uuid)
- username (string)
- password_hash (string) 
- email (string)
- first_name (string)
- last_name (string)
- display_name (string)
- is_active (boolean)
- created_date (datetime)
- modified_date (datetime)
- employee_code (string)
- department_id (uuid)
- date_of_birth (date)
- nationality (string)
- passport_number (string)
- passport_expiry_date (date)
- seaman_book_number (string)
- seaman_book_expiry_date (date)
- phone_number (string)
- preferred_language (string)
- profile_picture (string)
- hire_date (date)
- place_of_birth (string)
- email_confirmed (boolean)
- department (string)
```

**Schema Table (ids.abpusers)**
```
- id (uuid)
- tenantid (uuid)
- username (string)
- normalizedusername (string)
- email (string)
- normalizedemail (string)
- emailconfirmed (boolean)
- passwordhash (string)
- securitystamp (string)
- concurrencystamp (string)
- phonenumber (string)
- phonenumberconfirmed (boolean)
- twofactorenabled (boolean)
- lockoutend (timestamp)
- lockoutenabled (boolean)
- accessfailedcount (integer)
- name (string)
- surname (string)
- isactive (boolean)
- creationtime (timestamp)
- creatoruserid (uuid)
- lastmodificationtime (timestamp)
- lastmodifieruserid (uuid)
- isdeleted (boolean)
- deleteruserid (uuid)
- deletiontime (timestamp)
```

**Missing Fields in Current Model:**
- tenantid
- normalizedusername
- normalizedemail
- securitystamp
- concurrencystamp
- phonenumberconfirmed
- twofactorenabled
- lockoutend
- lockoutenabled
- accessfailedcount
- name/surname (we use first_name/last_name)
- creatoruserid
- lastmodifieruserid
- isdeleted
- deleteruserid
- deletiontime

### 2. Vessels Table

**Current Model (vessels)**
```
- id (uuid)
- vessel_name (string)
- vessel_code (string)
- imo_number (string)
- mmsi_number (string)
- call_sign (string)
- vessel_type_id (uuid)
- vessel_status_id (uuid)
- vessel_class_id (uuid)
- flag_state_id (uuid)
- gross_tonnage (decimal)
- net_tonnage (decimal)
- dead_weight (decimal)
- length_overall (decimal)
- beam (decimal)
- draft (decimal)
- built_year (integer)
- builder_name (string)
- builder_country (string)
- owner_id (uuid)
- manager_id (uuid)
- operator_id (uuid)
- engine_make (string)
- engine_model (string)
- engine_power_kw (decimal)
- is_active (boolean)
- created_date (datetime)
- created_by (uuid)
- modified_date (datetime)
- modified_by (uuid)
- is_deleted (boolean)
```

**Schema Table (vsl.vessels)**
```
- id (uuid)
- tenantid (uuid)
- name (string)
- imonumber (string)
- mmsinumber (string)
- callsign (string)
- vesselclassid (uuid)
- vesseltypeid (uuid)
- vesselstatusid (uuid)
- flagstateid (uuid)
- builtyear (integer)
- deadweight (decimal)
- grosstonage (decimal)
- nettonage (decimal)
- length (decimal)
- beam (decimal)
- draft (decimal)
- mainenginepower (decimal)
- classification (string)
- isactive (boolean)
- creationtime (timestamp)
- creatoruserid (uuid)
- lastmodificationtime (timestamp)
- lastmodifieruserid (uuid)
- isdeleted (boolean)
- deleteruserid (uuid)
- deletiontime (timestamp)
```

**Missing Fields in Current Model:**
- tenantid
- classification
- deleteruserid
- deletiontime

**Extra Fields in Current Model:**
- vessel_code
- builder_name
- builder_country
- owner_id
- manager_id
- operator_id
- engine_make
- engine_model

### 3. Vessel Crew Assignments Table

**Current Model (vessel_crew_assignments)**
```
- id (uuid)
- user_id (uuid)
- vessel_id (uuid)
- rank_id (uuid)
- department_id (uuid)
- sign_on_date (date)
- expected_sign_off_date (date)
- actual_sign_off_date (date)
- sign_on_port (string)
- sign_off_port (string)
- contract_duration_months (integer)
- daily_wage (decimal)
- currency (string)
- assignment_reason (string)
- status (string)
- notes (text)
- is_active (boolean)
- created_date (datetime)
- created_by (uuid)
- modified_date (datetime)
- modified_by (uuid)
- is_deleted (boolean)
- relief_user_id (uuid)
```

**Schema Table (crw.crewonvessels)**
```
- crewonvesselid (uuid)
- crewworkpositionid (uuid)
- crewidentityid (uuid)
- crewvesselrankid (uuid)
- vesselid (uuid)
- comment (string)
- isdeleted (boolean)
- creationtime (timestamp)
- creatoruserid (uuid)
- lastmodifieruserid (uuid)
- lastmodificationtime (timestamp)
- deleteruserid (uuid)
- deletiontime (timestamp)
```

**Missing Fields in Current Model:**
- crewworkpositionid
- deleteruserid
- deletiontime

**Extra Fields in Current Model:**
- department_id
- sign_on_date
- expected_sign_off_date
- actual_sign_off_date
- sign_on_port
- sign_off_port
- contract_duration_months
- daily_wage
- currency
- assignment_reason
- status
- is_active
- relief_user_id

### 4. User Compliance Documents Table

**Current Model (user_compliance_documents)**
```
- id (uuid)
- user_id (uuid)
- document_type (string)
- document_category (string)
- document_number (string)
- issuing_authority (string)
- issuing_country (string)
- issuing_place (string)
- issue_date (date)
- expiry_date (date)
- is_mandatory (boolean)
- flag_states (array)
- vessel_types (array)
- is_original_seen (boolean)
- original_verified_by (uuid)
- original_verified_date (datetime)
- requires_flag_endorsement (boolean)
- flag_endorsement_number (string)
- flag_endorsement_date (date)
- flag_endorsement_expiry (date)
- document_path (string)
- document_size_bytes (long)
- document_hash (string)
- uploaded_date (datetime)
- uploaded_by (uuid)
- verification_status (string)
- verification_notes (string)
- verified_by (uuid)
- verified_date (datetime)
- rejection_reason (string)
- alert_days_before_expiry (integer)
- first_alert_sent (datetime)
- last_alert_sent (datetime)
- alert_count (integer)
- created_date (datetime)
- created_by (uuid)
- modified_date (datetime)
- modified_by (uuid)
- is_deleted (boolean)
- deleted_date (datetime)
- deleted_by (uuid)
```

**Schema Table (crw.crewcertificates)**
```
- id (uuid)
- tenantid (uuid)
- crewidentityid (uuid)
- certificatetype (string)
- certificatename (string)
- certificatenumber (string)
- issuingauthority (string)
- issuedate (date)
- expirydate (date)
- renewaldate (date)
- documentpath (string)
- grade (string)
- limitations (text)
- isactive (boolean)
- creationtime (timestamp)
- creatoruserid (uuid)
- lastmodificationtime (timestamp)
- lastmodifieruserid (uuid)
- isdeleted (boolean)
- deleteruserid (uuid)
- deletiontime (timestamp)
```

**Missing Fields in Current Model:**
- tenantid
- certificatename
- renewaldate
- grade
- limitations

**Extra Fields in Current Model:**
- document_category
- issuing_country
- issuing_place
- is_mandatory
- flag_states
- vessel_types
- is_original_seen
- original_verified_by
- original_verified_date
- requires_flag_endorsement
- flag_endorsement_number
- flag_endorsement_date
- flag_endorsement_expiry
- document_size_bytes
- document_hash
- uploaded_date
- uploaded_by
- verification_status
- verification_notes
- verified_by
- verified_date
- rejection_reason
- alert_days_before_expiry
- first_alert_sent
- last_alert_sent
- alert_count

### 5. Vessel Certificates Table

**Current Model (vessel_certificates)**
```
- id (uuid)
- vessel_id (uuid)
- certificate_type_id (uuid)
- certificate_number (string)
- issue_date (date)
- expiry_date (date)
- last_endorsement_date (date)
- next_endorsement_date (date)
- last_intermediate_date (date)
- next_intermediate_date (date)
- issued_by (string)
- issued_at (string)
- issuing_authority (string)
- survey_type (string)
- surveyor_name (string)
- survey_company (string)
- status (string)
- is_original (boolean)
- remarks (text)
- document_path (string)
- document_size (long)
- document_hash (string)
- alert_30_days (boolean)
- alert_60_days (boolean)
- alert_90_days (boolean)
- alert_acknowledged (boolean)
- alert_acknowledged_by (uuid)
- alert_acknowledged_date (datetime)
- created_date (datetime)
- created_by (uuid)
- modified_date (datetime)
- modified_by (uuid)
- is_deleted (boolean)
```

**Schema Table (vsl.vesselcertificates)**
```
- id (uuid)
- tenantid (uuid)
- vesselid (uuid)
- certificatename (string)
- certificatenumber (string)
- issuingauthority (string)
- issuedate (timestamp)
- expirydate (timestamp)
- renewaldate (timestamp)
- documentpath (string)
- isactive (boolean)
- creationtime (timestamp)
- creatoruserid (uuid)
- lastmodificationtime (timestamp)
- lastmodifieruserid (uuid)
- isdeleted (boolean)
- deleteruserid (uuid)
- deletiontime (timestamp)
```

**Missing Fields in Current Model:**
- tenantid
- certificatename
- renewaldate
- deleteruserid
- deletiontime

**Extra Fields in Current Model:**
- certificate_type_id
- last_endorsement_date
- next_endorsement_date
- last_intermediate_date
- next_intermediate_date
- issued_by
- issued_at
- survey_type
- surveyor_name
- survey_company
- status
- is_original
- remarks
- document_size
- document_hash
- alert_30_days
- alert_60_days
- alert_90_days
- alert_acknowledged
- alert_acknowledged_by
- alert_acknowledged_date

## Summary of Key Differences

1. **Multi-Tenancy**: Schema tables include `tenantid` field which is not in current models
2. **Soft Delete Pattern**: Schema uses additional fields (deleteruserid, deletiontime) 
3. **Audit Fields**: Schema uses different naming (creatoruserid vs created_by)
4. **Normalization**: Schema includes normalized fields for search optimization
5. **Security**: Schema includes security-related fields (securitystamp, lockout fields)
6. **Missing Tables**: menus and menu_roles tables don't exist in the schema
7. **Different Schemas**: Tables are distributed across multiple schemas (ids, vsl, crw, ops, loc, crt, sys)

## Recommendations

1. **Add Multi-Tenancy Support**: Include tenantid field in all tables
2. **Implement Soft Delete Pattern**: Add deleteruserid and deletiontime fields
3. **Standardize Audit Fields**: Align with schema naming conventions
4. **Create Missing Tables**: Implement menus and menu_roles in the database
5. **Review Security Fields**: Consider adding security-related fields to users table
6. **Schema Alignment**: Consider using schema prefixes for better organization