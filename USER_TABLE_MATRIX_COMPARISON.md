# Users Table Matrix Comparison

## Comparison Matrix: PostgreSQL vs Model Entity vs users.txt

| Field Name | PostgreSQL Database | Model Entity (User.cs) | users.txt (SQL Server) | Match Status |
|------------|-------------------|---------------------|---------------------|--------------|
| **🔑 Primary Key** |
| id/UserID | `id (uuid)` | `Guid Id` | `UserID (uniqueidentifier)` | ✅ All Match |
| **👤 Basic User Information** |
| username/UserName | `username (varchar)` | `string Username` | `UserName (nvarchar(256))` | ✅ All Match |
| first_name/Name | `first_name (varchar)` | `string FirstName` | `Name (nvarchar(150))` | ✅ All Match |
| last_name/Surname | `last_name (varchar)` | `string LastName` | `Surname (nvarchar(150))` | ✅ All Match |
| email/Email | `email (varchar)` | `string Email` | `Email (nvarchar(150))` | ✅ All Match |
| display_name | `display_name (varchar)` | `string? DisplayName` | ❌ Missing | ⚠️ DB + Model Only |
| normalized_username | ❌ Missing | ❌ Missing | `NormalizedUserName (nvarchar(256))` | 🔴 SQL Only |
| normalized_email | ❌ Missing | ❌ Missing | `NormalizedEmail (nvarchar(256))` | 🔴 SQL Only |
| **🔐 Authentication & Security** |
| password | ❌ Not Used | ❌ Not Used | `Password (nvarchar(150))` | 🔴 SQL Only |
| password_hash | `password_hash (varchar)` | `string? PasswordHash` | `PasswordHash (nvarchar(256))` | ✅ All Match |
| security_stamp | ❌ Missing | ❌ Missing | `SecurityStamp (nvarchar(256))` | 🔴 SQL Only |
| password_reset_code | ❌ Missing | ❌ Missing | `PasswordResetCode (nvarchar(328))` | 🔴 SQL Only |
| should_change_password | ❌ Missing | ❌ Missing | `ShouldChangePasswordOnNextLogin (bit)` | 🔴 SQL Only |
| last_password_change | ❌ Missing | ❌ Missing | `LastPasswordChangeTime (datetimeoffset)` | 🔴 SQL Only |
| access_failed_count | ❌ Missing | ❌ Missing | `AccessFailedCount (int)` | 🔴 SQL Only |
| last_login_time | ❌ Missing | ❌ Missing | `LastLoginTime (datetime)` | 🔴 SQL Only |
| two_factor_enabled | ❌ Missing | ❌ Missing | `TwoFactorEnabled (bit)` | 🔴 SQL Only |
| lockout_end | ❌ Missing | ❌ Missing | `LockoutEnd (datetimeoffset)` | 🔴 SQL Only |
| lockout_enabled | ❌ Missing | ❌ Missing | `LockoutEnabled (bit)` | 🔴 SQL Only |
| **📞 Contact Information** |
| phone_number | `phone_number (varchar)` | `string? PhoneNumber` | `PhoneNumber (nvarchar(16))` | ✅ All Match |
| phone_number_confirmed | ❌ Missing | ❌ Missing | `PhoneNumberConfirmed (bit)` | 🔴 SQL Only |
| email_confirmed | `email_confirmed (boolean)` | `bool EmailConfirmed` | `EmailConfirmed (bit)` | ✅ All Match |
| **✅ Status Flags** |
| is_active | `is_active (boolean)` | `bool IsActive` | `IsActive (bit)` | ✅ All Match |
| is_deleted | ❌ Missing | ❌ Missing | `IsDeleted (bit)` | 🔴 SQL Only |
| is_system_user | ❌ Missing | ❌ Missing | `IsSystemUser (bit)` | 🔴 SQL Only |
| is_external | ❌ Missing | ❌ Missing | `IsExternal (bit)` | 🔴 SQL Only |
| **🏢 Organization & Multi-tenancy** |
| tenant_id | ❌ Missing | ❌ Missing | `TenantId (uniqueidentifier)` | 🔴 SQL Only |
| user_group_id | ❌ Missing | ❌ Missing | `UserGroupID (uniqueidentifier)` | 🔴 SQL Only |
| language_id/preferred_language | `preferred_language (varchar)` | `string PreferredLanguage` | `LanguageID (uniqueidentifier)` | ⚠️ Different Types |
| **📅 Audit Fields** |
| created_date/CreationTime | `created_date (timestamp)` | `DateTime CreatedDate` | `CreationTime (datetime2)` | ✅ All Match |
| created_by/CreatorId | ❌ Missing | ❌ Missing | `CreatorId (uniqueidentifier)` | 🔴 SQL Only |
| modified_date/LastModificationTime | `modified_date (timestamp)` | `DateTime? ModifiedDate` | `LastModificationTime (datetime2)` | ✅ All Match |
| modified_by/LastModifierId | ❌ Missing | ❌ Missing | `LastModifierId (uniqueidentifier)` | 🔴 SQL Only |
| deleted_date/DeletionTime | ❌ Missing | ❌ Missing | `DeletionTime (datetime2)` | 🔴 SQL Only |
| deleted_by/DeleterId | ❌ Missing | ❌ Missing | `DeleterId (uniqueidentifier)` | 🔴 SQL Only |
| **🚢 Maritime/Crew Specific (SMS Features)** |
| date_of_birth | `date_of_birth (date)` | `DateTime? DateOfBirth` | ❌ Missing | 🟢 PG + Model Only |
| nationality | `nationality (varchar)` | `string? Nationality` | ❌ Missing | 🟢 PG + Model Only |
| place_of_birth | `place_of_birth (varchar)` | `string? PlaceOfBirth` | ❌ Missing | 🟢 PG + Model Only |
| department | `department (varchar)` | `string? Department` | ❌ Missing | 🟢 PG + Model Only |
| department_id | `department_id (uuid)` | `Guid? DepartmentId` | ❌ Missing | 🟢 PG + Model Only |
| employee_code | `employee_code (varchar)` | `string? EmployeeCode` | ❌ Missing | 🟢 PG + Model Only |
| hire_date | `hire_date (date)` | `DateTime? HireDate` | ❌ Missing | 🟢 PG + Model Only |
| passport_number | `passport_number (varchar)` | `string? PassportNumber` | ❌ Missing | 🟢 PG + Model Only |
| passport_expiry_date | `passport_expiry_date (date)` | `DateTime? PassportExpiryDate` | ❌ Missing | 🟢 PG + Model Only |
| seaman_book_number | `seaman_book_number (varchar)` | `string? SeamanBookNumber` | ❌ Missing | 🟢 PG + Model Only |
| seaman_book_expiry_date | `seaman_book_expiry_date (date)` | `DateTime? SeamanBookExpiryDate` | ❌ Missing | 🟢 PG + Model Only |
| profile_picture | `profile_picture (varchar)` | `string? ProfilePicture` | ❌ Missing | 🟢 PG + Model Only |
| **🔧 Technical/System Fields** |
| current_session_id | ❌ Missing | ❌ Missing | `CurrentSessionID (uniqueidentifier)` | 🔴 SQL Only |
| concurrency_stamp | ❌ Missing | ❌ Missing | `ConcurrencyStamp (nvarchar(40))` | 🔴 SQL Only |
| extra_properties | ❌ Missing | ❌ Missing | `ExtraProperties (nvarchar)` | 🔴 SQL Only |
| entity_version | ❌ Missing | ❌ Missing | `EntityVersion (int)` | 🔴 SQL Only |
| user_row_pointer | ❌ Missing | ❌ Missing | `UserRowPointer (uniqueidentifier)` | 🔴 SQL Only |
| old_user_id | ❌ Missing | ❌ Missing | `OldUserID (int)` | 🔴 SQL Only |
| selector_id | ❌ Missing | ❌ Missing | `SelectorID (int)` | 🔴 SQL Only |
| directory_enum | ❌ Missing | ❌ Missing | `DirectoryEnum (int)` | 🔴 SQL Only |

## Summary Statistics

### 📊 Field Coverage Analysis

| Source | Total Fields | Unique Fields | Shared with All | Implementation Rate |
|--------|--------------|---------------|-----------------|-------------------|
| **PostgreSQL** | 22 | 0 | 10 | 45% of users.txt |
| **Model Entity** | 22 | 0 | 10 | 45% of users.txt |
| **users.txt** | 42 | 32 | 10 | 100% (Reference) |

### 🎯 Match Categories

| Category | Count | Fields |
|----------|-------|--------|
| ✅ **All Match** | 10 | id, username, first_name, last_name, email, password_hash, phone_number, email_confirmed, is_active, created_date, modified_date |
| 🟢 **PG + Model Only** | 12 | display_name, date_of_birth, nationality, place_of_birth, department, department_id, employee_code, hire_date, passport_number, passport_expiry_date, seaman_book_number, seaman_book_expiry_date, profile_picture |
| 🔴 **SQL Server Only** | 30 | normalized_username, normalized_email, security_stamp, password_reset_code, should_change_password, last_password_change, access_failed_count, last_login_time, two_factor_enabled, lockout_end, lockout_enabled, phone_number_confirmed, is_deleted, is_system_user, is_external, tenant_id, user_group_id, created_by, modified_by, deleted_date, deleted_by, current_session_id, concurrency_stamp, extra_properties, entity_version, user_row_pointer, old_user_id, selector_id, directory_enum, password |
| ⚠️ **Type Mismatch** | 1 | language_id/preferred_language (GUID vs string) |

## 🔍 Key Insights

### 1. **Security Features Gap** 🔐
PostgreSQL and Model are missing critical security features:
- No password reset mechanism
- No account lockout functionality
- No two-factor authentication support
- No login tracking
- No security stamps for token invalidation

### 2. **Maritime Domain Focus** 🚢
PostgreSQL and Model include 12 maritime-specific fields not in users.txt:
- Personal information (birth details, nationality)
- Employment records
- Maritime documents (passport, seaman book)
- These represent 55% of the implemented fields

### 3. **Audit Trail Incomplete** 📝
Missing comprehensive audit fields:
- No tracking of who created/modified/deleted records
- No soft delete implementation
- No concurrency control

### 4. **Multi-tenancy Not Implemented** 🏢
- No TenantId support
- No user group management
- Single-tenant design

### 5. **Performance Optimizations Missing** ⚡
- No normalized fields for search optimization
- No caching-friendly fields

## 🚀 Recommendations Priority Matrix

| Priority | Implementation | Fields to Add | Business Impact |
|----------|---------------|---------------|-----------------|
| 🔴 **Critical** | Security | `security_stamp`, `lockout_*`, `two_factor_enabled`, `access_failed_count` | User account security |
| 🟠 **High** | Soft Delete | `is_deleted`, `deleted_date`, `deleted_by` | Data integrity & compliance |
| 🟡 **Medium** | Audit Trail | `created_by`, `modified_by` | Traceability |
| 🟢 **Low** | Performance | `normalized_username`, `normalized_email` | Search optimization |
| 🔵 **Future** | Multi-tenancy | `tenant_id`, `user_group_id` | Scalability |

## 📋 Implementation Checklist

- [ ] Add security fields to PostgreSQL schema
- [ ] Update User.cs model with security properties
- [ ] Implement soft delete pattern
- [ ] Add audit trail fields
- [ ] Create migration scripts
- [ ] Update repository methods for soft delete
- [ ] Implement password reset functionality
- [ ] Add account lockout logic
- [ ] Consider multi-tenancy for future