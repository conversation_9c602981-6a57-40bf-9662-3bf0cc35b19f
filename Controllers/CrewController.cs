using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class CrewController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;

    public CrewController(ApplicationDbContext context, ILanguageService languageService)
    {
        _context = context;
        _languageService = languageService;
    }

    // GET: Crew
    public async Task<IActionResult> Index()
    {
        var crew = await _context.Users
            .Include(u => u.VesselCrewAssignments)
                .ThenInclude(vca => vca.Vessel)
            .Include(u => u.VesselCrewAssignments)
                .ThenInclude(vca => vca.Rank)
            .Where(u => u.SeamanBookNumber != null || u.VesselCrewAssignments.Any())
            .Select(u => new CrewViewModel
            {
                Id = u.Id,
                EmployeeCode = u.EmployeeCode ?? "",
                FirstName = u.FirstName,
                LastName = u.LastName,
                DisplayName = u.DisplayName,
                Email = u.Email,
                PhoneNumber = u.PhoneNumber,
                DateOfBirth = u.DateOfBirth,
                Nationality = u.Nationality,
                SeamanBookNumber = u.SeamanBookNumber,
                SeamanBookExpiryDate = u.SeamanBookExpiryDate,
                PassportNumber = u.PassportNumber,
                PassportExpiryDate = u.PassportExpiryDate,
                IsActive = u.IsActive,
                CurrentVesselName = u.VesselCrewAssignments
                    .Where(vca => vca.Status == "Onboard" && !vca.IsDeleted)
                    .Select(vca => vca.Vessel!.VesselName)
                    .FirstOrDefault(),
                CurrentRankName = u.VesselCrewAssignments
                    .Where(vca => vca.Status == "Onboard" && !vca.IsDeleted)
                    .Select(vca => vca.Rank!.Name)
                    .FirstOrDefault(),
                CurrentSignOnDate = u.VesselCrewAssignments
                    .Where(vca => vca.Status == "Onboard" && !vca.IsDeleted)
                    .Select(vca => vca.SignOnDate)
                    .FirstOrDefault(),
                IsOnBoard = u.VesselCrewAssignments.Any(vca => vca.Status == "Onboard" && !vca.IsDeleted)
            })
            .OrderBy(c => c.LastName)
            .ThenBy(c => c.FirstName)
            .ToListAsync();

        return View(crew);
    }

    // GET: Crew/Details/5
    public async Task<IActionResult> Details(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var crew = await _context.Users
            .Include(u => u.DepartmentEntity)
            .Include(u => u.VesselCrewAssignments)
                .ThenInclude(vca => vca.Vessel)
            .Include(u => u.VesselCrewAssignments)
                .ThenInclude(vca => vca.Rank)
            .FirstOrDefaultAsync(u => u.Id == id);

        if (crew == null)
        {
            return NotFound();
        }

        var viewModel = new CrewViewModel
        {
            Id = crew.Id,
            EmployeeCode = crew.EmployeeCode ?? "",
            FirstName = crew.FirstName,
            LastName = crew.LastName,
            DisplayName = crew.DisplayName,
            Email = crew.Email,
            PhoneNumber = crew.PhoneNumber,
            DateOfBirth = crew.DateOfBirth,
            Nationality = crew.Nationality,
            PlaceOfBirth = crew.PlaceOfBirth,
            DepartmentId = crew.DepartmentId,
            DepartmentName = crew.DepartmentEntity?.Name,
            HireDate = crew.HireDate,
            PassportNumber = crew.PassportNumber,
            PassportExpiryDate = crew.PassportExpiryDate,
            SeamanBookNumber = crew.SeamanBookNumber,
            SeamanBookExpiryDate = crew.SeamanBookExpiryDate,
            IsActive = crew.IsActive
        };

        ViewBag.Assignments = crew.VesselCrewAssignments.OrderByDescending(a => a.SignOnDate).ToList();

        return View(viewModel);
    }

    // GET: Crew/Create
    public async Task<IActionResult> Create()
    {
        await LoadViewData();
        return View(new CrewViewModel { IsActive = true });
    }

    // POST: Crew/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CrewViewModel model)
    {
        if (ModelState.IsValid)
        {
            var user = new User
            {
                Id = Guid.NewGuid(),
                Username = model.EmployeeCode.ToLower(), // Use employee code as username
                Email = model.Email,
                FirstName = model.FirstName,
                LastName = model.LastName,
                DisplayName = model.DisplayName ?? $"{model.FirstName} {model.LastName}",
                EmployeeCode = model.EmployeeCode,
                PhoneNumber = model.PhoneNumber,
                DateOfBirth = model.DateOfBirth?.ToUniversalTime(),
                Nationality = model.Nationality,
                PlaceOfBirth = model.PlaceOfBirth,
                DepartmentId = model.DepartmentId,
                HireDate = model.HireDate?.ToUniversalTime(),
                PassportNumber = model.PassportNumber,
                PassportExpiryDate = model.PassportExpiryDate?.ToUniversalTime(),
                SeamanBookNumber = model.SeamanBookNumber,
                SeamanBookExpiryDate = model.SeamanBookExpiryDate?.ToUniversalTime(),
                IsActive = model.IsActive,
                CreatedDate = DateTime.UtcNow
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("CrewCreatedSuccessfully");
            return RedirectToAction(nameof(Index));
        }
        
        await LoadViewData();
        return View(model);
    }

    // GET: Crew/Edit/5
    public async Task<IActionResult> Edit(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var user = await _context.Users.FindAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        var model = new CrewViewModel
        {
            Id = user.Id,
            EmployeeCode = user.EmployeeCode ?? "",
            FirstName = user.FirstName,
            LastName = user.LastName,
            DisplayName = user.DisplayName,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            DateOfBirth = user.DateOfBirth,
            Nationality = user.Nationality,
            PlaceOfBirth = user.PlaceOfBirth,
            DepartmentId = user.DepartmentId,
            HireDate = user.HireDate,
            PassportNumber = user.PassportNumber,
            PassportExpiryDate = user.PassportExpiryDate,
            SeamanBookNumber = user.SeamanBookNumber,
            SeamanBookExpiryDate = user.SeamanBookExpiryDate,
            IsActive = user.IsActive
        };

        await LoadViewData();
        return View(model);
    }

    // POST: Crew/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(Guid id, CrewViewModel model)
    {
        if (id != model.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                user.Email = model.Email;
                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.DisplayName = model.DisplayName ?? $"{model.FirstName} {model.LastName}";
                user.EmployeeCode = model.EmployeeCode;
                user.PhoneNumber = model.PhoneNumber;
                user.DateOfBirth = model.DateOfBirth?.ToUniversalTime();
                user.Nationality = model.Nationality;
                user.PlaceOfBirth = model.PlaceOfBirth;
                user.DepartmentId = model.DepartmentId;
                user.HireDate = model.HireDate?.ToUniversalTime();
                user.PassportNumber = model.PassportNumber;
                user.PassportExpiryDate = model.PassportExpiryDate?.ToUniversalTime();
                user.SeamanBookNumber = model.SeamanBookNumber;
                user.SeamanBookExpiryDate = model.SeamanBookExpiryDate?.ToUniversalTime();
                user.IsActive = model.IsActive;
                user.ModifiedDate = DateTime.UtcNow;

                _context.Update(user);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = _languageService.GetText("CrewUpdatedSuccessfully");
                return RedirectToAction(nameof(Index));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserExists(model.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
        }
        
        await LoadViewData();
        return View(model);
    }

    // GET: Crew/Assignments
    public async Task<IActionResult> Assignments(Guid? vesselId = null)
    {
        var assignmentsQuery = _context.VesselCrewAssignments
            .Include(vca => vca.Vessel)
            .Include(vca => vca.User)
            .Include(vca => vca.Rank)
            .Include(vca => vca.ReliefUser)
            .Where(vca => !vca.IsDeleted);

        if (vesselId.HasValue)
        {
            assignmentsQuery = assignmentsQuery.Where(vca => vca.VesselId == vesselId.Value);
        }

        var assignments = await assignmentsQuery
            .OrderBy(vca => vca.Vessel!.VesselName)
            .ThenByDescending(vca => vca.SignOnDate)
            .ToListAsync();

        ViewBag.Vessels = await _context.Vessels
            .Where(v => v.IsActive && !v.IsDeleted)
            .OrderBy(v => v.VesselName)
            .ToListAsync();

        ViewBag.SelectedVesselId = vesselId;

        return View(assignments);
    }

    // GET: Crew/AssignToVessel
    public async Task<IActionResult> AssignToVessel(Guid? userId = null)
    {
        await LoadAssignmentViewData();
        
        var model = new CrewAssignmentViewModel();
        if (userId.HasValue)
        {
            model.UserId = userId.Value;
        }
        
        return View(model);
    }

    // POST: Crew/AssignToVessel
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AssignToVessel(CrewAssignmentViewModel model)
    {
        if (ModelState.IsValid)
        {
            // Check if crew member is already assigned to another vessel
            var existingAssignment = await _context.VesselCrewAssignments
                .Where(vca => vca.UserId == model.UserId && vca.Status == "Onboard" && !vca.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingAssignment != null)
            {
                ModelState.AddModelError("", _languageService.GetText("CrewAlreadyAssigned"));
                await LoadAssignmentViewData();
                return View(model);
            }

            var assignment = new VesselCrewAssignment
            {
                Id = Guid.NewGuid(),
                VesselId = model.VesselId,
                UserId = model.UserId,
                RankId = model.RankId,
                Department = model.Department,
                SignOnDate = (model.SignOnDate ?? DateTime.Today).ToUniversalTime(),
                SignOnPort = model.SignOnPort,
                Status = "Onboard",
                Remarks = model.Notes,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
            };

            // Calculate expected sign off date
            if (model.ContractDurationMonths.HasValue)
            {
                assignment.ExpectedSignOffDate = assignment.SignOnDate.AddMonths(model.ContractDurationMonths.Value);
                assignment.ReliefDueDate = assignment.ExpectedSignOffDate.Value.AddDays(-14); // 14 days before contract end
            }

            _context.VesselCrewAssignments.Add(assignment);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("CrewAssignedSuccessfully");
            return RedirectToAction(nameof(Assignments));
        }
        
        await LoadAssignmentViewData();
        return View(model);
    }

    // POST: Crew/SignOff/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SignOff(Guid id, DateTime signOffDate, string signOffPort)
    {
        var assignment = await _context.VesselCrewAssignments.FindAsync(id);
        if (assignment == null)
        {
            return NotFound();
        }

        assignment.ActualSignOffDate = signOffDate.ToUniversalTime();
        assignment.SignOffPort = signOffPort;
        assignment.Status = "Completed";
        assignment.ModifiedDate = DateTime.UtcNow;
        assignment.ModifiedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");

        _context.Update(assignment);
        await _context.SaveChangesAsync();
        
        TempData["SuccessMessage"] = _languageService.GetText("CrewSignedOffSuccessfully");
        return RedirectToAction(nameof(Assignments));
    }

    private bool UserExists(Guid id)
    {
        return _context.Users.Any(e => e.Id == id);
    }

    private async Task LoadViewData()
    {
        ViewData["Departments"] = await _context.CrewDepartments
            .Where(d => d.IsActive)
            .OrderBy(d => d.SortOrder)
            .ThenBy(d => d.Name)
            .ToListAsync();

        ViewData["Nationalities"] = new List<SelectListItem>
        {
            new SelectListItem { Value = "TR", Text = "Turkey" },
            new SelectListItem { Value = "PH", Text = "Philippines" },
            new SelectListItem { Value = "IN", Text = "India" },
            new SelectListItem { Value = "CN", Text = "China" },
            new SelectListItem { Value = "ID", Text = "Indonesia" },
            new SelectListItem { Value = "RU", Text = "Russia" },
            new SelectListItem { Value = "UA", Text = "Ukraine" },
            new SelectListItem { Value = "GR", Text = "Greece" },
            new SelectListItem { Value = "GB", Text = "United Kingdom" },
            new SelectListItem { Value = "US", Text = "United States" }
        };
    }

    private async Task LoadAssignmentViewData()
    {
        ViewData["Vessels"] = await _context.Vessels
            .Where(v => v.IsActive && !v.IsDeleted)
            .OrderBy(v => v.VesselName)
            .ToListAsync();

        ViewData["Crew"] = await _context.Users
            .Where(u => u.SeamanBookNumber != null && u.IsActive)
            .OrderBy(u => u.LastName)
            .ThenBy(u => u.FirstName)
            .ToListAsync();

        ViewData["Ranks"] = await _context.CrewRanks
            .Where(r => r.IsActive)
            .OrderBy(r => r.Level)
            .ThenBy(r => r.SortOrder)
            .ToListAsync();
    }
}