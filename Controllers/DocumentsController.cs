using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;
using System.Security.Cryptography;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class DocumentsController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;
    private readonly IWebHostEnvironment _environment;
    private readonly IConfiguration _configuration;

    public DocumentsController(
        ApplicationDbContext context, 
        ILanguageService languageService,
        IWebHostEnvironment environment,
        IConfiguration configuration)
    {
        _context = context;
        _languageService = languageService;
        _environment = environment;
        _configuration = configuration;
    }

    // GET: Documents
    public IActionResult Index()
    {
        return View();
    }

    // GET: Documents/UserDocuments
    public async Task<IActionResult> UserDocuments(DocumentFilterViewModel? filter = null)
    {
        var query = _context.UserComplianceDocuments
            .Include(d => d.User)
            .Include(d => d.Uploader)
            .Include(d => d.Verifier)
            .Where(d => !d.IsDeleted);

        if (filter != null)
        {
            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                query = query.Where(d => 
                    d.DocumentNumber.Contains(filter.SearchTerm) ||
                    d.DocumentType.Contains(filter.SearchTerm) ||
                    d.IssuingAuthority.Contains(filter.SearchTerm));
            }

            if (!string.IsNullOrWhiteSpace(filter.DocumentType))
            {
                query = query.Where(d => d.DocumentType == filter.DocumentType);
            }

            if (!string.IsNullOrWhiteSpace(filter.Status))
            {
                query = query.Where(d => d.VerificationStatus == filter.Status);
            }

            if (filter.ShowExpired.HasValue && filter.ShowExpired.Value)
            {
                query = query.Where(d => d.ExpiryDate.HasValue && d.ExpiryDate.Value.Date < DateTime.UtcNow.Date);
            }

            if (filter.ShowExpiringSoon.HasValue && filter.ShowExpiringSoon.Value)
            {
                var expiryThreshold = DateTime.UtcNow.Date.AddDays(90);
                query = query.Where(d => d.ExpiryDate.HasValue && 
                    d.ExpiryDate.Value.Date >= DateTime.UtcNow.Date && 
                    d.ExpiryDate.Value.Date <= expiryThreshold);
            }

            if (filter.UserId.HasValue)
            {
                query = query.Where(d => d.UserId == filter.UserId.Value);
            }
        }

        var documents = await query
            .OrderBy(d => d.ExpiryDate)
            .Select(d => new UserDocumentViewModel
            {
                Id = d.Id,
                UserId = d.UserId,
                UserName = d.User!.Username,
                UserFullName = $"{d.User.FirstName} {d.User.LastName}",
                DocumentType = d.DocumentType,
                DocumentCategory = d.DocumentCategory,
                DocumentNumber = d.DocumentNumber,
                IssuingAuthority = d.IssuingAuthority,
                IssuingCountry = d.IssuingCountry,
                IssueDate = d.IssueDate,
                ExpiryDate = d.ExpiryDate,
                VerificationStatus = d.VerificationStatus,
                DocumentPath = d.DocumentPath,
                DocumentSizeBytes = d.DocumentSizeBytes,
                UploadedDate = d.UploadedDate,
                UploadedByName = d.Uploader != null ? $"{d.Uploader.FirstName} {d.Uploader.LastName}" : null,
                VerifiedDate = d.VerifiedDate,
                VerifiedByName = d.Verifier != null ? $"{d.Verifier.FirstName} {d.Verifier.LastName}" : null,
                IsExpired = d.ExpiryDate.HasValue && d.ExpiryDate.Value.Date < DateTime.UtcNow.Date,
                IsExpiringSoon = d.ExpiryDate.HasValue && 
                    d.ExpiryDate.Value.Date >= DateTime.UtcNow.Date && 
                    d.ExpiryDate.Value.Date <= DateTime.UtcNow.Date.AddDays(d.AlertDaysBeforeExpiry),
                DaysUntilExpiry = d.ExpiryDate.HasValue ? 
                    (int)(d.ExpiryDate.Value.Date - DateTime.UtcNow.Date).TotalDays : null
            })
            .ToListAsync();

        ViewBag.Filter = filter;
        ViewBag.DocumentTypes = await GetDistinctDocumentTypes();
        ViewBag.Users = await _context.Users
            .Where(u => u.IsActive)
            .OrderBy(u => u.FirstName)
            .ThenBy(u => u.LastName)
            .ToListAsync();

        return View(documents);
    }

    // GET: Documents/VesselDocuments
    public async Task<IActionResult> VesselDocuments(DocumentFilterViewModel? filter = null)
    {
        var query = _context.VesselCertificates
            .Include(c => c.Vessel)
            .Include(c => c.CertificateType)
            .Where(c => !c.IsDeleted);

        if (filter != null)
        {
            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                query = query.Where(c => 
                    c.CertificateNumber!.Contains(filter.SearchTerm) ||
                    c.CertificateType!.Name.Contains(filter.SearchTerm) ||
                    c.IssuingAuthority!.Contains(filter.SearchTerm));
            }

            if (!string.IsNullOrWhiteSpace(filter.Status))
            {
                query = query.Where(c => c.Status == filter.Status);
            }

            if (filter.ShowExpired.HasValue && filter.ShowExpired.Value)
            {
                query = query.Where(c => c.ExpiryDate.Date < DateTime.UtcNow.Date);
            }

            if (filter.ShowExpiringSoon.HasValue && filter.ShowExpiringSoon.Value)
            {
                var expiryThreshold = DateTime.UtcNow.Date.AddDays(90);
                query = query.Where(c => c.ExpiryDate.Date >= DateTime.UtcNow.Date && 
                    c.ExpiryDate.Date <= expiryThreshold);
            }

            if (filter.VesselId.HasValue)
            {
                query = query.Where(c => c.VesselId == filter.VesselId.Value);
            }
        }

        var certificates = await query
            .OrderBy(c => c.ExpiryDate)
            .Select(c => new VesselDocumentViewModel
            {
                Id = c.Id,
                VesselId = c.VesselId,
                VesselName = c.Vessel!.VesselName,
                CertificateTypeId = c.CertificateTypeId,
                CertificateTypeName = c.CertificateType!.Name,
                CertificateCategory = c.CertificateType.Category,
                CertificateNumber = c.CertificateNumber,
                IssueDate = c.IssueDate,
                ExpiryDate = c.ExpiryDate,
                Status = c.Status,
                IssuingAuthority = c.IssuingAuthority,
                SurveyType = c.SurveyType,
                DocumentPath = c.DocumentPath,
                DocumentSize = c.DocumentSize,
                IsExpired = c.ExpiryDate.Date < DateTime.UtcNow.Date,
                IsExpiringSoon = !c.IsExpired && c.ExpiryDate.Date <= DateTime.UtcNow.Date.AddDays(90),
                DaysUntilExpiry = (int)(c.ExpiryDate.Date - DateTime.UtcNow.Date).TotalDays,
                CreatedDate = c.CreatedDate
            })
            .ToListAsync();

        ViewBag.Filter = filter;
        ViewBag.Vessels = await _context.Vessels
            .Where(v => v.IsActive && !v.IsDeleted)
            .OrderBy(v => v.VesselName)
            .ToListAsync();

        return View(certificates);
    }

    // GET: Documents/CreateUserDocument
    public async Task<IActionResult> CreateUserDocument(Guid? userId = null)
    {
        await LoadUserDocumentViewData();
        
        var model = new UserDocumentViewModel();
        if (userId.HasValue)
        {
            model.UserId = userId.Value;
        }

        return View(model);
    }

    // POST: Documents/CreateUserDocument
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateUserDocument(UserDocumentViewModel model)
    {
        if (ModelState.IsValid)
        {
            // Check for duplicate document
            var exists = await _context.UserComplianceDocuments
                .AnyAsync(d => d.UserId == model.UserId && 
                    d.DocumentType == model.DocumentType && 
                    d.DocumentNumber == model.DocumentNumber &&
                    !d.IsDeleted);

            if (exists)
            {
                ModelState.AddModelError("", _languageService.GetText("DocumentAlreadyExists"));
            }
            else
            {
                var document = new UserComplianceDocument
                {
                    Id = Guid.NewGuid(),
                    UserId = model.UserId,
                    DocumentType = model.DocumentType,
                    DocumentCategory = model.DocumentCategory,
                    DocumentNumber = model.DocumentNumber,
                    IssuingAuthority = model.IssuingAuthority,
                    IssuingCountry = model.IssuingCountry,
                    IssuingPlace = model.IssuingPlace,
                    IssueDate = DateTime.SpecifyKind(model.IssueDate, DateTimeKind.Utc),
                    ExpiryDate = model.ExpiryDate.HasValue ? DateTime.SpecifyKind(model.ExpiryDate.Value, DateTimeKind.Utc) : null,
                    IsMandatory = model.IsMandatory,
                    IsOriginalSeen = model.IsOriginalSeen,
                    RequiresFlagEndorsement = model.RequiresFlagEndorsement,
                    FlagEndorsementNumber = model.FlagEndorsementNumber,
                    FlagEndorsementDate = model.FlagEndorsementDate.HasValue ? DateTime.SpecifyKind(model.FlagEndorsementDate.Value, DateTimeKind.Utc) : null,
                    FlagEndorsementExpiry = model.FlagEndorsementExpiry.HasValue ? DateTime.SpecifyKind(model.FlagEndorsementExpiry.Value, DateTimeKind.Utc) : null,
                    VerificationStatus = "Pending",
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
                };

                // Handle file upload
                if (model.DocumentFile != null && model.DocumentFile.Length > 0)
                {
                    var uploadResult = await UploadDocumentFile(model.DocumentFile, "user-documents", document.Id.ToString());
                    if (uploadResult.Success)
                    {
                        document.DocumentPath = uploadResult.FilePath;
                        document.DocumentSizeBytes = uploadResult.FileSize;
                        document.DocumentHash = uploadResult.FileHash;
                        document.UploadedDate = DateTime.UtcNow;
                        document.UploadedBy = document.CreatedBy;
                    }
                    else
                    {
                        ModelState.AddModelError("DocumentFile", uploadResult.ErrorMessage!);
                    }
                }

                if (ModelState.IsValid)
                {
                    _context.UserComplianceDocuments.Add(document);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = _languageService.GetText("DocumentCreatedSuccessfully");
                    return RedirectToAction(nameof(UserDocuments));
                }
            }
        }

        await LoadUserDocumentViewData();
        return View(model);
    }

    // GET: Documents/CreateVesselDocument
    public async Task<IActionResult> CreateVesselDocument(Guid? vesselId = null)
    {
        await LoadVesselDocumentViewData();
        
        var model = new VesselDocumentViewModel();
        if (vesselId.HasValue)
        {
            model.VesselId = vesselId.Value;
        }

        return View(model);
    }

    // POST: Documents/CreateVesselDocument
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateVesselDocument(VesselDocumentViewModel model)
    {
        if (ModelState.IsValid)
        {
            var certificate = new VesselCertificate
            {
                Id = Guid.NewGuid(),
                VesselId = model.VesselId,
                CertificateTypeId = model.CertificateTypeId,
                CertificateNumber = model.CertificateNumber,
                IssueDate = DateTime.SpecifyKind(model.IssueDate, DateTimeKind.Utc),
                ExpiryDate = DateTime.SpecifyKind(model.ExpiryDate, DateTimeKind.Utc),
                LastEndorsementDate = model.LastEndorsementDate.HasValue ? DateTime.SpecifyKind(model.LastEndorsementDate.Value, DateTimeKind.Utc) : null,
                NextEndorsementDate = model.NextEndorsementDate.HasValue ? DateTime.SpecifyKind(model.NextEndorsementDate.Value, DateTimeKind.Utc) : null,
                LastIntermediateDate = model.LastIntermediateDate.HasValue ? DateTime.SpecifyKind(model.LastIntermediateDate.Value, DateTimeKind.Utc) : null,
                NextIntermediateDate = model.NextIntermediateDate.HasValue ? DateTime.SpecifyKind(model.NextIntermediateDate.Value, DateTimeKind.Utc) : null,
                IssuedBy = model.IssuedBy,
                IssuedAt = model.IssuedAt,
                IssuingAuthority = model.IssuingAuthority,
                SurveyType = model.SurveyType,
                SurveyorName = model.SurveyorName,
                SurveyCompany = model.SurveyCompany,
                Status = model.Status,
                IsOriginal = model.IsOriginal,
                Remarks = model.Remarks,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
            };

            // Handle file upload
            if (model.DocumentFile != null && model.DocumentFile.Length > 0)
            {
                var uploadResult = await UploadDocumentFile(model.DocumentFile, "vessel-certificates", certificate.Id.ToString());
                if (uploadResult.Success)
                {
                    certificate.DocumentPath = uploadResult.FilePath;
                    certificate.DocumentSize = uploadResult.FileSize;
                    certificate.DocumentHash = uploadResult.FileHash;
                }
                else
                {
                    ModelState.AddModelError("DocumentFile", uploadResult.ErrorMessage!);
                }
            }

            if (ModelState.IsValid)
            {
                _context.VesselCertificates.Add(certificate);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = _languageService.GetText("CertificateCreatedSuccessfully");
                return RedirectToAction(nameof(VesselDocuments));
            }
        }

        await LoadVesselDocumentViewData();
        return View(model);
    }

    // GET: Documents/Download/5
    public async Task<IActionResult> Download(Guid id, string type = "user")
    {
        string? filePath = null;
        string? fileName = null;

        if (type == "user")
        {
            var document = await _context.UserComplianceDocuments
                .FirstOrDefaultAsync(d => d.Id == id && !d.IsDeleted);
            
            if (document != null && !string.IsNullOrEmpty(document.DocumentPath))
            {
                filePath = document.DocumentPath;
                fileName = $"{document.DocumentType}_{document.DocumentNumber}{Path.GetExtension(document.DocumentPath)}";
            }
        }
        else
        {
            var certificate = await _context.VesselCertificates
                .Include(c => c.CertificateType)
                .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
            
            if (certificate != null && !string.IsNullOrEmpty(certificate.DocumentPath))
            {
                filePath = certificate.DocumentPath;
                fileName = $"{certificate.CertificateType!.Name}_{certificate.CertificateNumber}{Path.GetExtension(certificate.DocumentPath)}";
            }
        }

        if (string.IsNullOrEmpty(filePath))
        {
            return NotFound();
        }

        var fullPath = Path.Combine(_environment.WebRootPath, filePath);
        if (!System.IO.File.Exists(fullPath))
        {
            return NotFound();
        }

        var contentType = GetContentType(fullPath);
        return PhysicalFile(fullPath, contentType, fileName);
    }

    // POST: Documents/Verify/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Verify(Guid id, string status, string? notes)
    {
        var document = await _context.UserComplianceDocuments.FindAsync(id);
        if (document == null || document.IsDeleted)
        {
            return NotFound();
        }

        document.VerificationStatus = status;
        document.VerificationNotes = notes;
        document.VerifiedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");
        document.VerifiedDate = DateTime.UtcNow;
        document.ModifiedDate = DateTime.UtcNow;
        document.ModifiedBy = document.VerifiedBy;

        _context.Update(document);
        await _context.SaveChangesAsync();

        TempData["SuccessMessage"] = _languageService.GetText("DocumentVerifiedSuccessfully");
        return RedirectToAction(nameof(UserDocuments));
    }

    // Private helper methods
    private async Task<List<string>> GetDistinctDocumentTypes()
    {
        return await _context.UserComplianceDocuments
            .Where(d => !d.IsDeleted)
            .Select(d => d.DocumentType)
            .Distinct()
            .OrderBy(t => t)
            .ToListAsync();
    }

    private async Task LoadUserDocumentViewData()
    {
        ViewData["Users"] = await _context.Users
            .Where(u => u.IsActive)
            .OrderBy(u => u.FirstName)
            .ThenBy(u => u.LastName)
            .ToListAsync();

        ViewData["Countries"] = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>
        {
            new() { Value = "TR", Text = "Turkey" },
            new() { Value = "US", Text = "United States" },
            new() { Value = "GB", Text = "United Kingdom" },
            new() { Value = "GR", Text = "Greece" },
            new() { Value = "MT", Text = "Malta" },
            new() { Value = "PA", Text = "Panama" },
            new() { Value = "LR", Text = "Liberia" },
            new() { Value = "MH", Text = "Marshall Islands" },
            new() { Value = "SG", Text = "Singapore" },
            new() { Value = "HK", Text = "Hong Kong" }
        };

        ViewData["DocumentTypes"] = new List<string>
        {
            "Passport",
            "Seaman Book",
            "Certificate of Competency",
            "Certificate of Proficiency",
            "Medical Certificate",
            "STCW Basic Training",
            "STCW Advanced Fire Fighting",
            "STCW Medical First Aid",
            "STCW Medical Care",
            "STCW Survival Craft",
            "STCW Fast Rescue Boat",
            "Security Awareness",
            "Ship Security Officer",
            "Tanker Familiarization",
            "Advanced Tanker Training",
            "GMDSS Certificate",
            "Visa",
            "Yellow Fever Certificate",
            "Drug & Alcohol Test"
        };
    }

    private async Task LoadVesselDocumentViewData()
    {
        ViewData["Vessels"] = await _context.Vessels
            .Where(v => v.IsActive && !v.IsDeleted)
            .OrderBy(v => v.VesselName)
            .ToListAsync();

        ViewData["CertificateTypes"] = await _context.CertificateTypes
            .Where(ct => ct.IsActive)
            .OrderBy(ct => ct.SortOrder)
            .ThenBy(ct => ct.Name)
            .ToListAsync();

        ViewData["Statuses"] = new List<string>
        {
            "Valid",
            "Expired",
            "Suspended",
            "Cancelled",
            "Pending Renewal"
        };
    }

    private async Task<(bool Success, string? FilePath, long FileSize, string? FileHash, string? ErrorMessage)> 
        UploadDocumentFile(IFormFile file, string folder, string documentId)
    {
        try
        {
            // Validate file
            var maxFileSize = _configuration.GetValue<long>("FileUpload:MaxFileSize", 10 * 1024 * 1024); // 10MB default
            if (file.Length > maxFileSize)
            {
                return (false, null, 0, null, _languageService.GetText("FileSizeExceedsLimit"));
            }

            var allowedExtensions = _configuration.GetValue<string>("FileUpload:AllowedExtensions", ".pdf,.jpg,.jpeg,.png,.doc,.docx")
                .Split(',', StringSplitOptions.RemoveEmptyEntries);
            
            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            if (!allowedExtensions.Contains(fileExtension))
            {
                return (false, null, 0, null, _languageService.GetText("FileTypeNotAllowed"));
            }

            // Create upload directory
            var uploadPath = Path.Combine(_environment.WebRootPath, "uploads", folder, DateTime.UtcNow.ToString("yyyy-MM"));
            Directory.CreateDirectory(uploadPath);

            // Generate unique filename
            var fileName = $"{documentId}_{DateTime.UtcNow.Ticks}{fileExtension}";
            var filePath = Path.Combine(uploadPath, fileName);

            // Calculate file hash
            string fileHash;
            using (var stream = file.OpenReadStream())
            {
                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = await sha256.ComputeHashAsync(stream);
                    fileHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower()!;
                }
                stream.Seek(0, SeekOrigin.Begin);

                // Save file
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await stream.CopyToAsync(fileStream);
                }
            }

            // Return relative path for storage
            var relativePath = Path.Combine("uploads", folder, DateTime.UtcNow.ToString("yyyy-MM"), fileName)
                .Replace(Path.DirectorySeparatorChar, '/');

            return (true, relativePath, file.Length, fileHash, null);
        }
        catch (Exception ex)
        {
            return (false, null, 0, null, ex.Message);
        }
    }

    private string GetContentType(string path)
    {
        var types = new Dictionary<string, string>
        {
            {".pdf", "application/pdf"},
            {".doc", "application/vnd.ms-word"},
            {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
            {".jpg", "image/jpeg"},
            {".jpeg", "image/jpeg"},
            {".png", "image/png"},
            {".gif", "image/gif"}
        };

        var ext = Path.GetExtension(path).ToLowerInvariant();
        return types.ContainsKey(ext) ? types[ext] : "application/octet-stream";
    }
}