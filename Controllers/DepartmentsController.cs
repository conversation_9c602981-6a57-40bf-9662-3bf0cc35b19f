using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class DepartmentsController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;

    public DepartmentsController(ApplicationDbContext context, ILanguageService languageService)
    {
        _context = context;
        _languageService = languageService;
    }

    // GET: Departments
    public async Task<IActionResult> Index(Guid? departmentTypeId = null, bool? isCritical = null)
    {
        var currentCulture = HttpContext.Features.Get<IRequestCultureFeature>()?.RequestCulture.Culture.Name ?? "en";
        var currentLanguage = await _context.Languages.FirstOrDefaultAsync(l => l.Code == currentCulture);
        var languageId = currentLanguage?.Id ?? Guid.Empty;

        var departmentsQuery = _context.Departments
            .Include(d => d.DepartmentType)
                .ThenInclude(dt => dt!.Translations)
            .Include(d => d.Translations)
            .Include(d => d.CreatorUser)
            .Include(d => d.LastModifierUser)
            .Where(d => !d.IsDeleted);

        if (departmentTypeId.HasValue)
        {
            departmentsQuery = departmentsQuery.Where(d => d.DepartmentTypeId == departmentTypeId);
        }

        if (isCritical.HasValue)
        {
            departmentsQuery = departmentsQuery.Where(d => d.IsCritical == isCritical);
        }

        var departmentsList = await departmentsQuery
            .OrderBy(d => d.DepartmentType!.Name)
            .ThenBy(d => d.Name)
            .ToListAsync();

        var departments = departmentsList.Select(d => {
            var deptTranslation = d.Translations.FirstOrDefault(t => t.LanguageId == languageId);
            var typeTranslation = d.DepartmentType?.Translations.FirstOrDefault(t => t.LanguageId == languageId);
            
            return new DepartmentViewModel
            {
                DepartmentId = d.DepartmentId,
                Name = deptTranslation?.Name ?? d.Name,
                DepartmentTypeId = d.DepartmentTypeId,
                DepartmentTypeName = typeTranslation?.Name ?? d.DepartmentType?.Name,
                IsCritical = d.IsCritical,
                CreatorUserId = d.CreatorUserId,
                CreatorUserName = d.CreatorUser != null ? d.CreatorUser.FirstName + " " + d.CreatorUser.LastName : "",
                CreationTime = d.CreationTime,
                LastModificationTime = d.LastModificationTime,
                LastModifierUserName = d.LastModifierUser != null ? d.LastModifierUser.FirstName + " " + d.LastModifierUser.LastName : null,
                EmployeeCount = _context.Users.Count(u => u.DepartmentId == d.DepartmentId && !u.IsDeleted)
            };
        }).ToList();

        // Load filter data
        ViewBag.DepartmentTypes = await _context.DepartmentTypes
            .Where(dt => dt.IsActive)
            .OrderBy(dt => dt.Name)
            .ToListAsync();
        
        ViewBag.SelectedDepartmentTypeId = departmentTypeId;
        ViewBag.SelectedIsCritical = isCritical;

        return View(departments);
    }

    // GET: Departments/Details/5
    public async Task<IActionResult> Details(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var currentCulture = HttpContext.Features.Get<IRequestCultureFeature>()?.RequestCulture.Culture.Name ?? "en";
        var currentLanguage = await _context.Languages.FirstOrDefaultAsync(l => l.Code == currentCulture);
        var languageId = currentLanguage?.Id ?? Guid.Empty;

        var department = await _context.Departments
            .Include(d => d.DepartmentType)
                .ThenInclude(dt => dt!.Translations)
            .Include(d => d.Translations)
            .Include(d => d.CreatorUser)
            .Include(d => d.LastModifierUser)
            .Include(d => d.DeleterUser)
            .FirstOrDefaultAsync(d => d.DepartmentId == id && !d.IsDeleted);

        if (department == null)
        {
            return NotFound();
        }

        var model = new DepartmentViewModel
        {
            DepartmentId = department.DepartmentId,
            Name = department.Translations.FirstOrDefault(t => t.LanguageId == languageId)?.Name ?? department.Name,
            DepartmentTypeId = department.DepartmentTypeId,
            DepartmentTypeName = department.DepartmentType?.Translations.FirstOrDefault(t => t.LanguageId == languageId)?.Name ?? department.DepartmentType?.Name,
            IsCritical = department.IsCritical,
            CreatorUserId = department.CreatorUserId,
            CreatorUserName = department.CreatorUser != null ? department.CreatorUser.FirstName + " " + department.CreatorUser.LastName : "",
            CreationTime = department.CreationTime,
            LastModifierUserId = department.LastModifierUserId,
            LastModifierUserName = department.LastModifierUser != null ? department.LastModifierUser.FirstName + " " + department.LastModifierUser.LastName : null,
            LastModificationTime = department.LastModificationTime,
            EmployeeCount = await _context.Users.CountAsync(u => u.DepartmentId == department.DepartmentId && !u.IsDeleted)
        };

        // Get employees in this department
        ViewBag.Employees = await _context.Users
            .Where(u => u.DepartmentId == department.DepartmentId && !u.IsDeleted)
            .OrderBy(u => u.FirstName)
            .ThenBy(u => u.LastName)
            .ToListAsync();

        return View(model);
    }

    // GET: Departments/Create
    public async Task<IActionResult> Create()
    {
        await LoadViewData();
        return View(new DepartmentViewModel());
    }

    // POST: Departments/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(DepartmentViewModel model)
    {
        if (ModelState.IsValid)
        {
            var currentUserId = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");
            
            var department = new Department
            {
                DepartmentId = Guid.NewGuid(),
                Name = model.Name,
                DepartmentTypeId = model.DepartmentTypeId,
                IsCritical = model.IsCritical,
                CreatorUserId = currentUserId,
                CreationTime = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc)
            };

            _context.Departments.Add(department);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("DepartmentCreatedSuccessfully");
            return RedirectToAction(nameof(Details), new { id = department.DepartmentId });
        }

        await LoadViewData();
        return View(model);
    }

    // GET: Departments/Edit/5
    public async Task<IActionResult> Edit(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var department = await _context.Departments
            .Include(d => d.DepartmentType)
            .FirstOrDefaultAsync(d => d.DepartmentId == id && !d.IsDeleted);
            
        if (department == null)
        {
            return NotFound();
        }

        var model = new DepartmentViewModel
        {
            DepartmentId = department.DepartmentId,
            Name = department.Name,
            DepartmentTypeId = department.DepartmentTypeId,
            IsCritical = department.IsCritical
        };

        await LoadViewData();
        return View(model);
    }

    // POST: Departments/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(Guid id, DepartmentViewModel model)
    {
        if (id != model.DepartmentId)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                var department = await _context.Departments.FindAsync(id);
                if (department == null || department.IsDeleted)
                {
                    return NotFound();
                }

                var currentUserId = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");

                department.Name = model.Name;
                department.DepartmentTypeId = model.DepartmentTypeId;
                department.IsCritical = model.IsCritical;
                department.LastModifierUserId = currentUserId;
                department.LastModificationTime = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);

                _context.Update(department);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = _languageService.GetText("DepartmentUpdatedSuccessfully");
                return RedirectToAction(nameof(Details), new { id = department.DepartmentId });
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DepartmentExists(model.DepartmentId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
        }
        
        await LoadViewData();
        return View(model);
    }

    // POST: Departments/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(Guid id)
    {
        var department = await _context.Departments.FindAsync(id);
        if (department != null && !department.IsDeleted)
        {
            // Check if there are employees in this department
            var employeeCount = await _context.Users.CountAsync(u => u.DepartmentId == id && !u.IsDeleted);
            if (employeeCount > 0)
            {
                TempData["ErrorMessage"] = _languageService.GetText("CannotDeleteDepartmentWithEmployees");
                return RedirectToAction(nameof(Details), new { id });
            }

            var currentUserId = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");

            // Soft delete
            department.IsDeleted = true;
            department.DeleterUserId = currentUserId;
            department.DeletionTime = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);
            
            _context.Update(department);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("DepartmentDeletedSuccessfully");
        }
        
        return RedirectToAction(nameof(Index));
    }

    private bool DepartmentExists(Guid id)
    {
        return _context.Departments.Any(e => e.DepartmentId == id && !e.IsDeleted);
    }

    private async Task LoadViewData()
    {
        var currentCulture = HttpContext.Features.Get<IRequestCultureFeature>()?.RequestCulture.Culture.Name ?? "en";
        var currentLanguage = await _context.Languages.FirstOrDefaultAsync(l => l.Code == currentCulture);
        var languageId = currentLanguage?.Id ?? Guid.Empty;

        var departmentTypes = await _context.DepartmentTypes
            .Include(dt => dt.Translations)
            .Where(dt => dt.IsActive)
            .OrderBy(dt => dt.Name)
            .ToListAsync();

        // Update names with translations
        foreach (var dt in departmentTypes)
        {
            var translation = dt.Translations.FirstOrDefault(t => t.LanguageId == languageId);
            if (translation != null)
            {
                dt.Name = translation.Name;
                dt.Description = translation.Description;
            }
        }

        ViewData["DepartmentTypes"] = departmentTypes;
    }

    // GET: Departments/Translations/5
    public async Task<IActionResult> Translations(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var department = await _context.Departments
            .Include(d => d.DepartmentType)
            .Include(d => d.Translations)
            .FirstOrDefaultAsync(d => d.DepartmentId == id && !d.IsDeleted);

        if (department == null)
        {
            return NotFound();
        }

        var languages = await _context.Languages
            .Where(l => l.IsActive)
            .OrderBy(l => l.SortOrder)
            .ToListAsync();

        var viewModel = new DepartmentTranslationViewModel
        {
            DepartmentId = department.DepartmentId,
            OriginalName = department.Name,
            DepartmentTypeName = department.DepartmentType?.Name ?? "",
            Translations = languages.Select(lang => new DepartmentTranslationItem
            {
                LanguageId = lang.Id,
                LanguageCode = lang.Code,
                LanguageName = lang.Name,
                Name = department.Translations.FirstOrDefault(t => t.LanguageId == lang.Id)?.Name
            }).ToList()
        };

        return View(viewModel);
    }

    // POST: Departments/Translations/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Translations(Guid id, IFormCollection form)
    {
        try
        {
            var existingTranslations = await _context.DepartmentTranslations
                .Where(t => t.DepartmentId == id)
                .ToListAsync();

            // Parse form data manually to bypass validation
            var languages = await _context.Languages
                .Where(l => l.IsActive)
                .OrderBy(l => l.SortOrder)
                .ToListAsync();

            for (int i = 0; i < languages.Count; i++)
            {
                var languageIdKey = $"Translations[{i}].LanguageId";
                var nameKey = $"Translations[{i}].Name";

                if (form.ContainsKey(languageIdKey) && Guid.TryParse(form[languageIdKey], out var languageId))
                {
                    var name = form[nameKey].ToString().Trim();
                    var existingTranslation = existingTranslations.FirstOrDefault(t => t.LanguageId == languageId);

                    if (!string.IsNullOrWhiteSpace(name))
                    {
                        if (existingTranslation != null)
                        {
                            // Update existing translation
                            existingTranslation.Name = name;
                            _context.DepartmentTranslations.Update(existingTranslation);
                        }
                        else
                        {
                            // Create new translation
                            var newTranslation = new DepartmentTranslation
                            {
                                DepartmentTranslationId = Guid.NewGuid(),
                                DepartmentId = id,
                                LanguageId = languageId,
                                Name = name
                            };
                            _context.DepartmentTranslations.Add(newTranslation);
                        }
                    }
                    else if (existingTranslation != null)
                    {
                        // Remove existing translation if the input is empty
                        _context.DepartmentTranslations.Remove(existingTranslation);
                    }
                }
            }

            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = _languageService.GetText("TranslationsUpdatedSuccessfully");
            return RedirectToAction(nameof(Details), new { id });
        }
        catch (Exception ex)
        {
            TempData["ErrorMessage"] = $"Error saving translations: {ex.Message}";
            return RedirectToAction(nameof(Translations), new { id });
        }
    }
}