using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class VoyagesController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;

    public VoyagesController(ApplicationDbContext context, ILanguageService languageService)
    {
        _context = context;
        _languageService = languageService;
    }

    // GET: Voyages
    public async Task<IActionResult> Index(string? status = null)
    {
        var voyagesQuery = _context.VesselVoyages
            .Include(v => v.Vessel)
            .Include(v => v.PortCalls)
            .Where(v => !v.IsDeleted);

        if (!string.IsNullOrEmpty(status))
        {
            voyagesQuery = voyagesQuery.Where(v => v.Status == status);
        }

        var voyages = await voyagesQuery
            .OrderByDescending(v => v.DepartureDate)
            .Select(v => new VoyageViewModel
            {
                Id = v.Id,
                VesselId = v.VesselId,
                VesselName = v.Vessel!.VesselName,
                VoyageNumber = v.VoyageNumber,
                VoyageType = v.VoyageType,
                DeparturePort = v.DeparturePort,
                DepartureDate = v.DepartureDate,
                ArrivalPort = v.ArrivalPort,
                Eta = v.Eta,
                Ata = v.Ata,
                Status = v.Status,
                PortCallCount = v.PortCalls.Count,
                CurrentLocation = v.Status == "IN_PORT" 
                    ? v.PortCalls.Where(pc => pc.Ata.HasValue && !pc.Atd.HasValue).Select(pc => pc.PortName).FirstOrDefault()
                    : v.Status == "AT_SEA" ? "At Sea" : null
            })
            .ToListAsync();

        ViewBag.Statuses = await _context.VoyageStatuses
            .Where(s => s.IsActive)
            .OrderBy(s => s.SortOrder)
            .ToListAsync();
        
        ViewBag.SelectedStatus = status;

        return View(voyages);
    }

    // GET: Voyages/Details/5
    public async Task<IActionResult> Details(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var voyage = await _context.VesselVoyages
            .Include(v => v.Vessel)
            .Include(v => v.PortCalls.OrderBy(pc => pc.PortSequence))
            .FirstOrDefaultAsync(v => v.Id == id && !v.IsDeleted);

        if (voyage == null)
        {
            return NotFound();
        }

        var model = new VoyageViewModel
        {
            Id = voyage.Id,
            VesselId = voyage.VesselId,
            VesselName = voyage.Vessel?.VesselName,
            VoyageNumber = voyage.VoyageNumber,
            VoyageType = voyage.VoyageType,
            DeparturePort = voyage.DeparturePort,
            DepartureDate = voyage.DepartureDate,
            ArrivalPort = voyage.ArrivalPort,
            Eta = voyage.Eta,
            Ata = voyage.Ata,
            DistanceMiles = voyage.DistanceMiles,
            CargoDescription = voyage.CargoDescription,
            CargoQuantity = voyage.CargoQuantity,
            CargoUnit = voyage.CargoUnit,
            CharterPartyRef = voyage.CharterPartyRef,
            Status = voyage.Status,
            Remarks = voyage.Remarks,
            PortCallCount = voyage.PortCalls.Count,
            CurrentLocation = voyage.Status == "IN_PORT" 
                ? voyage.PortCalls.Where(pc => pc.Ata.HasValue && !pc.Atd.HasValue).Select(pc => pc.PortName).FirstOrDefault()
                : voyage.Status == "AT_SEA" ? "At Sea" : null
        };

        ViewBag.PortCalls = voyage.PortCalls.ToList();

        return View(model);
    }

    // GET: Voyages/Create
    public async Task<IActionResult> Create(Guid? vesselId = null)
    {
        await LoadViewData();
        
        var model = new VoyageViewModel();
        if (vesselId.HasValue)
        {
            model.VesselId = vesselId.Value;
        }

        // Generate voyage number
        var lastVoyage = await _context.VesselVoyages
            .Where(v => v.VesselId == vesselId)
            .OrderByDescending(v => v.CreatedDate)
            .FirstOrDefaultAsync();

        if (lastVoyage != null)
        {
            // Extract number from last voyage and increment
            var lastNumber = System.Text.RegularExpressions.Regex.Match(lastVoyage.VoyageNumber, @"\d+").Value;
            if (int.TryParse(lastNumber, out int number))
            {
                model.VoyageNumber = $"V{(number + 1).ToString("D4")}";
            }
        }
        else
        {
            model.VoyageNumber = "V0001";
        }
        
        return View(model);
    }

    // POST: Voyages/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(VoyageViewModel model)
    {
        if (ModelState.IsValid)
        {
            var voyage = new VesselVoyage
            {
                Id = Guid.NewGuid(),
                VesselId = model.VesselId,
                VoyageNumber = model.VoyageNumber,
                VoyageType = model.VoyageType,
                DeparturePort = model.DeparturePort,
                DepartureDate = DateTime.SpecifyKind(model.DepartureDate, DateTimeKind.Utc),
                ArrivalPort = model.ArrivalPort,
                Eta = model.Eta.HasValue ? DateTime.SpecifyKind(model.Eta.Value, DateTimeKind.Utc) : null,
                DistanceMiles = model.DistanceMiles,
                CargoDescription = model.CargoDescription,
                CargoQuantity = model.CargoQuantity,
                CargoUnit = model.CargoUnit,
                CharterPartyRef = model.CharterPartyRef,
                Status = model.Status,
                Remarks = model.Remarks,
                CreatedDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc),
                CreatedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
            };

            _context.VesselVoyages.Add(voyage);
            
            // Add port calls for departure and arrival
            _context.VoyagePortCalls.Add(new VoyagePortCall
            {
                Id = Guid.NewGuid(),
                VoyageId = voyage.Id,
                PortName = model.DeparturePort,
                PortType = "Loading",
                Etd = DateTime.SpecifyKind(model.DepartureDate, DateTimeKind.Utc),
                PortSequence = 1,
                CreatedDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc),
                CreatedBy = voyage.CreatedBy
            });

            _context.VoyagePortCalls.Add(new VoyagePortCall
            {
                Id = Guid.NewGuid(),
                VoyageId = voyage.Id,
                PortName = model.ArrivalPort,
                PortType = "Discharging",
                Eta = model.Eta.HasValue ? DateTime.SpecifyKind(model.Eta.Value, DateTimeKind.Utc) : null,
                PortSequence = 2,
                CreatedDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc),
                CreatedBy = voyage.CreatedBy
            });

            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("VoyageCreatedSuccessfully");
            return RedirectToAction(nameof(Details), new { id = voyage.Id });
        }
        
        await LoadViewData();
        return View(model);
    }

    // GET: Voyages/Edit/5
    public async Task<IActionResult> Edit(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var voyage = await _context.VesselVoyages
            .Include(v => v.Vessel)
            .FirstOrDefaultAsync(v => v.Id == id && !v.IsDeleted);
            
        if (voyage == null)
        {
            return NotFound();
        }

        var model = new VoyageViewModel
        {
            Id = voyage.Id,
            VesselId = voyage.VesselId,
            VoyageNumber = voyage.VoyageNumber,
            VoyageType = voyage.VoyageType,
            DeparturePort = voyage.DeparturePort,
            DepartureDate = voyage.DepartureDate,
            ArrivalPort = voyage.ArrivalPort,
            Eta = voyage.Eta,
            Ata = voyage.Ata,
            DistanceMiles = voyage.DistanceMiles,
            CargoDescription = voyage.CargoDescription,
            CargoQuantity = voyage.CargoQuantity,
            CargoUnit = voyage.CargoUnit,
            CharterPartyRef = voyage.CharterPartyRef,
            Status = voyage.Status,
            Remarks = voyage.Remarks
        };

        await LoadViewData();
        return View(model);
    }

    // POST: Voyages/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(Guid id, VoyageViewModel model)
    {
        if (id != model.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                var voyage = await _context.VesselVoyages.FindAsync(id);
                if (voyage == null || voyage.IsDeleted)
                {
                    return NotFound();
                }

                voyage.VoyageNumber = model.VoyageNumber;
                voyage.VoyageType = model.VoyageType;
                voyage.DeparturePort = model.DeparturePort;
                voyage.DepartureDate = DateTime.SpecifyKind(model.DepartureDate, DateTimeKind.Utc);
                voyage.ArrivalPort = model.ArrivalPort;
                voyage.Eta = model.Eta.HasValue ? DateTime.SpecifyKind(model.Eta.Value, DateTimeKind.Utc) : null;
                voyage.Ata = model.Ata.HasValue ? DateTime.SpecifyKind(model.Ata.Value, DateTimeKind.Utc) : null;
                voyage.DistanceMiles = model.DistanceMiles;
                voyage.CargoDescription = model.CargoDescription;
                voyage.CargoQuantity = model.CargoQuantity;
                voyage.CargoUnit = model.CargoUnit;
                voyage.CharterPartyRef = model.CharterPartyRef;
                voyage.Status = model.Status;
                voyage.Remarks = model.Remarks;
                voyage.ModifiedDate = DateTime.UtcNow;
                voyage.ModifiedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");

                _context.Update(voyage);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = _languageService.GetText("VoyageUpdatedSuccessfully");
                return RedirectToAction(nameof(Details), new { id = voyage.Id });
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!VoyageExists(model.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
        }
        
        await LoadViewData();
        return View(model);
    }

    // POST: Voyages/UpdateStatus/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateStatus(Guid id, string status)
    {
        var voyage = await _context.VesselVoyages.FindAsync(id);
        if (voyage == null || voyage.IsDeleted)
        {
            return NotFound();
        }

        voyage.Status = status;
        voyage.ModifiedDate = DateTime.UtcNow;
        voyage.ModifiedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");

        // If completed, set ATA
        if (status == "COMPLETED" && !voyage.Ata.HasValue)
        {
            voyage.Ata = DateTime.UtcNow;
        }

        _context.Update(voyage);
        await _context.SaveChangesAsync();
        
        TempData["SuccessMessage"] = _languageService.GetText("VoyageStatusUpdated");
        return RedirectToAction(nameof(Details), new { id });
    }

    // GET: Voyages/PortCalls/5
    public async Task<IActionResult> PortCalls(Guid id)
    {
        var voyage = await _context.VesselVoyages
            .Include(v => v.Vessel)
            .Include(v => v.PortCalls.OrderBy(pc => pc.PortSequence))
            .FirstOrDefaultAsync(v => v.Id == id && !v.IsDeleted);

        if (voyage == null)
        {
            return NotFound();
        }

        return View(voyage);
    }

    // GET: Voyages/AddPortCall/5
    public async Task<IActionResult> AddPortCall(Guid voyageId)
    {
        var voyage = await _context.VesselVoyages.FindAsync(voyageId);
        if (voyage == null || voyage.IsDeleted)
        {
            return NotFound();
        }

        var model = new PortCallViewModel
        {
            VoyageId = voyageId,
            PortSequence = await _context.VoyagePortCalls
                .Where(pc => pc.VoyageId == voyageId)
                .MaxAsync(pc => (int?)pc.PortSequence) ?? 0 + 1
        };

        ViewBag.VoyageNumber = voyage.VoyageNumber;
        return View(model);
    }

    // POST: Voyages/AddPortCall
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddPortCall(PortCallViewModel model)
    {
        if (ModelState.IsValid)
        {
            var portCall = new VoyagePortCall
            {
                Id = Guid.NewGuid(),
                VoyageId = model.VoyageId,
                PortName = model.PortName,
                PortCountry = model.PortCountry,
                PortType = model.PortType,
                Eta = model.Eta.HasValue ? DateTime.SpecifyKind(model.Eta.Value, DateTimeKind.Utc) : null,
                Etb = model.Etb.HasValue ? DateTime.SpecifyKind(model.Etb.Value, DateTimeKind.Utc) : null,
                Etd = model.Etd.HasValue ? DateTime.SpecifyKind(model.Etd.Value, DateTimeKind.Utc) : null,
                BerthName = model.BerthName,
                AgentName = model.AgentName,
                AgentContact = model.AgentContact,
                PortActivities = model.PortActivities,
                PortSequence = model.PortSequence,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
            };

            _context.VoyagePortCalls.Add(portCall);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("PortCallAddedSuccessfully");
            return RedirectToAction(nameof(PortCalls), new { id = model.VoyageId });
        }

        return View(model);
    }

    // POST: Voyages/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(Guid id)
    {
        var voyage = await _context.VesselVoyages.FindAsync(id);
        if (voyage != null)
        {
            voyage.IsDeleted = true;
            voyage.DeletedDate = DateTime.UtcNow;
            voyage.DeletedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");
            
            _context.Update(voyage);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("VoyageDeletedSuccessfully");
        }
        
        return RedirectToAction(nameof(Index));
    }

    private bool VoyageExists(Guid id)
    {
        return _context.VesselVoyages.Any(e => e.Id == id);
    }

    private async Task LoadViewData()
    {
        ViewData["Vessels"] = await _context.Vessels
            .Where(v => v.IsActive && !v.IsDeleted)
            .OrderBy(v => v.VesselName)
            .ToListAsync();

        ViewData["Statuses"] = await _context.VoyageStatuses
            .Where(s => s.IsActive)
            .OrderBy(s => s.SortOrder)
            .ToListAsync();

        ViewData["PortTypes"] = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>
        {
            new() { Value = "Loading", Text = _languageService.GetText("Loading") },
            new() { Value = "Discharging", Text = _languageService.GetText("Discharging") },
            new() { Value = "Bunkering", Text = _languageService.GetText("Bunkering") },
            new() { Value = "Transit", Text = _languageService.GetText("Transit") },
            new() { Value = "Other", Text = _languageService.GetText("Other") }
        };
    }
}