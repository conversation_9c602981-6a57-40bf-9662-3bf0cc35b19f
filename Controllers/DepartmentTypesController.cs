using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class DepartmentTypesController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;

    public DepartmentTypesController(ApplicationDbContext context, ILanguageService languageService)
    {
        _context = context;
        _languageService = languageService;
    }

    // GET: DepartmentTypes
    public async Task<IActionResult> Index()
    {
        var currentCulture = HttpContext.Features.Get<IRequestCultureFeature>()?.RequestCulture.Culture.Name ?? "en";
        var currentLanguage = await _context.Languages.FirstOrDefaultAsync(l => l.Code == currentCulture);
        var languageId = currentLanguage?.Id ?? Guid.Empty;

        var departmentTypes = await _context.DepartmentTypes
            .Include(dt => dt.Departments)
            .Include(dt => dt.Translations)
                .ThenInclude(t => t.Language)
            .OrderBy(dt => dt.Name)
            .ToListAsync();

        var viewModels = departmentTypes.Select(dt => {
            var translation = dt.Translations.FirstOrDefault(t => t.LanguageId == languageId);
            
            return new DepartmentTypeViewModel
            {
                DepartmentTypeId = dt.DepartmentTypeId,
                Name = translation?.Name ?? dt.Name,
                Description = translation?.Description ?? dt.Description,
                IsActive = dt.IsActive,
                DepartmentCount = dt.Departments.Count(d => !d.IsDeleted)
            };
        }).ToList();

        return View(viewModels);
    }

    // GET: DepartmentTypes/Details/5
    public async Task<IActionResult> Details(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var departmentType = await _context.DepartmentTypes
            .Include(dt => dt.Departments.Where(d => !d.IsDeleted))
            .FirstOrDefaultAsync(dt => dt.DepartmentTypeId == id);

        if (departmentType == null)
        {
            return NotFound();
        }

        var model = new DepartmentTypeViewModel
        {
            DepartmentTypeId = departmentType.DepartmentTypeId,
            Name = departmentType.Name,
            Description = departmentType.Description,
            IsActive = departmentType.IsActive,
            DepartmentCount = departmentType.Departments.Count
        };

        return View(model);
    }

    // GET: DepartmentTypes/Create
    public IActionResult Create()
    {
        return View(new DepartmentTypeViewModel());
    }

    // POST: DepartmentTypes/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(DepartmentTypeViewModel model)
    {
        if (ModelState.IsValid)
        {
            var departmentType = new DepartmentType
            {
                DepartmentTypeId = Guid.NewGuid(),
                Name = model.Name,
                Description = model.Description,
                IsActive = model.IsActive
            };

            _context.DepartmentTypes.Add(departmentType);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("DepartmentTypeCreatedSuccessfully");
            return RedirectToAction(nameof(Index));
        }

        return View(model);
    }

    // GET: DepartmentTypes/Edit/5
    public async Task<IActionResult> Edit(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var departmentType = await _context.DepartmentTypes.FindAsync(id);
        if (departmentType == null)
        {
            return NotFound();
        }

        var model = new DepartmentTypeViewModel
        {
            DepartmentTypeId = departmentType.DepartmentTypeId,
            Name = departmentType.Name,
            Description = departmentType.Description,
            IsActive = departmentType.IsActive
        };

        return View(model);
    }

    // POST: DepartmentTypes/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(Guid id, DepartmentTypeViewModel model)
    {
        if (id != model.DepartmentTypeId)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                var departmentType = await _context.DepartmentTypes.FindAsync(id);
                if (departmentType == null)
                {
                    return NotFound();
                }

                departmentType.Name = model.Name;
                departmentType.Description = model.Description;
                departmentType.IsActive = model.IsActive;

                _context.Update(departmentType);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = _languageService.GetText("DepartmentTypeUpdatedSuccessfully");
                return RedirectToAction(nameof(Index));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DepartmentTypeExists(model.DepartmentTypeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
        }
        return View(model);
    }

    // POST: DepartmentTypes/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(Guid id)
    {
        var departmentType = await _context.DepartmentTypes
            .Include(dt => dt.Departments)
            .FirstOrDefaultAsync(dt => dt.DepartmentTypeId == id);
            
        if (departmentType != null)
        {
            // Check if there are any departments using this type
            if (departmentType.Departments.Any(d => !d.IsDeleted))
            {
                TempData["ErrorMessage"] = _languageService.GetText("CannotDeleteDepartmentTypeWithDepartments");
                return RedirectToAction(nameof(Index));
            }

            _context.DepartmentTypes.Remove(departmentType);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("DepartmentTypeDeletedSuccessfully");
        }
        
        return RedirectToAction(nameof(Index));
    }

    private bool DepartmentTypeExists(Guid id)
    {
        return _context.DepartmentTypes.Any(e => e.DepartmentTypeId == id);
    }

    // GET: DepartmentTypes/Translations/5
    public async Task<IActionResult> Translations(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var departmentType = await _context.DepartmentTypes
            .Include(dt => dt.Translations)
            .FirstOrDefaultAsync(dt => dt.DepartmentTypeId == id);

        if (departmentType == null)
        {
            return NotFound();
        }

        var languages = await _context.Languages
            .Where(l => l.IsActive)
            .OrderBy(l => l.SortOrder)
            .ToListAsync();

        var viewModel = new DepartmentTypeTranslationViewModel
        {
            DepartmentTypeId = departmentType.DepartmentTypeId,
            OriginalName = departmentType.Name,
            OriginalDescription = departmentType.Description,
            Translations = languages.Select(lang => new TranslationItem
            {
                LanguageId = lang.Id,
                LanguageCode = lang.Code,
                LanguageName = lang.Name,
                Name = departmentType.Translations.FirstOrDefault(t => t.LanguageId == lang.Id)?.Name ?? "",
                Description = departmentType.Translations.FirstOrDefault(t => t.LanguageId == lang.Id)?.Description
            }).ToList()
        };

        return View(viewModel);
    }

    // POST: DepartmentTypes/Translations/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Translations(Guid id, DepartmentTypeTranslationViewModel model)
    {
        if (id != model.DepartmentTypeId)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                // Get existing translations
                var existingTranslations = await _context.DepartmentTypeTranslations
                    .Where(t => t.DepartmentTypeId == id)
                    .ToListAsync();

                // Update or create translations
                foreach (var translation in model.Translations)
                {
                    if (!string.IsNullOrWhiteSpace(translation.Name))
                    {
                        var existing = existingTranslations
                            .FirstOrDefault(t => t.LanguageId == translation.LanguageId);

                        if (existing != null)
                        {
                            // Update existing
                            existing.Name = translation.Name;
                            existing.Description = translation.Description;
                            _context.Update(existing);
                        }
                        else
                        {
                            // Create new
                            var newTranslation = new DepartmentTypeTranslation
                            {
                                DepartmentTypeTranslationId = Guid.NewGuid(),
                                DepartmentTypeId = id,
                                LanguageId = translation.LanguageId,
                                Name = translation.Name,
                                Description = translation.Description
                            };
                            _context.DepartmentTypeTranslations.Add(newTranslation);
                        }
                    }
                    else
                    {
                        // Remove translation if name is empty
                        var existing = existingTranslations
                            .FirstOrDefault(t => t.LanguageId == translation.LanguageId);
                        if (existing != null)
                        {
                            _context.DepartmentTypeTranslations.Remove(existing);
                        }
                    }
                }

                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = _languageService.GetText("TranslationsUpdatedSuccessfully");
                return RedirectToAction(nameof(Details), new { id });
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DepartmentTypeExists(model.DepartmentTypeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
        }

        // Reload languages if model is invalid
        var languages = await _context.Languages
            .Where(l => l.IsActive)
            .OrderBy(l => l.SortOrder)
            .ToListAsync();

        // Ensure language info is populated
        foreach (var translation in model.Translations)
        {
            var lang = languages.FirstOrDefault(l => l.Id == translation.LanguageId);
            if (lang != null)
            {
                translation.LanguageCode = lang.Code;
                translation.LanguageName = lang.Name;
            }
        }

        return View(model);
    }
}