using System.Diagnostics;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.ViewModels;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        var dashboardData = new DashboardViewModel
        {
            // Fleet Statistics
            TotalVessels = await _context.Vessels.CountAsync(v => v.IsActive && !v.IsDeleted),
            ActiveVessels = await _context.Vessels
                .Include(v => v.VesselStatusNavigation)
                .CountAsync(v => v.IsActive && !v.IsDeleted && v.IsInFleet),
            
            // Crew Statistics
            TotalCrew = await _context.Users.CountAsync(u => u.IsActive),
            CrewOnboard = await _context.VesselCrewAssignments
                .CountAsync(vca => vca.Status == "Onboard" && vca.ActualSignOffDate == null && !vca.IsDeleted),
            
            // Voyage Statistics
            ActiveVoyages = await _context.VesselVoyages
                .CountAsync(v => !v.IsDeleted && (v.Status == "IN_PROGRESS" || v.Status == "AT_SEA")),
            CompletedVoyagesThisMonth = await _context.VesselVoyages
                .CountAsync(v => !v.IsDeleted && v.Status == "COMPLETED" && 
                    v.Ata.HasValue && v.Ata.Value.Month == DateTime.UtcNow.Month && v.Ata.Value.Year == DateTime.UtcNow.Year),
            
            // Document Expiry Alerts
            ExpiredDocuments = await _context.Users
                .CountAsync(u => u.IsActive && 
                    ((u.PassportExpiryDate.HasValue && u.PassportExpiryDate.Value <= DateTime.UtcNow.Date) ||
                     (u.SeamanBookExpiryDate.HasValue && u.SeamanBookExpiryDate.Value <= DateTime.UtcNow.Date))),
            
            ExpiringDocuments = await _context.Users
                .CountAsync(u => u.IsActive && 
                    ((u.PassportExpiryDate.HasValue && u.PassportExpiryDate.Value > DateTime.UtcNow.Date && u.PassportExpiryDate.Value <= DateTime.UtcNow.Date.AddDays(90)) ||
                     (u.SeamanBookExpiryDate.HasValue && u.SeamanBookExpiryDate.Value > DateTime.UtcNow.Date && u.SeamanBookExpiryDate.Value <= DateTime.UtcNow.Date.AddDays(90)))),
            
            // Recent Activities
            RecentVoyages = await _context.VesselVoyages
                .Include(v => v.Vessel)
                .Where(v => !v.IsDeleted)
                .OrderByDescending(v => v.CreatedDate)
                .Take(5)
                .Select(v => new VoyageViewModel
                {
                    Id = v.Id,
                    VoyageNumber = v.VoyageNumber,
                    VesselName = v.Vessel!.VesselName,
                    DeparturePort = v.DeparturePort,
                    ArrivalPort = v.ArrivalPort,
                    DepartureDate = v.DepartureDate,
                    Status = v.Status
                })
                .ToListAsync(),
            
            // Vessel Status Overview
            VesselsByStatus = await _context.Vessels
                .Include(v => v.VesselStatusNavigation)
                .Where(v => v.IsActive && !v.IsDeleted)
                .GroupBy(v => v.VesselStatusNavigation != null ? v.VesselStatusNavigation.Name : "Unknown")
                .Select(g => new VesselStatusCount
                {
                    Status = g.Key,
                    Count = g.Count()
                })
                .ToListAsync(),
            
            // Crew by Department
            CrewByDepartment = await _context.Users
                .Where(u => u.IsActive && u.DepartmentId.HasValue)
                .GroupBy(u => u.DepartmentId)
                .Select(g => new DepartmentCrewCount
                {
                    DepartmentId = g.Key!.Value,
                    DepartmentName = _context.CrewDepartments
                        .Where(d => d.Id == g.Key)
                        .Select(d => d.Name)
                        .FirstOrDefault() ?? "Unknown",
                    Count = g.Count()
                })
                .ToListAsync()
        };
        
        return View(dashboardData);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [HttpPost]
    public IActionResult SetLanguage(string culture, string returnUrl)
    {
        Response.Cookies.Append(
            CookieRequestCultureProvider.DefaultCookieName,
            CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
            new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
        );

        if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
        {
            return Redirect(returnUrl);
        }
        
        return RedirectToAction("Index");
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
