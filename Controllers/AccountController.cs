using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;
using System.Security.Claims;

namespace SMS_Maritime_Web.Controllers;

public class AccountController : Controller
{
    private readonly Services.IAuthenticationService _authService;
    private readonly ILanguageService _languageService;

    public AccountController(Services.IAuthenticationService authService, ILanguageService languageService)
    {
        _authService = authService;
        _languageService = languageService;
    }

    [HttpGet]
    public async Task<IActionResult> Login(string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;
        ViewBag.Languages = await _languageService.GetActiveLanguagesAsync();
        ViewBag.CurrentLanguage = System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        return View(new LoginViewModel { ReturnUrl = returnUrl });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Login(LoginViewModel model)
    {
        if (!ModelState.IsValid)
        {
            ViewBag.Languages = await _languageService.GetActiveLanguagesAsync();
            ViewBag.CurrentLanguage = System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            return View(model);
        }

        var potentialUser = await _authService.GetUserByUsernameAsync(model.Username);
        
        if (potentialUser != null && await _authService.IsLockedOutAsync(potentialUser))
        {
            ModelState.AddModelError(string.Empty, "Account is locked due to multiple failed login attempts. Please try again later.");
            ViewBag.Languages = await _languageService.GetActiveLanguagesAsync();
            ViewBag.CurrentLanguage = System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            return View(model);
        }
        
        var user = await _authService.ValidateUserAsync(model.Username, model.Password);
        if (user != null)
        {
            await _authService.UpdateLastLoginAsync(user.Id);
            
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim("FirstName", user.FirstName),
                new Claim("LastName", user.LastName)
            };

            var roles = await _authService.GetUserRolesAsync(user.Id);
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var authProperties = new AuthenticationProperties
            {
                IsPersistent = model.RememberMe,
                ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(60)
            };

            await HttpContext.SignInAsync(
                CookieAuthenticationDefaults.AuthenticationScheme,
                new ClaimsPrincipal(claimsIdentity),
                authProperties);

            if (user.MustChangePassword)
            {
                return RedirectToAction("ChangePassword", "Account");
            }

            return RedirectToLocal(model.ReturnUrl);
        }

        ModelState.AddModelError(string.Empty, "Invalid username or password.");
        ViewBag.Languages = await _languageService.GetActiveLanguagesAsync();
        ViewBag.CurrentLanguage = System.Globalization.CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        return RedirectToAction("Login");
    }

    public IActionResult AccessDenied()
    {
        return View();
    }

    [HttpGet]
    public IActionResult ForgotPassword()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ForgotPassword(ForgotPasswordViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var user = await _authService.GetUserByUsernameAsync(model.Username);
        if (user != null && user.Email == model.Email)
        {
            var code = await _authService.GeneratePasswordResetCodeAsync(user);
            // TODO: Send email with reset code
            TempData["SuccessMessage"] = "Password reset code has been sent to your email.";
            return RedirectToAction("ResetPassword");
        }

        TempData["ErrorMessage"] = "Invalid username or email.";
        return View(model);
    }

    [HttpGet]
    public IActionResult ResetPassword()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ResetPassword(ResetPasswordViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var user = await _authService.GetUserByUsernameAsync(model.Username);
        if (user != null)
        {
            var result = await _authService.ResetPasswordAsync(user, model.Code, model.NewPassword);
            if (result)
            {
                TempData["SuccessMessage"] = "Password has been reset successfully. Please login with your new password.";
                return RedirectToAction("Login");
            }
        }

        ModelState.AddModelError(string.Empty, "Invalid reset code or username.");
        return View(model);
    }

    [HttpGet]
    [Microsoft.AspNetCore.Authorization.Authorize]
    public async Task<IActionResult> ChangePassword()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return RedirectToAction("Login");
        }

        var user = await _authService.GetUserByIdAsync(Guid.Parse(userId));
        if (user == null)
        {
            return RedirectToAction("Login");
        }

        ViewBag.MustChangePassword = user.MustChangePassword;
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Microsoft.AspNetCore.Authorization.Authorize]
    public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return RedirectToAction("Login");
        }

        var user = await _authService.GetUserByIdAsync(Guid.Parse(userId));
        if (user != null)
        {
            var result = await _authService.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);
            if (result)
            {
                TempData["SuccessMessage"] = "Password has been changed successfully.";
                return RedirectToAction("Index", "Home");
            }
        }

        ModelState.AddModelError(string.Empty, "Current password is incorrect.");
        return View(model);
    }

    [HttpPost]
    public IActionResult SetLanguage(string culture, string returnUrl)
    {
        Response.Cookies.Append(
            CookieRequestCultureProvider.DefaultCookieName,
            CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
            new CookieOptions { Expires = DateTimeOffset.UtcNow.AddYears(1) }
        );

        // Validate and clean the return URL
        if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
        {
            return Redirect(returnUrl);
        }
        
        // If returnUrl is not valid, redirect to login page
        return RedirectToAction("Login");
    }

    private IActionResult RedirectToLocal(string? returnUrl)
    {
        if (Url.IsLocalUrl(returnUrl))
        {
            return Redirect(returnUrl);
        }
        else
        {
            return RedirectToAction(nameof(HomeController.Index), "Home");
        }
    }
}