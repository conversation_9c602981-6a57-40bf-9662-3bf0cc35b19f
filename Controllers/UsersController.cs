using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;
using System.Security.Cryptography;
using System.Text;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class UsersController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;

    public UsersController(ApplicationDbContext context, ILanguageService languageService)
    {
        _context = context;
        _languageService = languageService;
    }

    // GET: Users
    public async Task<IActionResult> Index(UserFilterViewModel? filter = null)
    {
        var query = _context.Users
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .AsQueryable();

        // Apply filters
        if (filter != null)
        {
            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                query = query.Where(u => 
                    u.Username.Contains(filter.SearchTerm) ||
                    u.Email.Contains(filter.SearchTerm) ||
                    u.FirstName.Contains(filter.SearchTerm) ||
                    u.LastName.Contains(filter.SearchTerm) ||
                    (u.EmployeeCode != null && u.EmployeeCode.Contains(filter.SearchTerm)));
            }

            if (filter.DepartmentId.HasValue)
            {
                query = query.Where(u => u.DepartmentId == filter.DepartmentId.Value);
            }

            if (filter.RoleId.HasValue)
            {
                query = query.Where(u => u.UserRoles.Any(ur => ur.RoleId == filter.RoleId.Value));
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(u => u.IsActive == filter.IsActive.Value);
            }

            if (filter.HasExpiredDocuments.HasValue && filter.HasExpiredDocuments.Value)
            {
                var today = DateTime.UtcNow.Date;
                query = query.Where(u => 
                    (u.PassportExpiryDate.HasValue && u.PassportExpiryDate.Value <= today) ||
                    (u.SeamanBookExpiryDate.HasValue && u.SeamanBookExpiryDate.Value <= today));
            }
        }

        var users = await query
            .OrderBy(u => u.FirstName)
            .ThenBy(u => u.LastName)
            .Select(u => new UserViewModel
            {
                Id = u.Id,
                Username = u.Username,
                Email = u.Email,
                FirstName = u.FirstName,
                LastName = u.LastName,
                DisplayName = u.DisplayName,
                PhoneNumber = u.PhoneNumber,
                IsActive = u.IsActive,
                EmailConfirmed = u.EmailConfirmed,
                DepartmentId = u.DepartmentId,
                DepartmentName = u.DepartmentId.HasValue ? _context.CrewDepartments
                    .Where(d => d.Id == u.DepartmentId.Value)
                    .Select(d => d.Name)
                    .FirstOrDefault() : null,
                EmployeeCode = u.EmployeeCode,
                RoleNames = u.UserRoles.Select(ur => ur.Role.Name).ToList(),
                CreatedDate = u.CreatedDate,
                ModifiedDate = u.ModifiedDate,
                PassportExpiryDate = u.PassportExpiryDate,
                SeamanBookExpiryDate = u.SeamanBookExpiryDate
            })
            .ToListAsync();

        // Load filter data
        ViewBag.Departments = await _context.CrewDepartments
            .Where(d => d.IsActive)
            .OrderBy(d => d.Name)
            .ToListAsync();

        ViewBag.Roles = await _context.Roles
            .OrderBy(r => r.Name)
            .ToListAsync();

        ViewBag.Filter = filter;

        return View(users);
    }

    // GET: Users/Details/5
    public async Task<IActionResult> Details(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var user = await _context.Users
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .Include(u => u.VesselCrewAssignments)
                .ThenInclude(vca => vca.Vessel)
            .FirstOrDefaultAsync(u => u.Id == id);

        if (user == null)
        {
            return NotFound();
        }

        var model = new UserViewModel
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            DisplayName = user.DisplayName,
            PhoneNumber = user.PhoneNumber,
            IsActive = user.IsActive,
            EmailConfirmed = user.EmailConfirmed,
            PreferredLanguage = user.PreferredLanguage,
            DepartmentId = user.DepartmentId,
            DepartmentName = user.DepartmentId.HasValue ? await _context.CrewDepartments
                .Where(d => d.Id == user.DepartmentId.Value)
                .Select(d => d.Name)
                .FirstOrDefaultAsync() : null,
            EmployeeCode = user.EmployeeCode,
            HireDate = user.HireDate,
            DateOfBirth = user.DateOfBirth,
            Nationality = user.Nationality,
            PlaceOfBirth = user.PlaceOfBirth,
            PassportNumber = user.PassportNumber,
            PassportExpiryDate = user.PassportExpiryDate,
            SeamanBookNumber = user.SeamanBookNumber,
            SeamanBookExpiryDate = user.SeamanBookExpiryDate,
            RoleNames = user.UserRoles.Select(ur => ur.Role.Name).ToList(),
            CreatedDate = user.CreatedDate,
            ModifiedDate = user.ModifiedDate
        };

        ViewBag.VesselAssignments = user.VesselCrewAssignments
            .OrderByDescending(vca => vca.SignOnDate)
            .ToList();

        return View(model);
    }

    // GET: Users/Create
    public async Task<IActionResult> Create()
    {
        await LoadViewData();
        return View();
    }

    // POST: Users/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(UserViewModel model)
    {
        if (ModelState.IsValid)
        {
            // Check if username exists
            if (await _context.Users.AnyAsync(u => u.Username == model.Username))
            {
                ModelState.AddModelError("Username", _languageService.GetText("UsernameAlreadyExists"));
            }

            // Check if email exists
            if (await _context.Users.AnyAsync(u => u.Email == model.Email))
            {
                ModelState.AddModelError("Email", _languageService.GetText("EmailAlreadyExists"));
            }

            if (ModelState.IsValid)
            {
                var user = new User
                {
                    Id = Guid.NewGuid(),
                    Username = model.Username,
                    Email = model.Email,
                    PasswordHash = !string.IsNullOrEmpty(model.Password) ? HashPassword(model.Password) : null,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    DisplayName = model.DisplayName ?? $"{model.FirstName} {model.LastName}",
                    PhoneNumber = model.PhoneNumber,
                    IsActive = model.IsActive,
                    EmailConfirmed = model.EmailConfirmed,
                    PreferredLanguage = model.PreferredLanguage,
                    DepartmentId = model.DepartmentId,
                    EmployeeCode = model.EmployeeCode,
                    HireDate = model.HireDate,
                    DateOfBirth = model.DateOfBirth,
                    Nationality = model.Nationality,
                    PlaceOfBirth = model.PlaceOfBirth,
                    PassportNumber = model.PassportNumber,
                    PassportExpiryDate = model.PassportExpiryDate,
                    SeamanBookNumber = model.SeamanBookNumber,
                    SeamanBookExpiryDate = model.SeamanBookExpiryDate,
                    CreatedDate = DateTime.UtcNow
                };

                _context.Users.Add(user);

                // Add user roles
                foreach (var roleId in model.SelectedRoleIds)
                {
                    _context.UserRoles.Add(new UserRole
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        RoleId = roleId,
                        AssignedDate = DateTime.UtcNow,
                        AssignedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
                    });
                }

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = _languageService.GetText("UserCreatedSuccessfully");
                return RedirectToAction(nameof(Details), new { id = user.Id });
            }
        }

        await LoadViewData();
        return View(model);
    }

    // GET: Users/Edit/5
    public async Task<IActionResult> Edit(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var user = await _context.Users
            .Include(u => u.UserRoles)
            .FirstOrDefaultAsync(u => u.Id == id);

        if (user == null)
        {
            return NotFound();
        }

        var model = new UserViewModel
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            DisplayName = user.DisplayName,
            PhoneNumber = user.PhoneNumber,
            IsActive = user.IsActive,
            EmailConfirmed = user.EmailConfirmed,
            PreferredLanguage = user.PreferredLanguage,
            DepartmentId = user.DepartmentId,
            EmployeeCode = user.EmployeeCode,
            HireDate = user.HireDate,
            DateOfBirth = user.DateOfBirth,
            Nationality = user.Nationality,
            PlaceOfBirth = user.PlaceOfBirth,
            PassportNumber = user.PassportNumber,
            PassportExpiryDate = user.PassportExpiryDate,
            SeamanBookNumber = user.SeamanBookNumber,
            SeamanBookExpiryDate = user.SeamanBookExpiryDate,
            SelectedRoleIds = user.UserRoles.Select(ur => ur.RoleId).ToList()
        };

        await LoadViewData();
        return View(model);
    }

    // POST: Users/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(Guid id, UserViewModel model)
    {
        if (id != model.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                    .FirstOrDefaultAsync(u => u.Id == id);

                if (user == null)
                {
                    return NotFound();
                }

                // Check if username is taken by another user
                if (await _context.Users.AnyAsync(u => u.Username == model.Username && u.Id != id))
                {
                    ModelState.AddModelError("Username", _languageService.GetText("UsernameAlreadyExists"));
                }

                // Check if email is taken by another user
                if (await _context.Users.AnyAsync(u => u.Email == model.Email && u.Id != id))
                {
                    ModelState.AddModelError("Email", _languageService.GetText("EmailAlreadyExists"));
                }

                if (ModelState.IsValid)
                {
                    user.Username = model.Username;
                    user.Email = model.Email;
                    user.FirstName = model.FirstName;
                    user.LastName = model.LastName;
                    user.DisplayName = model.DisplayName ?? $"{model.FirstName} {model.LastName}";
                    user.PhoneNumber = model.PhoneNumber;
                    user.IsActive = model.IsActive;
                    user.EmailConfirmed = model.EmailConfirmed;
                    user.PreferredLanguage = model.PreferredLanguage;
                    user.DepartmentId = model.DepartmentId;
                    user.EmployeeCode = model.EmployeeCode;
                    user.HireDate = model.HireDate;
                    user.DateOfBirth = model.DateOfBirth;
                    user.Nationality = model.Nationality;
                    user.PlaceOfBirth = model.PlaceOfBirth;
                    user.PassportNumber = model.PassportNumber;
                    user.PassportExpiryDate = model.PassportExpiryDate;
                    user.SeamanBookNumber = model.SeamanBookNumber;
                    user.SeamanBookExpiryDate = model.SeamanBookExpiryDate;
                    user.ModifiedDate = DateTime.UtcNow;

                    // Update password if provided
                    if (!string.IsNullOrEmpty(model.Password))
                    {
                        user.PasswordHash = HashPassword(model.Password);
                    }

                    // Update roles
                    var currentRoleIds = user.UserRoles.Select(ur => ur.RoleId).ToList();
                    var toRemove = currentRoleIds.Except(model.SelectedRoleIds);
                    var toAdd = model.SelectedRoleIds.Except(currentRoleIds);

                    foreach (var roleId in toRemove)
                    {
                        var userRole = user.UserRoles.FirstOrDefault(ur => ur.RoleId == roleId);
                        if (userRole != null)
                        {
                            _context.UserRoles.Remove(userRole);
                        }
                    }

                    foreach (var roleId in toAdd)
                    {
                        _context.UserRoles.Add(new UserRole
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            RoleId = roleId,
                            AssignedDate = DateTime.UtcNow,
                            AssignedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
                        });
                    }

                    _context.Update(user);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = _languageService.GetText("UserUpdatedSuccessfully");
                    return RedirectToAction(nameof(Details), new { id = user.Id });
                }
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserExists(model.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
        }

        await LoadViewData();
        return View(model);
    }

    // POST: Users/ToggleActive/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ToggleActive(Guid id)
    {
        var user = await _context.Users.FindAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        user.IsActive = !user.IsActive;
        user.ModifiedDate = DateTime.UtcNow;

        _context.Update(user);
        await _context.SaveChangesAsync();

        TempData["SuccessMessage"] = user.IsActive
            ? _languageService.GetText("UserActivatedSuccessfully")
            : _languageService.GetText("UserDeactivatedSuccessfully");

        return RedirectToAction(nameof(Index));
    }

    // POST: Users/ResetPassword/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ResetPassword(Guid id, string newPassword)
    {
        var user = await _context.Users.FindAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        user.PasswordHash = HashPassword(newPassword);
        user.ModifiedDate = DateTime.UtcNow;

        _context.Update(user);
        await _context.SaveChangesAsync();

        TempData["SuccessMessage"] = _languageService.GetText("PasswordResetSuccessfully");
        return RedirectToAction(nameof(Details), new { id });
    }

    private bool UserExists(Guid id)
    {
        return _context.Users.Any(e => e.Id == id);
    }

    private async Task LoadViewData()
    {
        ViewData["Departments"] = await _context.CrewDepartments
            .Where(d => d.IsActive)
            .OrderBy(d => d.Name)
            .ToListAsync();

        ViewData["Roles"] = await _context.Roles
            .OrderBy(r => r.Name)
            .ToListAsync();

        ViewData["Languages"] = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>
        {
            new() { Value = "en", Text = _languageService.GetText("English") },
            new() { Value = "tr", Text = _languageService.GetText("Turkish") }
        };
    }

    private string HashPassword(string password)
    {
        using (var sha256 = SHA256.Create())
        {
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
        }
    }
}