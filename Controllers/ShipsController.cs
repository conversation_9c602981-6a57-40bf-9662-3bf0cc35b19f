using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Data;
using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;
using SMS_Maritime_Web.ViewModels;

namespace SMS_Maritime_Web.Controllers;

[Authorize]
public class ShipsController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly ILanguageService _languageService;

    public ShipsController(ApplicationDbContext context, ILanguageService languageService)
    {
        _context = context;
        _languageService = languageService;
    }

    // GET: Ships
    public async Task<IActionResult> Index()
    {
        var vessels = await _context.Vessels
            .Include(v => v.VesselTypeNavigation)
            .Include(v => v.VesselStatusNavigation)
            .Include(v => v.FlagState)
            .Include(v => v.Fleet)
            .Where(v => !v.IsDeleted)
            .OrderBy(v => v.VesselName)
            .ToListAsync();

        return View(vessels);
    }

    // GET: Ships/Details/5
    public async Task<IActionResult> Details(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var vessel = await _context.Vessels
            .Include(v => v.VesselTypeNavigation)
            .Include(v => v.VesselStatusNavigation)
            .Include(v => v.VesselClass)
            .Include(v => v.FlagState)
            .Include(v => v.Fleet)
            .FirstOrDefaultAsync(v => v.Id == id && !v.IsDeleted);

        if (vessel == null)
        {
            return NotFound();
        }

        return View(vessel);
    }

    // GET: Ships/Create
    public async Task<IActionResult> Create()
    {
        await LoadViewData();
        return View(new VesselViewModel());
    }
    
    // GET: Ships/CreateWizard
    public async Task<IActionResult> CreateWizard()
    {
        await LoadViewData();
        return View(new VesselViewModel());
    }
    
    // GET: Ships/CreateAccordion
    public async Task<IActionResult> CreateAccordion()
    {
        await LoadViewData();
        return View(new VesselViewModel());
    }
    
    // GET: Ships/CreateSimple
    public async Task<IActionResult> CreateSimple()
    {
        await LoadViewData();
        return View(new VesselViewModel());
    }

    // POST: Ships/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(VesselViewModel model)
    {
        if (ModelState.IsValid)
        {
            var vessel = new Vessel
            {
                Id = Guid.NewGuid(),
                VesselCode = model.VesselCode,
                VesselName = model.VesselName,
                ImoNumber = model.ImoNumber,
                MmsiNumber = model.MmsiNumber,
                CallSign = model.CallSign,
                VesselTypeId = model.VesselTypeId,
                VesselStatusId = model.VesselStatusId,
                VesselClassId = model.VesselClassId,
                FlagStateId = model.FlagStateId,
                PortOfRegistry = model.PortOfRegistry,
                BuildYear = model.BuildYear,
                DeliveryDate = model.DeliveryDate.HasValue ? DateOnly.FromDateTime(model.DeliveryDate.Value) : null,
                GrossTonnage = model.GrossTonnage,
                NetTonnage = model.NetTonnage,
                DeadweightSummer = model.DeadweightSummer,
                LengthOverall = model.LengthOverall,
                BreadthMoulded = model.BreadthMoulded,
                DepthMoulded = model.DepthMoulded,
                DraftSummer = model.DraftSummer,
                SpeedService = model.SpeedService,
                SpeedMaximum = model.SpeedMaximum,
                // Engine Info
                MainEngineMaker = model.MainEngineMaker,
                MainEngineModel = model.MainEngineModel,
                MainEnginePowerKw = model.MainEnginePowerKw,
                EngineType = model.EngineType,
                EnginePower = model.EnginePower,
                // Additional Technical
                Flag = model.Flag,
                VesselType = model.VesselType,
                Beam = model.Beam,
                Draft = model.Draft,
                Deadweight = model.Deadweight,
                ClassSociety = model.ClassSociety,
                // Operational
                Status = model.Status ?? "Active",
                CurrentLocation = model.CurrentLocation,
                NextPort = model.NextPort,
                Eta = model.Eta?.ToUniversalTime(),
                LastDrydockDate = model.LastDrydockDate,
                NextDrydockDate = model.NextDrydockDate,
                FleetId = model.FleetId,
                // Environmental
                EcoDesign = model.EcoDesign,
                ScrubberFitted = model.ScrubberFitted,
                BallastWaterTreatment = model.BallastWaterTreatment,
                // Communication
                SatellitePhone = model.SatellitePhone,
                SatelliteEmail = model.SatelliteEmail,
                // Management
                TechnicalManagerCompany = model.TechnicalManagerCompany,
                CommercialManagerCompany = model.CommercialManagerCompany,
                CrewManagerCompany = model.CrewManagerCompany,
                // Trading
                TradingArea = model.TradingArea,
                TradeType = model.TradeType,
                // Additional
                FormerNames = model.FormerNames,
                BuilderName = model.BuilderName,
                BuilderYard = model.BuilderYard,
                HullNumber = model.HullNumber,
                // Capacity
                CargoCapacityGrain = model.CargoCapacityGrain,
                CargoCapacityBale = model.CargoCapacityBale,
                CargoHolds = model.CargoHolds,
                CargoHatches = model.CargoHatches,
                TeuCapacity = model.TeuCapacity,
                PassengersCapacity = model.PassengersCapacity,
                // Status Flags
                IsActive = model.IsActive,
                IsOwned = model.IsOwned,
                IsInFleet = model.IsInFleet,
                CreatedDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc),
                CreatedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "")
            };

            _context.Add(vessel);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("VesselCreatedSuccessfully");
            return RedirectToAction(nameof(Index));
        }
        
        await LoadViewData();
        return View(model);
    }

    // GET: Ships/Edit/5
    public async Task<IActionResult> Edit(Guid? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var vessel = await _context.Vessels.FindAsync(id);
        if (vessel == null || vessel.IsDeleted)
        {
            return NotFound();
        }

        var model = new VesselViewModel
        {
            Id = vessel.Id,
            VesselCode = vessel.VesselCode,
            VesselName = vessel.VesselName,
            ImoNumber = vessel.ImoNumber,
            MmsiNumber = vessel.MmsiNumber,
            CallSign = vessel.CallSign,
            VesselTypeId = vessel.VesselTypeId,
            VesselStatusId = vessel.VesselStatusId,
            VesselClassId = vessel.VesselClassId,
            FlagStateId = vessel.FlagStateId,
            PortOfRegistry = vessel.PortOfRegistry,
            BuildYear = vessel.BuildYear,
            DeliveryDate = vessel.DeliveryDate?.ToDateTime(TimeOnly.MinValue),
            GrossTonnage = vessel.GrossTonnage,
            NetTonnage = vessel.NetTonnage,
            DeadweightSummer = vessel.DeadweightSummer,
            LengthOverall = vessel.LengthOverall,
            BreadthMoulded = vessel.BreadthMoulded,
            DepthMoulded = vessel.DepthMoulded,
            DraftSummer = vessel.DraftSummer,
            SpeedService = vessel.SpeedService,
            SpeedMaximum = vessel.SpeedMaximum,
            // Engine Info
            MainEngineMaker = vessel.MainEngineMaker,
            MainEngineModel = vessel.MainEngineModel,
            MainEnginePowerKw = vessel.MainEnginePowerKw,
            EngineType = vessel.EngineType,
            EnginePower = vessel.EnginePower,
            // Additional Technical
            Flag = vessel.Flag,
            VesselType = vessel.VesselType,
            Beam = vessel.Beam,
            Draft = vessel.Draft,
            Deadweight = vessel.Deadweight,
            ClassSociety = vessel.ClassSociety,
            // Operational
            Status = vessel.Status,
            CurrentLocation = vessel.CurrentLocation,
            NextPort = vessel.NextPort,
            Eta = vessel.Eta,
            LastDrydockDate = vessel.LastDrydockDate,
            NextDrydockDate = vessel.NextDrydockDate,
            FleetId = vessel.FleetId,
            // Environmental
            EcoDesign = vessel.EcoDesign,
            ScrubberFitted = vessel.ScrubberFitted,
            BallastWaterTreatment = vessel.BallastWaterTreatment,
            // Communication
            SatellitePhone = vessel.SatellitePhone,
            SatelliteEmail = vessel.SatelliteEmail,
            // Management
            TechnicalManagerCompany = vessel.TechnicalManagerCompany,
            CommercialManagerCompany = vessel.CommercialManagerCompany,
            CrewManagerCompany = vessel.CrewManagerCompany,
            // Trading
            TradingArea = vessel.TradingArea,
            TradeType = vessel.TradeType,
            // Additional
            FormerNames = vessel.FormerNames,
            BuilderName = vessel.BuilderName,
            BuilderYard = vessel.BuilderYard,
            HullNumber = vessel.HullNumber,
            // Capacity
            CargoCapacityGrain = vessel.CargoCapacityGrain,
            CargoCapacityBale = vessel.CargoCapacityBale,
            CargoHolds = vessel.CargoHolds,
            CargoHatches = vessel.CargoHatches,
            TeuCapacity = vessel.TeuCapacity,
            PassengersCapacity = vessel.PassengersCapacity,
            // Status Flags
            IsActive = vessel.IsActive,
            IsOwned = vessel.IsOwned,
            IsInFleet = vessel.IsInFleet
        };

        await LoadViewData();
        return View(model);
    }

    // POST: Ships/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(Guid id, VesselViewModel model)
    {
        if (id != model.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                var vessel = await _context.Vessels.FindAsync(id);
                if (vessel == null || vessel.IsDeleted)
                {
                    return NotFound();
                }

                vessel.VesselCode = model.VesselCode;
                vessel.VesselName = model.VesselName;
                vessel.ImoNumber = model.ImoNumber;
                vessel.MmsiNumber = model.MmsiNumber;
                vessel.CallSign = model.CallSign;
                vessel.VesselTypeId = model.VesselTypeId;
                vessel.VesselStatusId = model.VesselStatusId;
                vessel.VesselClassId = model.VesselClassId;
                vessel.FlagStateId = model.FlagStateId;
                vessel.PortOfRegistry = model.PortOfRegistry;
                vessel.BuildYear = model.BuildYear;
                vessel.DeliveryDate = model.DeliveryDate.HasValue ? DateOnly.FromDateTime(model.DeliveryDate.Value) : null;
                vessel.GrossTonnage = model.GrossTonnage;
                vessel.NetTonnage = model.NetTonnage;
                vessel.DeadweightSummer = model.DeadweightSummer;
                vessel.LengthOverall = model.LengthOverall;
                vessel.BreadthMoulded = model.BreadthMoulded;
                vessel.DepthMoulded = model.DepthMoulded;
                vessel.DraftSummer = model.DraftSummer;
                vessel.SpeedService = model.SpeedService;
                vessel.SpeedMaximum = model.SpeedMaximum;
                // Engine Info
                vessel.MainEngineMaker = model.MainEngineMaker;
                vessel.MainEngineModel = model.MainEngineModel;
                vessel.MainEnginePowerKw = model.MainEnginePowerKw;
                vessel.EngineType = model.EngineType;
                vessel.EnginePower = model.EnginePower;
                // Additional Technical
                vessel.Flag = model.Flag;
                vessel.VesselType = model.VesselType;
                vessel.Beam = model.Beam;
                vessel.Draft = model.Draft;
                vessel.Deadweight = model.Deadweight;
                vessel.ClassSociety = model.ClassSociety;
                // Operational
                vessel.Status = model.Status ?? "Active";
                vessel.CurrentLocation = model.CurrentLocation;
                vessel.NextPort = model.NextPort;
                vessel.Eta = model.Eta?.ToUniversalTime();
                vessel.LastDrydockDate = model.LastDrydockDate;
                vessel.NextDrydockDate = model.NextDrydockDate;
                vessel.FleetId = model.FleetId;
                // Environmental
                vessel.EcoDesign = model.EcoDesign;
                vessel.ScrubberFitted = model.ScrubberFitted;
                vessel.BallastWaterTreatment = model.BallastWaterTreatment;
                // Communication
                vessel.SatellitePhone = model.SatellitePhone;
                vessel.SatelliteEmail = model.SatelliteEmail;
                // Management
                vessel.TechnicalManagerCompany = model.TechnicalManagerCompany;
                vessel.CommercialManagerCompany = model.CommercialManagerCompany;
                vessel.CrewManagerCompany = model.CrewManagerCompany;
                // Trading
                vessel.TradingArea = model.TradingArea;
                vessel.TradeType = model.TradeType;
                // Additional
                vessel.FormerNames = model.FormerNames;
                vessel.BuilderName = model.BuilderName;
                vessel.BuilderYard = model.BuilderYard;
                vessel.HullNumber = model.HullNumber;
                // Capacity
                vessel.CargoCapacityGrain = model.CargoCapacityGrain;
                vessel.CargoCapacityBale = model.CargoCapacityBale;
                vessel.CargoHolds = model.CargoHolds;
                vessel.CargoHatches = model.CargoHatches;
                vessel.TeuCapacity = model.TeuCapacity;
                vessel.PassengersCapacity = model.PassengersCapacity;
                // Status Flags
                vessel.IsActive = model.IsActive;
                vessel.IsOwned = model.IsOwned;
                vessel.IsInFleet = model.IsInFleet;
                vessel.ModifiedDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);
                vessel.ModifiedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");

                _context.Update(vessel);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = _languageService.GetText("VesselUpdatedSuccessfully");
                return RedirectToAction(nameof(Index));
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!VesselExists(model.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
        }
        
        await LoadViewData();
        return View(model);
    }

    // POST: Ships/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(Guid id)
    {
        var vessel = await _context.Vessels.FindAsync(id);
        if (vessel != null)
        {
            vessel.IsDeleted = true;
            vessel.DeletedDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc);
            vessel.DeletedBy = Guid.Parse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "");
            
            _context.Update(vessel);
            await _context.SaveChangesAsync();
            
            TempData["SuccessMessage"] = _languageService.GetText("VesselDeletedSuccessfully");
        }
        
        return RedirectToAction(nameof(Index));
    }

    private bool VesselExists(Guid id)
    {
        return _context.Vessels.Any(e => e.Id == id);
    }

    private async Task LoadViewData()
    {
        ViewData["VesselTypes"] = await _context.VesselTypes
            .Where(vt => vt.IsActive)
            .OrderBy(vt => vt.SortOrder)
            .ThenBy(vt => vt.Name)
            .ToListAsync();

        ViewData["VesselStatuses"] = await _context.VesselStatuses
            .Where(vs => vs.IsActive)
            .OrderBy(vs => vs.SortOrder)
            .ThenBy(vs => vs.Name)
            .ToListAsync();

        ViewData["VesselClasses"] = await _context.VesselClasses
            .Where(vc => vc.IsActive)
            .OrderBy(vc => vc.SortOrder)
            .ThenBy(vc => vc.Name)
            .ToListAsync();

        ViewData["FlagStates"] = await _context.FlagStates
            .Where(fs => fs.IsActive)
            .OrderBy(fs => fs.SortOrder)
            .ThenBy(fs => fs.CountryName)
            .ToListAsync();
            
        ViewData["Fleets"] = await _context.Fleets
            .Where(f => f.IsActive)
            .OrderBy(f => f.FleetName)
            .ToListAsync();
    }
}