-- Drop dependent views
DROP VIEW IF EXISTS v_active_crew_assignments CASCADE;
DROP VIEW IF EXISTS v_document_expiry_alerts CASCADE;
DROP VIEW IF EXISTS vw_user_themes CASCADE;

-- Update users table timestamp columns to use timestamp with time zone
ALTER TABLE users 
    ALTER COLUMN created_date TYPE timestamp with time zone USING created_date AT TIME ZONE 'UTC',
    ALTER COLUMN modified_date TYPE timestamp with time zone USING modified_date AT TIME ZONE 'UTC',
    ALTER COLUMN deleted_date TYPE timestamp with time zone USING deleted_date AT TIME ZONE 'UTC',
    ALTER COLUMN last_password_change TYPE timestamp with time zone USING last_password_change AT TIME ZONE 'UTC';

-- Recreate views
CREATE VIEW vw_user_themes AS
 SELECT u.id,
    u.username,
    u.first_name,
    u.last_name,
    u.email,
    ut.theme_name,
    ut.sidebar_color,
    ut.navbar_color,
    ut.is_dark_mode,
    ut.font_size,
    ut.language
   FROM (users u
     LEFT JOIN user_theme_preferences ut ON ((u.id = ut.user_id)));

CREATE VIEW v_active_crew_assignments AS
 SELECT vca.id,
    vca.vessel_id,
    vca.user_id,
    vca.rank_id,
    vca.department,
    vca.sign_on_date,
    vca.expected_sign_off_date,
    vca.actual_sign_off_date,
    vca.sign_on_port,
    vca.sign_off_port,
    vca.status,
    vca.relief_due_date,
    vca.relief_user_id,
    vca.remarks,
    vca.is_deleted,
    v.vessel_name,
    v.imo_number,
    u.first_name,
    u.last_name,
    u.employee_code,
    r.name AS rank_name,
    r.code AS rank_code
   FROM (((vessel_crew_assignments vca
     JOIN vessels v ON ((vca.vessel_id = v.id)))
     JOIN users u ON ((vca.user_id = u.id)))
     JOIN crew_ranks r ON ((vca.rank_id = r.id)))
  WHERE ((vca.status = 'Onboard'::text) AND (NOT vca.is_deleted) AND (v.is_active) AND (NOT v.is_deleted));

CREATE VIEW v_document_expiry_alerts AS
 SELECT 'passport'::text AS document_type,
    u.id AS user_id,
    u.first_name,
    u.last_name,
    u.employee_code,
    u.passport_number AS document_number,
    u.passport_expiry_date AS expiry_date,
    (u.passport_expiry_date - CURRENT_DATE) AS days_until_expiry,
        CASE
            WHEN (u.passport_expiry_date <= CURRENT_DATE) THEN 'expired'::text
            WHEN (u.passport_expiry_date <= (CURRENT_DATE + 30)) THEN 'critical'::text
            WHEN (u.passport_expiry_date <= (CURRENT_DATE + 90)) THEN 'warning'::text
            ELSE 'valid'::text
        END AS status
   FROM users u
  WHERE ((u.passport_expiry_date IS NOT NULL) AND u.is_active AND (NOT u.is_deleted))
UNION ALL
 SELECT 'seaman_book'::text AS document_type,
    u.id AS user_id,
    u.first_name,
    u.last_name,
    u.employee_code,
    u.seaman_book_number AS document_number,
    u.seaman_book_expiry_date AS expiry_date,
    (u.seaman_book_expiry_date - CURRENT_DATE) AS days_until_expiry,
        CASE
            WHEN (u.seaman_book_expiry_date <= CURRENT_DATE) THEN 'expired'::text
            WHEN (u.seaman_book_expiry_date <= (CURRENT_DATE + 30)) THEN 'critical'::text
            WHEN (u.seaman_book_expiry_date <= (CURRENT_DATE + 90)) THEN 'warning'::text
            ELSE 'valid'::text
        END AS status
   FROM users u
  WHERE ((u.seaman_book_expiry_date IS NOT NULL) AND u.is_active AND (NOT u.is_deleted));