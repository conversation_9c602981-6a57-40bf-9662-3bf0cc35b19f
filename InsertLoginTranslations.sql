-- Login page translations for all languages
DO $$
DECLARE
    lang_id UUID;
    lang_code VARCHAR(10);
BEGIN
    -- Spanish translations
    SELECT id INTO lang_id FROM languages WHERE code = 'es' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', 'Iniciar sesión', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', 'Nombre de usuario', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', 'Contraseña', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', 'Recuérdame', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', 'Entrar', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', '<PERSON>rrar sesi<PERSON>', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', 'Ingrese su nombre de usuario', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', 'Ingrese su contraseña', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', 'Panel', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', 'Bienvenido', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', 'Sistema de Gestión de Buques', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', 'Bienvenido al sistema de gestión de buques SMS Maritime.', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', 'Total de Buques', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', 'Viajes Activos', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', 'Tareas Pendientes', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', 'Tripulación Total', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

    -- German translations
    SELECT id INTO lang_id FROM languages WHERE code = 'de' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', 'Anmelden', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', 'Benutzername', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', 'Passwort', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', 'Angemeldet bleiben', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', 'Einloggen', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', 'Abmelden', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', 'Benutzername eingeben', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', 'Passwort eingeben', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', 'Dashboard', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', 'Willkommen', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', 'Schiffsmanagementsystem', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', 'Willkommen beim SMS Maritime Schiffsmanagementsystem.', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', 'Schiffe Gesamt', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', 'Aktive Reisen', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', 'Ausstehende Aufgaben', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', 'Besatzung Gesamt', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

    -- French translations
    SELECT id INTO lang_id FROM languages WHERE code = 'fr' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', 'Connexion', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', 'Nom d''utilisateur', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', 'Mot de passe', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', 'Se souvenir de moi', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', 'Se connecter', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', 'Déconnexion', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', 'Entrez votre nom d''utilisateur', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', 'Entrez votre mot de passe', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', 'Tableau de bord', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', 'Bienvenue', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', 'Système de Gestion des Navires', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', 'Bienvenue dans le système de gestion des navires SMS Maritime.', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', 'Total des Navires', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', 'Voyages Actifs', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', 'Tâches en Attente', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', 'Équipage Total', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

    -- Russian translations
    SELECT id INTO lang_id FROM languages WHERE code = 'ru' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', 'Вход', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', 'Имя пользователя', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', 'Пароль', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', 'Запомнить меня', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', 'Войти', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', 'Выйти', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', 'Введите имя пользователя', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', 'Введите пароль', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', 'Панель управления', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', 'Добро пожаловать', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', 'Система управления судами', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', 'Добро пожаловать в систему управления судами SMS Maritime.', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', 'Всего судов', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', 'Активные рейсы', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', 'Ожидающие задачи', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', 'Всего экипажа', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

    -- Chinese translations
    SELECT id INTO lang_id FROM languages WHERE code = 'zh' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', '登录', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', '用户名', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', '密码', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', '记住我', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', '登入', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', '登出', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', '请输入用户名', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', '请输入密码', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', '仪表板', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', '欢迎', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', '船舶管理系统', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', '欢迎使用SMS Maritime船舶管理系统。', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', '船舶总数', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', '活跃航程', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', '待处理任务', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', '船员总数', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

    -- Arabic translations
    SELECT id INTO lang_id FROM languages WHERE code = 'ar' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', 'تسجيل الدخول', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', 'اسم المستخدم', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', 'كلمة المرور', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', 'تذكرني', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', 'دخول', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', 'تسجيل الخروج', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', 'أدخل اسم المستخدم', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', 'أدخل كلمة المرور', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', 'لوحة القيادة', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', 'مرحباً', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', 'نظام إدارة السفن', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', 'مرحباً بك في نظام إدارة السفن SMS Maritime.', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', 'إجمالي السفن', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', 'الرحلات النشطة', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', 'المهام المعلقة', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', 'إجمالي الطاقم', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

    -- Portuguese translations
    SELECT id INTO lang_id FROM languages WHERE code = 'pt' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', 'Entrar', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', 'Nome de usuário', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', 'Senha', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', 'Lembrar-me', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', 'Entrar', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', 'Sair', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', 'Digite seu nome de usuário', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', 'Digite sua senha', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', 'Painel', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', 'Bem-vindo', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', 'Sistema de Gestão de Navios', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', 'Bem-vindo ao sistema de gestão de navios SMS Maritime.', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', 'Total de Navios', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', 'Viagens Ativas', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', 'Tarefas Pendentes', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', 'Tripulação Total', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;

    -- Dutch translations
    SELECT id INTO lang_id FROM languages WHERE code = 'nl' LIMIT 1;
    INSERT INTO language_texts (id, language_id, text_key, text_value, module, created_date) VALUES
    (gen_random_uuid(), lang_id, 'Login', 'Inloggen', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Username', 'Gebruikersnaam', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Password', 'Wachtwoord', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'RememberMe', 'Onthoud mij', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'SignIn', 'Aanmelden', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Logout', 'Uitloggen', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterUsername', 'Voer uw gebruikersnaam in', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'EnterPassword', 'Voer uw wachtwoord in', 'Account', NOW()),
    (gen_random_uuid(), lang_id, 'Dashboard', 'Dashboard', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'Welcome', 'Welkom', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'ShipManagementSystem', 'Scheepsbeheersysteem', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'WelcomeMessage', 'Welkom bij het SMS Maritime scheepsbeheersysteem.', 'Common', NOW()),
    (gen_random_uuid(), lang_id, 'TotalShips', 'Totaal Schepen', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'ActiveVoyages', 'Actieve Reizen', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'PendingTasks', 'Openstaande Taken', 'Dashboard', NOW()),
    (gen_random_uuid(), lang_id, 'TotalCrew', 'Totale Bemanning', 'Dashboard', NOW())
    ON CONFLICT (language_id, text_key) DO UPDATE SET text_value = EXCLUDED.text_value;
END $$;