# SMS Maritime Web - Voyages Management Test Checklist

## <PERSON><PERSON>, SMS Maritime Web uygulamasının Voyages (Sefer Yönetimi) modülü için kapsamlı test senaryolarını içerir. Test sürecinde Create (Oluşturma), Edit (Düzenleme), View (Görüntüleme) ve List (Listeleme) sayfalarının tüm fonksiyonları kontrol edilecektir.

**Test Başlangıç Tarihi:** 2025-01-06  
**Test URL:** http://localhost:5116/Voyages  
**Test Kullanıcısı:** admin / admin123

## 1. Voyages List (Liste) Sayfası Testleri

### 1.1 Sayfa Erişimi ve Görünüm
- [x] Voyages menüsüne tıklama
- [x] Liste sayfasının açılması
- [x] Sayfa başlığının doğru gö<PERSON>ünmes<PERSON> ("Voyages")
- [x] Ta<PERSON>lo yapı<PERSON>ının kontrolü
- [x] <PERSON><PERSON> başlıklarının doğru gösterimi

### 1.2 Liste İçeriği
- [x] Mevcut voyage kayıtlarının görüntülenmesi (Kayıt yok)
- [x] Voyage Number gösterimi
- [ ] Vessel Name gösterimi
- [ ] Route bilgisi (From Port - To Port)
- [ ] Departure ve Arrival tarihleri
- [ ] Status gösterimi
- [ ] Cargo Type gösterimi

### 1.3 Liste İşlemleri
- [x] "Create New Voyage" butonu kontrolü ("+ Add New")
- [x] Status filtreleme sekmeleri (All, Planned, In Progress, At Sea, In Port, Completed, Cancelled)
- [ ] View (Görüntüle) ikonu (Kayıt olmadığı için test edilemedi)
- [ ] Edit (Düzenle) ikonu (Kayıt olmadığı için test edilemedi)
- [ ] Delete (Sil) ikonu (varsa)
- [ ] Arama özellikleri
- [ ] Sayfalama (pagination) kontrolü

## 2. Create Voyage (Sefer Oluşturma) Testleri

### 2.1 Sayfa Erişimi
- [x] "Create New Voyage" butonuna tıklama
- [x] Create formu açılması
- [x] Form başlığı kontrolü ("Create Voyage")

### 2.2 Voyage Information (Sefer Bilgileri)
- [ ] **Voyage Number** (Zorunlu Alan)
  - [ ] Otomatik oluşturma özelliği
  - [ ] Manuel giriş imkanı
  - [ ] Benzersizlik kontrolü
- [ ] **Vessel Selection** (Zorunlu Alan)
  - [ ] Dropdown listesi
  - [ ] Aktif gemilerin listelenmesi
- [ ] **Voyage Type**
  - [ ] Seçenekler kontrolü (Laden, Ballast, etc.)
- [ ] **Charter Type**
  - [ ] Seçenekler kontrolü (Time Charter, Voyage Charter, etc.)

### 2.3 Route Information (Rota Bilgileri)
- [ ] **Departure Port** (Zorunlu Alan)
  - [ ] Port arama/seçim özelliği
  - [ ] Otomatik tamamlama
- [ ] **Arrival Port** (Zorunlu Alan)
  - [ ] Port arama/seçim özelliği
  - [ ] Otomatik tamamlama
- [ ] **Via Ports** (Ara Limanlar)
  - [ ] Çoklu port ekleme
  - [ ] Port silme özelliği
- [ ] **Distance (NM)**
  - [ ] Sayısal giriş kontrolü
  - [ ] Otomatik hesaplama (varsa)

### 2.4 Schedule Information (Zaman Bilgileri)
- [ ] **ETD (Expected Time of Departure)**
  - [ ] Tarih ve saat seçici
  - [ ] Format kontrolü
- [ ] **ETA (Expected Time of Arrival)**
  - [ ] Tarih ve saat seçici
  - [ ] ETD'den önce olamaz kontrolü
- [ ] **ATD (Actual Time of Departure)**
  - [ ] Opsiyonel alan
- [ ] **ATA (Actual Time of Arrival)**
  - [ ] Opsiyonel alan

### 2.5 Cargo Information (Yük Bilgileri)
- [ ] **Cargo Type**
  - [ ] Dropdown seçenekleri
  - [ ] Çoklu seçim (varsa)
- [ ] **Cargo Description**
  - [ ] Serbest metin alanı
- [ ] **Cargo Quantity**
  - [ ] Sayısal giriş
  - [ ] Birim seçimi (MT, CBM, etc.)
- [ ] **Loading Rate**
  - [ ] Sayısal giriş
- [ ] **Discharge Rate**
  - [ ] Sayısal giriş

### 2.6 Commercial Information (Ticari Bilgiler)
- [ ] **Charterer**
  - [ ] Müşteri seçimi
  - [ ] Yeni müşteri ekleme
- [ ] **Freight Rate**
  - [ ] Para birimi seçimi
  - [ ] Sayısal giriş
- [ ] **Demurrage Rate**
  - [ ] Sayısal giriş
- [ ] **Commission %**
  - [ ] Yüzde hesaplama

### 2.7 Status ve Notes
- [ ] **Voyage Status**
  - [ ] Dropdown seçenekleri (Planned, In Progress, Completed, Cancelled)
  - [ ] Varsayılan değer kontrolü
- [ ] **Notes/Remarks**
  - [ ] Serbest metin alanı
  - [ ] Karakter limiti kontrolü

### 2.8 Form İşlemleri
- [ ] **Save** butonu
  - [ ] Zorunlu alan kontrolleri
  - [ ] Başarılı kayıt mesajı
  - [ ] Liste sayfasına yönlendirme
- [ ] **Save and Continue** butonu (varsa)
- [ ] **Cancel** butonu
  - [ ] İptal onayı
  - [ ] Liste sayfasına dönüş

## 3. Edit Voyage (Sefer Düzenleme) Testleri

### 3.1 Sayfa Erişimi
- [ ] Listeden Edit ikonuna tıklama
- [ ] Edit formunun açılması
- [ ] Mevcut verilerin yüklenmesi

### 3.2 Form Alanları
- [ ] Tüm alanların Create sayfası ile aynı olması
- [ ] Voyage Number değiştirilemez kontrolü (varsa)
- [ ] Vessel değiştirme kısıtlaması (varsa)
- [ ] Tarih alanlarının güncellenmesi

### 3.3 Güncelleme İşlemleri
- [ ] Veri değişikliği yapma
- [ ] Save butonu ile güncelleme
- [ ] Başarılı güncelleme mesajı
- [ ] Değişikliklerin yansıması

## 4. View Voyage (Sefer Detayları) Testleri

### 4.1 Sayfa Erişimi
- [ ] Listeden View ikonuna tıklama
- [ ] Detay sayfasının açılması
- [ ] Salt okunur mod kontrolü

### 4.2 Bilgi Görüntüleme
- [ ] Voyage Overview bölümü
- [ ] Route Details bölümü
- [ ] Schedule Information bölümü
- [ ] Cargo Details bölümü
- [ ] Commercial Details bölümü
- [ ] Document Attachments bölümü (varsa)

### 4.3 Ek Özellikler
- [ ] Print/Export butonu
- [ ] Edit butonuna geçiş
- [ ] Related Documents görüntüleme
- [ ] Voyage Timeline/History (varsa)

## 5. Voyage Operations (Sefer İşlemleri)

### 5.1 Status Updates
- [ ] Voyage başlatma (Start Voyage)
- [ ] Port arrival kayıtları
- [ ] Port departure kayıtları
- [ ] Voyage tamamlama (Complete Voyage)

### 5.2 Position Reporting
- [ ] Noon Report ekleme
- [ ] Position güncelleme
- [ ] Weather raporu
- [ ] Fuel consumption kayıtları

### 5.3 Port Operations
- [ ] Port activities ekleme
- [ ] Cargo operations kayıtları
- [ ] Berth bilgileri
- [ ] Agent bilgileri

## 6. Integrations ve Reports

### 6.1 Crew Integration
- [ ] Voyage'a atanmış crew görüntüleme
- [ ] Crew change planlaması

### 6.2 Document Integration
- [ ] Voyage dökümanları yükleme
- [ ] Charter party upload
- [ ] B/L (Bill of Lading) management

### 6.3 Reporting
- [ ] Voyage P&L raporu
- [ ] Performance raporu
- [ ] Fuel consumption analizi
- [ ] Port stay analizi

## 7. Validations ve Business Rules

### 7.1 Tarih Kontrolleri
- [ ] ETA > ETD kontrolü
- [ ] ATA > ATD kontrolü
- [ ] Port rotation mantık kontrolü

### 7.2 Vessel Availability
- [ ] Vessel'in başka voyage'da olmaması
- [ ] Dry dock çakışma kontrolü
- [ ] Off-hire period kontrolü

### 7.3 Data Integrity
- [ ] Duplicate voyage number engelleme
- [ ] Referans bütünlüğü kontrolleri
- [ ] Soft delete işlemleri

## 8. User Interface ve Experience

### 8.1 Responsive Design
- [ ] Mobile görünüm kontrolü
- [ ] Tablet görünüm kontrolü
- [ ] Desktop görünüm kontrolü

### 8.2 Usability
- [ ] Form navigation
- [ ] Tab order
- [ ] Keyboard shortcuts
- [ ] Help tooltips

### 8.3 Performance
- [ ] Sayfa yükleme süreleri
- [ ] Port arama performansı
- [ ] Large data set handling

## 9. Error Handling

### 9.1 Validation Errors
- [ ] Zorunlu alan hataları
- [ ] Format hataları
- [ ] Business rule hataları

### 9.2 System Errors
- [ ] Connection error handling
- [ ] Session timeout handling
- [ ] Concurrent update handling

## Test Sonuçları ve Notlar

### Bulunan Hatalar:
- **2025-01-06** - Create Voyage sayfasında DateTime UTC hatası: "Cannot write DateTime with Kind=Unspecified to PostgreSQL type 'timestamp with time zone'" - Tarih alanları UTC formatında kaydedilmiyor
  - ApplicationDbContext'te ConvertDateTimesToUtc metodu var ancak çalışmıyor
  - VoyagesController'da DateTime.SpecifyKind kullanıldı ancak hata devam ediyor
  - Npgsql.EnableLegacyTimestampBehavior false olarak ayarlandı ancak sorun çözülmedi
  - Model default değerleri kaldırıldı ancak sorun devam ediyor

### İyileştirme Önerileri:
- DateTime alanlarının PostgreSQL timestamp with time zone ile uyumlu hale getirilmesi için daha kapsamlı bir çözüm gerekiyor
- Entity Framework Core DateTime conversion configuration yapılması gerekebilir
- Tüm DateTime alanlarının DateTimeKind.Utc olarak işaretlenmesi için global bir çözüm uygulanmalı

### Test Tamamlanma Durumu:
- [x] List Sayfası Testleri (Temel testler tamamlandı)
- [ ] Create Sayfası Testleri (BLOCKER - UTC tarih hatası nedeniyle kayıt oluşturulamadı)
- [ ] Edit Sayfası Testleri (Kayıt olmadığı için test edilemedi)
- [ ] View Sayfası Testleri (Kayıt olmadığı için test edilemedi)
- [ ] Operations Testleri
- [ ] Integration Testleri

**Son Güncelleme:** 2025-01-06