using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("vessel_crew_assignments")]
public class VesselCrewAssignment
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("vessel_id")]
    public Guid VesselId { get; set; }
    
    [Column("user_id")]
    public Guid UserId { get; set; }
    
    [Column("rank_id")]
    public Guid? RankId { get; set; }
    
    [Column("department")]
    public string? Department { get; set; }
    
    [Column("sign_on_date")]
    public DateTime SignOnDate { get; set; }
    
    [Column("sign_on_port")]
    public string? SignOnPort { get; set; }
    
    [Column("expected_sign_off_date")]
    public DateTime? ExpectedSignOffDate { get; set; }
    
    [Column("actual_sign_off_date")]
    public DateTime? ActualSignOffDate { get; set; }
    
    [Column("sign_off_port")]
    public string? SignOffPort { get; set; }
    
    [Column("relief_due_date")]
    public DateTime? ReliefDueDate { get; set; }
    
    [Column("relief_user_id")]
    public Guid? ReliefUserId { get; set; }
    
    [Column("relief_confirmed")]
    public bool ReliefConfirmed { get; set; } = false;
    
    [Column("status")]
    public string Status { get; set; } = "Onboard";
    
    [Column("remarks")]
    public string? Remarks { get; set; }
    
    [Column("is_deleted")]
    public bool IsDeleted { get; set; } = false;
    
    // Computed property for backward compatibility
    public bool IsOnBoard => Status == "Onboard" && ActualSignOffDate == null;
    public bool IsActive => !IsDeleted;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    [Column("deleted_date")]
    public DateTime? DeletedDate { get; set; }
    
    [Column("deleted_by")]
    public Guid? DeletedBy { get; set; }
    
    // Navigation properties
    public virtual Vessel? Vessel { get; set; }
    public virtual User? User { get; set; }
    public virtual CrewRank? Rank { get; set; }
    public virtual User? ReliefUser { get; set; }
}