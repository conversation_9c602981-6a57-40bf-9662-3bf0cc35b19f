using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("flag_states")]
public class FlagState
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("country_code")]
    public string CountryCode { get; set; } = string.Empty;
    
    [Column("country_name")]
    public string CountryName { get; set; } = string.Empty;
    
    [Column("flag_code")]
    public string? FlagCode { get; set; }
    
    [Column("maritime_authority")]
    public string? MaritimeAuthority { get; set; }
    
    [Column("is_flag_of_convenience")]
    public bool IsFlagOfConvenience { get; set; } = false;
    
    [Column("is_paris_mou")]
    public bool IsParisMou { get; set; } = false;
    
    [Column("is_tokyo_mou")]
    public bool IsTokyoMou { get; set; } = false;
    
    [Column("sort_order")]
    public int SortOrder { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
}