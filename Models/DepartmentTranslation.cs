using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models
{
    [Table("department_translations")]
    public class DepartmentTranslation
    {
        [Key]
        [Column("department_translation_id")]
        public Guid DepartmentTranslationId { get; set; }

        [Column("department_id")]
        [Required]
        public Guid DepartmentId { get; set; }

        [Column("language_id")]
        [Required]
        public Guid LanguageId { get; set; }

        [Column("name")]
        [StringLength(150)]
        public string? Name { get; set; }

        // Navigation properties
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; } = null!;

        [ForeignKey("LanguageId")]
        public virtual Language Language { get; set; } = null!;
    }
}