using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SMS_Maritime_Web.Models;

[Table("vessels")]
public class Vessel
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("vessel_code")]
    [Required]
    [StringLength(50)]
    public string VesselCode { get; set; } = string.Empty;
    
    [Column("vessel_name")]
    [Required]
    [StringLength(200)]
    public string VesselName { get; set; } = string.Empty;
    
    [Column("former_names")]
    public string? FormerNames { get; set; }
    
    [Column("imo_number")]
    [Required]
    [StringLength(20)]
    public string ImoNumber { get; set; } = string.Empty;
    
    [Column("mmsi_number")]
    [StringLength(20)]
    public string? MmsiNumber { get; set; }
    
    [Column("call_sign")]
    [StringLength(20)]
    public string? CallSign { get; set; }
    
    [Column("vessel_type_id")]
    public Guid? VesselTypeId { get; set; }
    
    [Column("vessel_class_id")]
    public Guid? VesselClassId { get; set; }
    
    [Column("vessel_status_id")]
    public Guid? VesselStatusId { get; set; }
    
    [Column("flag_state_id")]
    public Guid? FlagStateId { get; set; }
    
    [Column("port_of_registry")]
    [StringLength(100)]
    public string? PortOfRegistry { get; set; }
    
    [Column("builder_name")]
    [StringLength(200)]
    public string? BuilderName { get; set; }
    
    [Column("builder_yard")]
    [StringLength(100)]
    public string? BuilderYard { get; set; }
    
    [Column("build_year")]
    public int? BuildYear { get; set; }
    
    [Column("delivery_date")]
    public DateOnly? DeliveryDate { get; set; }
    
    [Column("hull_number")]
    [StringLength(50)]
    public string? HullNumber { get; set; }
    
    // Dimensions
    [Column("length_overall")]
    public decimal? LengthOverall { get; set; }
    
    [Column("breadth_moulded")]
    public decimal? BreadthMoulded { get; set; }
    
    [Column("depth_moulded")]
    public decimal? DepthMoulded { get; set; }
    
    [Column("draft_summer")]
    public decimal? DraftSummer { get; set; }
    
    // Tonnage
    [Column("gross_tonnage")]
    public decimal? GrossTonnage { get; set; }
    
    [Column("net_tonnage")]
    public decimal? NetTonnage { get; set; }
    
    [Column("deadweight_summer")]
    public decimal? DeadweightSummer { get; set; }
    
    // Capacity
    [Column("cargo_capacity_grain")]
    public decimal? CargoCapacityGrain { get; set; }
    
    [Column("cargo_capacity_bale")]
    public decimal? CargoCapacityBale { get; set; }
    
    [Column("cargo_holds")]
    public int? CargoHolds { get; set; }
    
    [Column("cargo_hatches")]
    public int? CargoHatches { get; set; }
    
    [Column("teu_capacity")]
    public int? TeuCapacity { get; set; }
    
    [Column("passengers_capacity")]
    public int? PassengersCapacity { get; set; }
    
    // Engine
    [Column("main_engine_maker")]
    [StringLength(100)]
    public string? MainEngineMaker { get; set; }
    
    [Column("main_engine_model")]
    [StringLength(100)]
    public string? MainEngineModel { get; set; }
    
    [Column("main_engine_power_kw")]
    public decimal? MainEnginePowerKw { get; set; }
    
    [Column("speed_service")]
    public decimal? SpeedService { get; set; }
    
    [Column("speed_maximum")]
    public decimal? SpeedMaximum { get; set; }
    
    // Environmental
    [Column("eco_design")]
    public bool EcoDesign { get; set; } = false;
    
    [Column("scrubber_fitted")]
    public bool ScrubberFitted { get; set; } = false;
    
    [Column("ballast_water_treatment")]
    public bool BallastWaterTreatment { get; set; } = false;
    
    // Communication
    [Column("satellite_phone")]
    [StringLength(50)]
    public string? SatellitePhone { get; set; }
    
    [Column("satellite_email")]
    [StringLength(100)]
    public string? SatelliteEmail { get; set; }
    
    // Management
    [Column("technical_manager_company")]
    [StringLength(200)]
    public string? TechnicalManagerCompany { get; set; }
    
    [Column("commercial_manager_company")]
    [StringLength(200)]
    public string? CommercialManagerCompany { get; set; }
    
    [Column("crew_manager_company")]
    [StringLength(200)]
    public string? CrewManagerCompany { get; set; }
    
    // Trading
    [Column("trading_area")]
    [StringLength(200)]
    public string? TradingArea { get; set; }
    
    [Column("trade_type")]
    [StringLength(100)]
    public string? TradeType { get; set; }
    
    // Additional DB fields
    [Column("flag")]
    [StringLength(100)]
    public string? Flag { get; set; }
    
    [Column("vessel_type")]
    [StringLength(100)]
    public string? VesselType { get; set; }
    
    [Column("beam")]
    public decimal? Beam { get; set; }
    
    [Column("draft")]
    public decimal? Draft { get; set; }
    
    [Column("deadweight")]
    public decimal? Deadweight { get; set; }
    
    [Column("engine_type")]
    [StringLength(100)]
    public string? EngineType { get; set; }
    
    [Column("engine_power")]
    [Precision(10, 2)]
    public decimal? EnginePower { get; set; }
    
    [Column("class_society")]
    [StringLength(100)]
    public string? ClassSociety { get; set; }
    
    [Column("status")]
    [StringLength(50)]
    public string? Status { get; set; } = "Active";
    
    [Column("current_location")]
    [StringLength(256)]
    public string? CurrentLocation { get; set; }
    
    [Column("next_port")]
    [StringLength(256)]
    public string? NextPort { get; set; }
    
    [Column("eta")]
    public DateTime? Eta { get; set; }
    
    [Column("last_drydock_date")]
    public DateOnly? LastDrydockDate { get; set; }
    
    [Column("next_drydock_date")]
    public DateOnly? NextDrydockDate { get; set; }
    
    [Column("fleet_id")]
    public Guid? FleetId { get; set; }
    
    // Status
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("is_owned")]
    public bool IsOwned { get; set; } = true;
    
    [Column("is_in_fleet")]
    public bool IsInFleet { get; set; } = true;
    
    [Column("is_deleted")]
    public bool IsDeleted { get; set; } = false;
    
    // Audit
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    [Column("deleted_date")]
    public DateTime? DeletedDate { get; set; }
    
    [Column("deleted_by")]
    public Guid? DeletedBy { get; set; }
    
    // Navigation properties
    public virtual VesselType? VesselTypeNavigation { get; set; }
    public virtual VesselClass? VesselClass { get; set; }
    public virtual VesselStatus? VesselStatusNavigation { get; set; }
    public virtual VesselStatus? VesselStatus => VesselStatusNavigation; // Alias for compatibility
    public virtual FlagState? FlagState { get; set; }
    public virtual Fleet? Fleet { get; set; }
}