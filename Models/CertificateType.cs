using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("certificate_types")]
public class CertificateType
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("code")]
    public string Code { get; set; } = string.Empty;
    
    [Column("name")]
    public string Name { get; set; } = string.Empty;
    
    [Column("category")]
    public string? Category { get; set; }
    
    [Column("issuing_authority")]
    public string? IssuingAuthority { get; set; }
    
    [Column("validity_period_months")]
    public int? ValidityPeriodMonths { get; set; }
    
    [Column("is_mandatory")]
    public bool IsMandatory { get; set; } = true;
    
    [Column("requires_annual_endorsement")]
    public bool RequiresAnnualEndorsement { get; set; } = false;
    
    [Column("requires_intermediate_survey")]
    public bool RequiresIntermediateSurvey { get; set; } = false;
    
    [Column("alert_days_before_expiry")]
    public int AlertDaysBeforeExpiry { get; set; } = 90;
    
    [Column("description")]
    public string? Description { get; set; }
    
    [Column("sort_order")]
    public int SortOrder { get; set; } = 0;
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    // Navigation properties
    public virtual ICollection<VesselCertificate> VesselCertificates { get; set; } = new List<VesselCertificate>();
}