using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("menus")]
public class Menu
{
    [Column("id")]
    public int Id { get; set; }
    
    [Column("name")]
    public string Name { get; set; } = string.Empty;
    
    [Column("text_key")]
    public string TextKey { get; set; } = string.Empty;
    
    [Column("url")]
    public string Url { get; set; } = string.Empty;
    
    [Column("icon")]
    public string Icon { get; set; } = string.Empty;
    
    [Column("parent_id")]
    public int? ParentId { get; set; }
    
    [Column("display_order")]
    public int DisplayOrder { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    public virtual Menu? ParentMenu { get; set; }
    public virtual ICollection<Menu> SubMenus { get; set; } = new List<Menu>();
    public virtual ICollection<MenuRole> MenuRoles { get; set; } = new List<MenuRole>();
}