using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("department_types")]
public class DepartmentType
{
    [Column("department_type_id")]
    public Guid DepartmentTypeId { get; set; }
    
    [Column("name")]
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Column("description")]
    [StringLength(250)]
    public string? Description { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<Department> Departments { get; set; } = new List<Department>();
    public virtual ICollection<DepartmentTypeTranslation> Translations { get; set; } = new List<DepartmentTypeTranslation>();
}