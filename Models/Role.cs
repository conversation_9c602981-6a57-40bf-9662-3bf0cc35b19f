using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("roles")]
public class Role
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("name")]
    public string Name { get; set; } = string.Empty;
    
    [Column("normalized_name")]
    public string? NormalizedName { get; set; }
    
    [Column("description")]
    public string? Description { get; set; }
    
    [Column("role_type")]
    public string? RoleType { get; set; }
    
    [Column("role_category")]
    public string? RoleCategory { get; set; }
    
    [Column("permission_level")]
    public int PermissionLevel { get; set; } = 1;
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<MenuRole> MenuRoles { get; set; } = new List<MenuRole>();
}