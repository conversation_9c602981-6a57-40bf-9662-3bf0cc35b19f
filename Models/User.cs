using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("users")]
public class User
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("username")]
    public string Username { get; set; } = string.Empty;
    
    [Column("email")]
    public string Email { get; set; } = string.Empty;
    
    [Column("password_hash")]
    public string? PasswordHash { get; set; }
    
    [Column("first_name")]
    public string FirstName { get; set; } = string.Empty;
    
    [Column("last_name")]
    public string LastName { get; set; } = string.Empty;
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("email_confirmed")]
    public bool EmailConfirmed { get; set; } = false;
    
    [Column("phone_number")]
    public string? PhoneNumber { get; set; }
    
    [Column("profile_picture")]
    public string? ProfilePicture { get; set; }
    
    [Column("display_name")]
    public string? DisplayName { get; set; }
    
    [Column("preferred_language")]
    public string PreferredLanguage { get; set; } = "en";
    
    // Crew specific fields
    [Column("date_of_birth")]
    public DateTime? DateOfBirth { get; set; }
    
    [Column("nationality")]
    public string? Nationality { get; set; }
    
    [Column("place_of_birth")]
    public string? PlaceOfBirth { get; set; }
    
    [Column("department")]
    public string? Department { get; set; }
    
    [Column("department_id")]
    public Guid? DepartmentId { get; set; }
    
    [Column("employee_code")]
    public string? EmployeeCode { get; set; }
    
    [Column("hire_date")]
    public DateTime? HireDate { get; set; }
    
    // Security fields
    [Column("security_stamp")]
    public string? SecurityStamp { get; set; }
    
    [Column("lockout_enabled")]
    public bool LockoutEnabled { get; set; } = true;
    
    [Column("lockout_end")]
    public DateTimeOffset? LockoutEnd { get; set; }
    
    [Column("access_failed_count")]
    public int AccessFailedCount { get; set; } = 0;
    
    [Column("two_factor_enabled")]
    public bool TwoFactorEnabled { get; set; } = false;
    
    [Column("password_reset_code")]
    public string? PasswordResetCode { get; set; }
    
    [Column("must_change_password")]
    public bool MustChangePassword { get; set; } = false;
    
    [Column("last_password_change")]
    public DateTime? LastPasswordChange { get; set; }
    
    // Soft delete fields
    [Column("is_deleted")]
    public bool IsDeleted { get; set; } = false;
    
    [Column("deleted_date")]
    public DateTime? DeletedDate { get; set; }
    
    [Column("deleted_by")]
    public Guid? DeletedBy { get; set; }
    
    // Maritime documents
    [Column("passport_number")]
    public string? PassportNumber { get; set; }
    
    [Column("passport_expiry_date")]
    public DateTime? PassportExpiryDate { get; set; }
    
    [Column("seaman_book_number")]
    public string? SeamanBookNumber { get; set; }
    
    [Column("seaman_book_expiry_date")]
    public DateTime? SeamanBookExpiryDate { get; set; }
    
    // Navigation properties
    public virtual CrewDepartment? DepartmentEntity { get; set; }
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<VesselCrewAssignment> VesselCrewAssignments { get; set; } = new List<VesselCrewAssignment>();
}