using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("departments")]
public class Department
{
    [Column("department_id")]
    public Guid DepartmentId { get; set; }
    
    [Column("name")]
    [Required]
    [StringLength(150)]
    public string Name { get; set; } = string.Empty;
    
    [Column("department_type_id")]
    [Required]
    public Guid DepartmentTypeId { get; set; }
    
    [Column("is_critical")]
    public bool IsCritical { get; set; } = false;
    
    [Column("is_deleted")]
    public bool IsDeleted { get; set; } = false;
    
    [Column("creator_user_id")]
    [Required]
    public Guid CreatorUserId { get; set; }
    
    [Column("creation_time")]
    public DateTime CreationTime { get; set; } = DateTime.UtcNow;
    
    [Column("last_modifier_user_id")]
    public Guid? LastModifierUserId { get; set; }
    
    [Column("last_modification_time")]
    public DateTime? LastModificationTime { get; set; }
    
    [Column("deleter_user_id")]
    public Guid? DeleterUserId { get; set; }
    
    [Column("deletion_time")]
    public DateTime? DeletionTime { get; set; }
    
    // Navigation properties
    public virtual DepartmentType? DepartmentType { get; set; }
    public virtual User? CreatorUser { get; set; }
    public virtual User? LastModifierUser { get; set; }
    public virtual User? DeleterUser { get; set; }
    public virtual ICollection<DepartmentTranslation> Translations { get; set; } = new List<DepartmentTranslation>();
}