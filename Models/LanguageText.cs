using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("language_texts")]
public class LanguageText
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("language_id")]
    public Guid LanguageId { get; set; }
    
    [Column("text_key")]
    public string TextKey { get; set; } = string.Empty;
    
    [Column("text_value")]
    public string TextValue { get; set; } = string.Empty;
    
    [Column("module")]
    public string? Module { get; set; }
    
    [Column("is_html")]
    public bool IsHtml { get; set; } = false;
    
    [Column("is_approved")]
    public bool IsApproved { get; set; } = false;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    public virtual Language Language { get; set; } = null!;
}