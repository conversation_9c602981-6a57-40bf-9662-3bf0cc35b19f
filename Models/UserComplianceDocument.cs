using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("user_compliance_documents")]
public class UserComplianceDocument
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("user_id")]
    public Guid UserId { get; set; }
    
    [Column("document_type")]
    public string DocumentType { get; set; } = string.Empty;
    
    [Column("document_category")]
    public string? DocumentCategory { get; set; }
    
    [Column("document_number")]
    public string DocumentNumber { get; set; } = string.Empty;
    
    [Column("issuing_authority")]
    public string IssuingAuthority { get; set; } = string.Empty;
    
    [Column("issuing_country")]
    public string IssuingCountry { get; set; } = string.Empty;
    
    [Column("issuing_place")]
    public string? IssuingPlace { get; set; }
    
    [Column("issue_date")]
    public DateTime IssueDate { get; set; }
    
    [Column("expiry_date")]
    public DateTime? ExpiryDate { get; set; }
    
    [Column("is_mandatory")]
    public bool IsMandatory { get; set; } = true;
    
    [Column("flag_states")]
    public string[]? FlagStates { get; set; }
    
    [Column("vessel_types")]
    public string[]? VesselTypes { get; set; }
    
    [Column("is_original_seen")]
    public bool IsOriginalSeen { get; set; } = false;
    
    [Column("original_verified_by")]
    public Guid? OriginalVerifiedBy { get; set; }
    
    [Column("original_verified_date")]
    public DateTime? OriginalVerifiedDate { get; set; }
    
    [Column("requires_flag_endorsement")]
    public bool RequiresFlagEndorsement { get; set; } = false;
    
    [Column("flag_endorsement_number")]
    public string? FlagEndorsementNumber { get; set; }
    
    [Column("flag_endorsement_date")]
    public DateTime? FlagEndorsementDate { get; set; }
    
    [Column("flag_endorsement_expiry")]
    public DateTime? FlagEndorsementExpiry { get; set; }
    
    [Column("document_path")]
    public string? DocumentPath { get; set; }
    
    [Column("document_size_bytes")]
    public long? DocumentSizeBytes { get; set; }
    
    [Column("document_hash")]
    public string? DocumentHash { get; set; }
    
    [Column("uploaded_date")]
    public DateTime? UploadedDate { get; set; }
    
    [Column("uploaded_by")]
    public Guid? UploadedBy { get; set; }
    
    [Column("verification_status")]
    public string VerificationStatus { get; set; } = "Pending";
    
    [Column("verification_notes")]
    public string? VerificationNotes { get; set; }
    
    [Column("verified_by")]
    public Guid? VerifiedBy { get; set; }
    
    [Column("verified_date")]
    public DateTime? VerifiedDate { get; set; }
    
    [Column("rejection_reason")]
    public string? RejectionReason { get; set; }
    
    [Column("alert_days_before_expiry")]
    public int AlertDaysBeforeExpiry { get; set; } = 90;
    
    [Column("first_alert_sent")]
    public DateTime? FirstAlertSent { get; set; }
    
    [Column("last_alert_sent")]
    public DateTime? LastAlertSent { get; set; }
    
    [Column("alert_count")]
    public int AlertCount { get; set; } = 0;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    [Column("is_deleted")]
    public bool IsDeleted { get; set; } = false;
    
    [Column("deleted_date")]
    public DateTime? DeletedDate { get; set; }
    
    [Column("deleted_by")]
    public Guid? DeletedBy { get; set; }
    
    // Computed properties
    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value.Date < DateTime.UtcNow.Date;
    public bool IsExpiringSoon => ExpiryDate.HasValue && 
        ExpiryDate.Value.Date >= DateTime.UtcNow.Date && 
        ExpiryDate.Value.Date <= DateTime.UtcNow.Date.AddDays(AlertDaysBeforeExpiry);
    public int? DaysUntilExpiry => ExpiryDate.HasValue ? 
        (int)(ExpiryDate.Value.Date - DateTime.UtcNow.Date).TotalDays : null;
    
    // Navigation properties
    public virtual User? User { get; set; }
    public virtual User? OriginalVerifier { get; set; }
    public virtual User? Uploader { get; set; }
    public virtual User? Verifier { get; set; }
}