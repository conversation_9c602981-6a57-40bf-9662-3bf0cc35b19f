using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("vessel_certificates")]
public class VesselCertificate
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("vessel_id")]
    public Guid VesselId { get; set; }
    
    [Column("certificate_type_id")]
    public Guid CertificateTypeId { get; set; }
    
    [Column("certificate_number")]
    public string? CertificateNumber { get; set; }
    
    [Column("issue_date")]
    public DateTime IssueDate { get; set; }
    
    [Column("expiry_date")]
    public DateTime ExpiryDate { get; set; }
    
    [Column("last_endorsement_date")]
    public DateTime? LastEndorsementDate { get; set; }
    
    [Column("next_endorsement_date")]
    public DateTime? NextEndorsementDate { get; set; }
    
    [Column("last_intermediate_date")]
    public DateTime? LastIntermediateDate { get; set; }
    
    [Column("next_intermediate_date")]
    public DateTime? NextIntermediateDate { get; set; }
    
    [Column("issued_by")]
    public string? IssuedBy { get; set; }
    
    [Column("issued_at")]
    public string? IssuedAt { get; set; }
    
    [Column("issuing_authority")]
    public string? IssuingAuthority { get; set; }
    
    [Column("survey_type")]
    public string? SurveyType { get; set; }
    
    [Column("surveyor_name")]
    public string? SurveyorName { get; set; }
    
    [Column("survey_company")]
    public string? SurveyCompany { get; set; }
    
    [Column("document_path")]
    public string? DocumentPath { get; set; }
    
    [Column("document_size")]
    public long? DocumentSize { get; set; }
    
    [Column("document_hash")]
    public string? DocumentHash { get; set; }
    
    [Column("status")]
    public string Status { get; set; } = "Valid";
    
    [Column("is_original")]
    public bool IsOriginal { get; set; } = true;
    
    [Column("remarks")]
    public string? Remarks { get; set; }
    
    [Column("alert_sent")]
    public bool AlertSent { get; set; } = false;
    
    [Column("alert_sent_date")]
    public DateTime? AlertSentDate { get; set; }
    
    [Column("alert_acknowledged")]
    public bool AlertAcknowledged { get; set; } = false;
    
    [Column("alert_acknowledged_by")]
    public Guid? AlertAcknowledgedBy { get; set; }
    
    [Column("alert_acknowledged_date")]
    public DateTime? AlertAcknowledgedDate { get; set; }
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    [Column("deleted_date")]
    public DateTime? DeletedDate { get; set; }
    
    [Column("deleted_by")]
    public Guid? DeletedBy { get; set; }
    
    [Column("is_deleted")]
    public bool IsDeleted { get; set; } = false;
    
    // Computed properties
    public bool IsExpired => ExpiryDate.Date < DateTime.UtcNow.Date;
    public bool IsExpiringSoon => !IsExpired && ExpiryDate.Date <= DateTime.UtcNow.Date.AddDays(90);
    public int DaysUntilExpiry => (int)(ExpiryDate.Date - DateTime.UtcNow.Date).TotalDays;
    
    // Navigation properties
    public virtual Vessel? Vessel { get; set; }
    public virtual CertificateType? CertificateType { get; set; }
    public virtual User? AlertAcknowledger { get; set; }
}