using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("fleets")]
public class Fleet
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("fleet_code")]
    [Required]
    [StringLength(50)]
    public string FleetCode { get; set; } = string.Empty;
    
    [Column("fleet_name")]
    [Required]
    [StringLength(200)]
    public string FleetName { get; set; } = string.Empty;
    
    [Column("description")]
    public string? Description { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    // Navigation properties
    public virtual ICollection<Vessel> Vessels { get; set; } = new List<Vessel>();
}