using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("languages")]
public class Language
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("code")]
    public string Code { get; set; } = string.Empty;
    
    [Column("name")]
    public string Name { get; set; } = string.Empty;
    
    [Column("native_name")]
    public string? NativeName { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("is_default")]
    public bool IsDefault { get; set; } = false;
    
    [Column("is_rtl")]
    public bool IsRtl { get; set; } = false;
    
    [Column("sort_order")]
    public int SortOrder { get; set; } = 0;
    
    [Column("flag_code")]
    public string? FlagCode { get; set; }
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public virtual ICollection<LanguageText> LanguageTexts { get; set; } = new List<LanguageText>();
}