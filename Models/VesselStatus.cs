using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("vessel_statuses")]
public class VesselStatus
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("code")]
    public string Code { get; set; } = string.Empty;
    
    [Column("name")]
    public string Name { get; set; } = string.Empty;
    
    [Column("description")]
    public string? Description { get; set; }
    
    [Column("color")]
    public string? Color { get; set; }
    
    [Column("is_operational")]
    public bool IsOperational { get; set; } = true;
    
    [Column("sort_order")]
    public int SortOrder { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
}