using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("user_roles")]
public class UserRole
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("user_id")]
    public Guid UserId { get; set; }
    
    [Column("role_id")]
    public Guid RoleId { get; set; }
    
    [Column("company_id")]
    public Guid? CompanyId { get; set; }
    
    [Column("vessel_id")]
    public Guid? VesselId { get; set; }
    
    [Column("valid_from")]
    public DateTime ValidFrom { get; set; } = DateTime.UtcNow.Date;
    
    [Column("valid_until")]
    public DateTime? ValidUntil { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("assigned_date")]
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    
    [Column("assigned_by")]
    public Guid? AssignedBy { get; set; }
    
    public virtual User User { get; set; } = null!;
    public virtual Role Role { get; set; } = null!;
}