using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("vessel_classes")]
public class VesselClass
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("code")]
    public string Code { get; set; } = string.Empty;
    
    [Column("name")]
    public string Name { get; set; } = string.Empty;
    
    [Column("full_name")]
    public string? FullName { get; set; }
    
    [Column("country")]
    public string? Country { get; set; }
    
    [Column("website")]
    public string? Website { get; set; }
    
    [Column("is_iacs_member")]
    public bool IsIacsMember { get; set; } = false;
    
    [Column("sort_order")]
    public int SortOrder { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
}