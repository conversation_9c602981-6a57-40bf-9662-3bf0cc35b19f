using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("voyage_port_calls")]
public class VoyagePortCall
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("voyage_id")]
    [Required]
    public Guid VoyageId { get; set; }
    
    [Column("port_name")]
    [Required]
    [StringLength(200)]
    public string PortName { get; set; } = string.Empty;
    
    [Column("port_country")]
    [StringLength(100)]
    public string? PortCountry { get; set; }
    
    [Column("port_type")]
    [StringLength(50)]
    public string? PortType { get; set; } // Loading, Discharging, Bunkering, etc.
    
    [Column("eta")]
    public DateTime? Eta { get; set; } // Estimated Time of Arrival
    
    [Column("etb")]
    public DateTime? Etb { get; set; } // Estimated Time of Berthing
    
    [Column("etd")]
    public DateTime? Etd { get; set; } // Estimated Time of Departure
    
    [Column("ata")]
    public DateTime? Ata { get; set; } // Actual Time of Arrival
    
    [Column("atb")]
    public DateTime? Atb { get; set; } // Actual Time of Berthing
    
    [Column("atd")]
    public DateTime? Atd { get; set; } // Actual Time of Departure
    
    [Column("berth_name")]
    [StringLength(100)]
    public string? BerthName { get; set; }
    
    [Column("agent_name")]
    [StringLength(200)]
    public string? AgentName { get; set; }
    
    [Column("agent_contact")]
    [StringLength(500)]
    public string? AgentContact { get; set; }
    
    [Column("port_activities")]
    public string? PortActivities { get; set; }
    
    [Column("port_sequence")]
    public int PortSequence { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; }
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    // Navigation properties
    public virtual VesselVoyage? Voyage { get; set; }
    
    // Computed properties
    public string PortDescription => $"{PortName}{(string.IsNullOrEmpty(PortCountry) ? "" : $", {PortCountry}")}";
    public bool IsCompleted => Atd.HasValue;
    public bool IsInPort => Ata.HasValue && !Atd.HasValue;
}