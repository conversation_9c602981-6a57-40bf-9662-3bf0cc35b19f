using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models
{
    [Table("department_type_translations")]
    public class DepartmentTypeTranslation
    {
        [Key]
        [Column("department_type_translation_id")]
        public Guid DepartmentTypeTranslationId { get; set; }

        [Column("department_type_id")]
        [Required]
        public Guid DepartmentTypeId { get; set; }

        [Column("language_id")]
        [Required]
        public Guid LanguageId { get; set; }

        [Column("name")]
        [Required]
        [StringLength(150)]
        public string Name { get; set; } = string.Empty;

        [Column("description")]
        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation properties
        [ForeignKey("DepartmentTypeId")]
        public virtual DepartmentType DepartmentType { get; set; } = null!;

        [ForeignKey("LanguageId")]
        public virtual Language Language { get; set; } = null!;
    }
}