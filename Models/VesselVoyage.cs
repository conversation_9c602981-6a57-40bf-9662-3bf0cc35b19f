using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SMS_Maritime_Web.Models;

[Table("vessel_voyages")]
public class VesselVoyage
{
    [Column("id")]
    public Guid Id { get; set; }
    
    [Column("vessel_id")]
    [Required]
    public Guid VesselId { get; set; }
    
    [Column("voyage_number")]
    [Required]
    [StringLength(50)]
    public string VoyageNumber { get; set; } = string.Empty;
    
    [Column("voyage_type")]
    [StringLength(50)]
    public string? VoyageType { get; set; }
    
    [Column("departure_port")]
    [Required]
    [StringLength(200)]
    public string DeparturePort { get; set; } = string.Empty;
    
    [Column("departure_date")]
    [Required]
    public DateTime DepartureDate { get; set; }
    
    [Column("arrival_port")]
    [Required]
    [StringLength(200)]
    public string ArrivalPort { get; set; } = string.Empty;
    
    [Column("eta")]
    public DateTime? Eta { get; set; } // Estimated Time of Arrival
    
    [Column("ata")]
    public DateTime? Ata { get; set; } // Actual Time of Arrival
    
    [Column("distance_miles")]
    public decimal? DistanceMiles { get; set; }
    
    [Column("cargo_description")]
    public string? CargoDescription { get; set; }
    
    [Column("cargo_quantity")]
    public decimal? CargoQuantity { get; set; }
    
    [Column("cargo_unit")]
    [StringLength(50)]
    public string? CargoUnit { get; set; }
    
    [Column("charter_party_ref")]
    [StringLength(100)]
    public string? CharterPartyRef { get; set; }
    
    [Column("status")]
    [StringLength(50)]
    public string Status { get; set; } = "Planned";
    
    [Column("remarks")]
    public string? Remarks { get; set; }
    
    [Column("is_active")]
    public bool IsActive { get; set; } = true;
    
    [Column("created_date")]
    public DateTime CreatedDate { get; set; }
    
    [Column("created_by")]
    public Guid? CreatedBy { get; set; }
    
    [Column("modified_date")]
    public DateTime? ModifiedDate { get; set; }
    
    [Column("modified_by")]
    public Guid? ModifiedBy { get; set; }
    
    [Column("deleted_date")]
    public DateTime? DeletedDate { get; set; }
    
    [Column("deleted_by")]
    public Guid? DeletedBy { get; set; }
    
    [Column("is_deleted")]
    public bool IsDeleted { get; set; } = false;
    
    // Navigation properties
    public virtual Vessel? Vessel { get; set; }
    public virtual ICollection<VoyagePortCall> PortCalls { get; set; } = new List<VoyagePortCall>();
    
    // Computed properties
    public string VoyageDescription => $"{VoyageNumber}: {DeparturePort} - {ArrivalPort}";
    public int? VoyageDays => Ata.HasValue ? (int)(Ata.Value - DepartureDate).TotalDays : 
                              Eta.HasValue ? (int)(Eta.Value - DepartureDate).TotalDays : null;
}