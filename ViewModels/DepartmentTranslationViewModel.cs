using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class DepartmentTranslationViewModel
{
    public Guid DepartmentId { get; set; }
    public string OriginalName { get; set; } = string.Empty;
    public string DepartmentTypeName { get; set; } = string.Empty;
    
    public List<DepartmentTranslationItem> Translations { get; set; } = new List<DepartmentTranslationItem>();
}

public class DepartmentTranslationItem
{
    public Guid LanguageId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    
    // Translations are optional - make nullable
    [StringLength(150, ErrorMessage = "Name cannot exceed 150 characters")]
    public string? Name { get; set; }
}