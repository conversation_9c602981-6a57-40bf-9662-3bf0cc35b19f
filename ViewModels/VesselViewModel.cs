using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class VesselViewModel
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "Vessel code is required")]
    [StringLength(50)]
    [Display(Name = "Vessel Code")]
    public string VesselCode { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Vessel name is required")]
    [StringLength(200)]
    [Display(Name = "Vessel Name")]
    public string VesselName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "IMO number is required")]
    [StringLength(20)]
    [Display(Name = "IMO Number")]
    public string ImoNumber { get; set; } = string.Empty;
    
    [StringLength(20)]
    [Display(Name = "MMSI Number")]
    public string? MmsiNumber { get; set; }
    
    [StringLength(20)]
    [Display(Name = "Call Sign")]
    public string? CallSign { get; set; }
    
    [Display(Name = "Vessel Type")]
    public Guid? VesselTypeId { get; set; }
    
    [Display(Name = "Vessel Status")]
    public Guid? VesselStatusId { get; set; }
    
    [Display(Name = "Vessel Class")]
    public Guid? VesselClassId { get; set; }
    
    [Display(Name = "Flag State")]
    public Guid? FlagStateId { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Port of Registry")]
    public string? PortOfRegistry { get; set; }
    
    [Display(Name = "Build Year")]
    [Range(1900, 2100)]
    public int? BuildYear { get; set; }
    
    [Display(Name = "Delivery Date")]
    [DataType(DataType.Date)]
    public DateTime? DeliveryDate { get; set; }
    
    // Dimensions
    [Display(Name = "Length Overall (m)")]
    [Range(0, 500)]
    public decimal? LengthOverall { get; set; }
    
    [Display(Name = "Breadth (m)")]
    [Range(0, 100)]
    public decimal? BreadthMoulded { get; set; }
    
    [Display(Name = "Depth (m)")]
    [Range(0, 100)]
    public decimal? DepthMoulded { get; set; }
    
    [Display(Name = "Summer Draft (m)")]
    [Range(0, 50)]
    public decimal? DraftSummer { get; set; }
    
    // Tonnage
    [Display(Name = "Gross Tonnage")]
    [Range(0, 999999)]
    public decimal? GrossTonnage { get; set; }
    
    [Display(Name = "Net Tonnage")]
    [Range(0, 999999)]
    public decimal? NetTonnage { get; set; }
    
    [Display(Name = "Deadweight (t)")]
    [Range(0, 999999)]
    public decimal? DeadweightSummer { get; set; }
    
    // Speed
    [Display(Name = "Service Speed (kn)")]
    [Range(0, 50)]
    public decimal? SpeedService { get; set; }
    
    [Display(Name = "Maximum Speed (kn)")]
    [Range(0, 50)]
    public decimal? SpeedMaximum { get; set; }
    
    // Engine Information
    [StringLength(100)]
    [Display(Name = "Main Engine Maker")]
    public string? MainEngineMaker { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Main Engine Model")]
    public string? MainEngineModel { get; set; }
    
    [Display(Name = "Main Engine Power (kW)")]
    [Range(0, 999999)]
    public decimal? MainEnginePowerKw { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Engine Type")]
    public string? EngineType { get; set; }
    
    [Display(Name = "Engine Power")]
    [Range(0, 99999999.99)]
    public decimal? EnginePower { get; set; }
    
    // Additional Technical Info
    [StringLength(100)]
    [Display(Name = "Flag")]
    public string? Flag { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Vessel Type")]
    public string? VesselType { get; set; }
    
    [Display(Name = "Beam (m)")]
    [Range(0, 100)]
    public decimal? Beam { get; set; }
    
    [Display(Name = "Draft (m)")]
    [Range(0, 50)]
    public decimal? Draft { get; set; }
    
    [Display(Name = "Deadweight")]
    [Range(0, 999999)]
    public decimal? Deadweight { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Class Society")]
    public string? ClassSociety { get; set; }
    
    // Operational Information
    [StringLength(50)]
    [Display(Name = "Status")]
    public string? Status { get; set; }
    
    [StringLength(256)]
    [Display(Name = "Current Location")]
    public string? CurrentLocation { get; set; }
    
    [StringLength(256)]
    [Display(Name = "Next Port")]
    public string? NextPort { get; set; }
    
    [Display(Name = "ETA")]
    [DataType(DataType.DateTime)]
    public DateTime? Eta { get; set; }
    
    [Display(Name = "Last Drydock Date")]
    [DataType(DataType.Date)]
    public DateOnly? LastDrydockDate { get; set; }
    
    [Display(Name = "Next Drydock Date")]
    [DataType(DataType.Date)]
    public DateOnly? NextDrydockDate { get; set; }
    
    [Display(Name = "Fleet")]
    public Guid? FleetId { get; set; }
    
    // Environmental Compliance
    [Display(Name = "Eco Design")]
    public bool EcoDesign { get; set; } = false;
    
    [Display(Name = "Scrubber Fitted")]
    public bool ScrubberFitted { get; set; } = false;
    
    [Display(Name = "Ballast Water Treatment")]
    public bool BallastWaterTreatment { get; set; } = false;
    
    // Communication
    [StringLength(50)]
    [Display(Name = "Satellite Phone")]
    public string? SatellitePhone { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Satellite Email")]
    [EmailAddress]
    public string? SatelliteEmail { get; set; }
    
    // Management Companies
    [StringLength(200)]
    [Display(Name = "Technical Manager")]
    public string? TechnicalManagerCompany { get; set; }
    
    [StringLength(200)]
    [Display(Name = "Commercial Manager")]
    public string? CommercialManagerCompany { get; set; }
    
    [StringLength(200)]
    [Display(Name = "Crew Manager")]
    public string? CrewManagerCompany { get; set; }
    
    // Trading Information
    [StringLength(200)]
    [Display(Name = "Trading Area")]
    public string? TradingArea { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Trade Type")]
    public string? TradeType { get; set; }
    
    // Additional Information
    [Display(Name = "Former Names")]
    public string? FormerNames { get; set; }
    
    [StringLength(200)]
    [Display(Name = "Builder Name")]
    public string? BuilderName { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Builder Yard")]
    public string? BuilderYard { get; set; }
    
    [StringLength(50)]
    [Display(Name = "Hull Number")]
    public string? HullNumber { get; set; }
    
    // Capacity
    [Display(Name = "Cargo Capacity Grain (m³)")]
    [Range(0, 999999)]
    public decimal? CargoCapacityGrain { get; set; }
    
    [Display(Name = "Cargo Capacity Bale (m³)")]
    [Range(0, 999999)]
    public decimal? CargoCapacityBale { get; set; }
    
    [Display(Name = "Cargo Holds")]
    [Range(0, 50)]
    public int? CargoHolds { get; set; }
    
    [Display(Name = "Cargo Hatches")]
    [Range(0, 50)]
    public int? CargoHatches { get; set; }
    
    [Display(Name = "TEU Capacity")]
    [Range(0, 99999)]
    public int? TeuCapacity { get; set; }
    
    [Display(Name = "Passengers Capacity")]
    [Range(0, 9999)]
    public int? PassengersCapacity { get; set; }
    
    // Status Flags
    [Display(Name = "Is Active")]
    public bool IsActive { get; set; } = true;
    
    [Display(Name = "Is Owned")]
    public bool IsOwned { get; set; } = true;
    
    [Display(Name = "Is In Fleet")]
    public bool IsInFleet { get; set; } = true;
}