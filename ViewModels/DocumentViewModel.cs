using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class UserDocumentViewModel
{
    public Guid Id { get; set; }
    
    public Guid UserId { get; set; }
    
    [Required(ErrorMessage = "Document type is required")]
    [Display(Name = "Document Type")]
    public string DocumentType { get; set; } = string.Empty;
    
    [Display(Name = "Category")]
    public string? DocumentCategory { get; set; }
    
    [Required(ErrorMessage = "Document number is required")]
    [Display(Name = "Document Number")]
    public string DocumentNumber { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Issuing authority is required")]
    [Display(Name = "Issuing Authority")]
    public string IssuingAuthority { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Issuing country is required")]
    [Display(Name = "Issuing Country")]
    [StringLength(3)]
    public string IssuingCountry { get; set; } = string.Empty;
    
    [Display(Name = "Issuing Place")]
    public string? IssuingPlace { get; set; }
    
    [Required(ErrorMessage = "Issue date is required")]
    [Display(Name = "Issue Date")]
    [DataType(DataType.Date)]
    public DateTime IssueDate { get; set; } = DateTime.Today;
    
    [Display(Name = "Expiry Date")]
    [DataType(DataType.Date)]
    public DateTime? ExpiryDate { get; set; }
    
    [Display(Name = "Mandatory")]
    public bool IsMandatory { get; set; } = true;
    
    [Display(Name = "Original Seen")]
    public bool IsOriginalSeen { get; set; }
    
    [Display(Name = "Requires Flag Endorsement")]
    public bool RequiresFlagEndorsement { get; set; }
    
    [Display(Name = "Flag Endorsement Number")]
    public string? FlagEndorsementNumber { get; set; }
    
    [Display(Name = "Flag Endorsement Date")]
    [DataType(DataType.Date)]
    public DateTime? FlagEndorsementDate { get; set; }
    
    [Display(Name = "Flag Endorsement Expiry")]
    [DataType(DataType.Date)]
    public DateTime? FlagEndorsementExpiry { get; set; }
    
    [Display(Name = "Verification Status")]
    public string VerificationStatus { get; set; } = "Pending";
    
    [Display(Name = "Verification Notes")]
    [DataType(DataType.MultilineText)]
    public string? VerificationNotes { get; set; }
    
    [Display(Name = "Rejection Reason")]
    [DataType(DataType.MultilineText)]
    public string? RejectionReason { get; set; }
    
    // File upload
    [Display(Name = "Document File")]
    public IFormFile? DocumentFile { get; set; }
    
    // Display properties
    public string? UserName { get; set; }
    public string? UserFullName { get; set; }
    public string? DocumentPath { get; set; }
    public long? DocumentSizeBytes { get; set; }
    public DateTime? UploadedDate { get; set; }
    public string? UploadedByName { get; set; }
    public DateTime? VerifiedDate { get; set; }
    public string? VerifiedByName { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public int? DaysUntilExpiry { get; set; }
}

public class VesselDocumentViewModel
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "Vessel is required")]
    [Display(Name = "Vessel")]
    public Guid VesselId { get; set; }
    
    [Required(ErrorMessage = "Certificate type is required")]
    [Display(Name = "Certificate Type")]
    public Guid CertificateTypeId { get; set; }
    
    [Display(Name = "Certificate Number")]
    public string? CertificateNumber { get; set; }
    
    [Required(ErrorMessage = "Issue date is required")]
    [Display(Name = "Issue Date")]
    [DataType(DataType.Date)]
    public DateTime IssueDate { get; set; } = DateTime.Today;
    
    [Required(ErrorMessage = "Expiry date is required")]
    [Display(Name = "Expiry Date")]
    [DataType(DataType.Date)]
    public DateTime ExpiryDate { get; set; }
    
    [Display(Name = "Last Endorsement Date")]
    [DataType(DataType.Date)]
    public DateTime? LastEndorsementDate { get; set; }
    
    [Display(Name = "Next Endorsement Date")]
    [DataType(DataType.Date)]
    public DateTime? NextEndorsementDate { get; set; }
    
    [Display(Name = "Last Intermediate Date")]
    [DataType(DataType.Date)]
    public DateTime? LastIntermediateDate { get; set; }
    
    [Display(Name = "Next Intermediate Date")]
    [DataType(DataType.Date)]
    public DateTime? NextIntermediateDate { get; set; }
    
    [Display(Name = "Issued By")]
    public string? IssuedBy { get; set; }
    
    [Display(Name = "Issued At")]
    public string? IssuedAt { get; set; }
    
    [Display(Name = "Issuing Authority")]
    public string? IssuingAuthority { get; set; }
    
    [Display(Name = "Survey Type")]
    public string? SurveyType { get; set; }
    
    [Display(Name = "Surveyor Name")]
    public string? SurveyorName { get; set; }
    
    [Display(Name = "Survey Company")]
    public string? SurveyCompany { get; set; }
    
    [Display(Name = "Status")]
    public string Status { get; set; } = "Valid";
    
    [Display(Name = "Original Document")]
    public bool IsOriginal { get; set; } = true;
    
    [Display(Name = "Remarks")]
    [DataType(DataType.MultilineText)]
    public string? Remarks { get; set; }
    
    // File upload
    [Display(Name = "Certificate File")]
    public IFormFile? DocumentFile { get; set; }
    
    // Display properties
    public string? VesselName { get; set; }
    public string? CertificateTypeName { get; set; }
    public string? CertificateCategory { get; set; }
    public string? DocumentPath { get; set; }
    public long? DocumentSize { get; set; }
    public bool IsExpired { get; set; }
    public bool IsExpiringSoon { get; set; }
    public int DaysUntilExpiry { get; set; }
    public DateTime CreatedDate { get; set; }
}

public class DocumentFilterViewModel
{
    public string? SearchTerm { get; set; }
    public string? DocumentType { get; set; }
    public string? Status { get; set; }
    public bool? ShowExpired { get; set; }
    public bool? ShowExpiringSoon { get; set; }
    public Guid? UserId { get; set; }
    public Guid? VesselId { get; set; }
}