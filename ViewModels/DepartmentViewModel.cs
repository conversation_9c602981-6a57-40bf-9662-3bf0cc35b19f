using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class DepartmentViewModel
{
    public Guid DepartmentId { get; set; }
    
    [Required(ErrorMessage = "Name is required")]
    [StringLength(150, ErrorMessage = "Name cannot exceed 150 characters")]
    [Display(Name = "Name")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Department Type is required")]
    [Display(Name = "Department Type")]
    public Guid DepartmentTypeId { get; set; }
    
    [Display(Name = "Is Critical")]
    public bool IsCritical { get; set; } = false;
    
    [Display(Name = "Is Deleted")]
    public bool IsDeleted { get; set; } = false;
    
    public Guid CreatorUserId { get; set; }
    
    public DateTime CreationTime { get; set; }
    
    public Guid? LastModifierUserId { get; set; }
    
    public DateTime? LastModificationTime { get; set; }
    
    public Guid? DeleterUserId { get; set; }
    
    public DateTime? DeletionTime { get; set; }
    
    // For display purposes
    public string? DepartmentTypeName { get; set; }
    public string? CreatorUserName { get; set; }
    public string? LastModifierUserName { get; set; }
    public string? DeleterUserName { get; set; }
    public int EmployeeCount { get; set; }
}