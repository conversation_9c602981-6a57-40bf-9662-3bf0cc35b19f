using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class VoyageViewModel
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "Vessel is required")]
    [Display(Name = "Vessel")]
    public Guid VesselId { get; set; }
    
    [Required(ErrorMessage = "Voyage number is required")]
    [StringLength(50)]
    [Display(Name = "Voyage Number")]
    public string VoyageNumber { get; set; } = string.Empty;
    
    [Display(Name = "Voyage Type")]
    [StringLength(50)]
    public string? VoyageType { get; set; }
    
    [Required(ErrorMessage = "Departure port is required")]
    [StringLength(200)]
    [Display(Name = "Departure Port")]
    public string DeparturePort { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Departure date is required")]
    [Display(Name = "Departure Date")]
    [DataType(DataType.DateTime)]
    public DateTime DepartureDate { get; set; } = DateTime.UtcNow.Date;
    
    [Required(ErrorMessage = "Arrival port is required")]
    [StringLength(200)]
    [Display(Name = "Arrival Port")]
    public string ArrivalPort { get; set; } = string.Empty;
    
    [Display(Name = "ETA")]
    [DataType(DataType.DateTime)]
    public DateTime? Eta { get; set; }
    
    [Display(Name = "ATA")]
    [DataType(DataType.DateTime)]
    public DateTime? Ata { get; set; }
    
    [Display(Name = "Distance (Miles)")]
    [Range(0, 99999)]
    public decimal? DistanceMiles { get; set; }
    
    [Display(Name = "Cargo Description")]
    [DataType(DataType.MultilineText)]
    public string? CargoDescription { get; set; }
    
    [Display(Name = "Cargo Quantity")]
    [Range(0, 999999999)]
    public decimal? CargoQuantity { get; set; }
    
    [Display(Name = "Cargo Unit")]
    [StringLength(50)]
    public string? CargoUnit { get; set; }
    
    [Display(Name = "Charter Party Ref")]
    [StringLength(100)]
    public string? CharterPartyRef { get; set; }
    
    [Display(Name = "Status")]
    public string Status { get; set; } = "Planned";
    
    [Display(Name = "Remarks")]
    [DataType(DataType.MultilineText)]
    public string? Remarks { get; set; }
    
    // For display
    public string? VesselName { get; set; }
    public int PortCallCount { get; set; }
    public string? CurrentLocation { get; set; }
}