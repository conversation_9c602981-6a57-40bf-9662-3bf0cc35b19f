namespace SMS_Maritime_Web.ViewModels;

public class DashboardViewModel
{
    // Fleet Statistics
    public int TotalVessels { get; set; }
    public int ActiveVessels { get; set; }
    
    // Crew Statistics
    public int TotalCrew { get; set; }
    public int CrewOnboard { get; set; }
    
    // Voyage Statistics
    public int ActiveVoyages { get; set; }
    public int CompletedVoyagesThisMonth { get; set; }
    
    // Document Alerts
    public int ExpiredDocuments { get; set; }
    public int ExpiringDocuments { get; set; }
    
    // Recent Activities
    public List<VoyageViewModel> RecentVoyages { get; set; } = new List<VoyageViewModel>();
    
    // Charts Data
    public List<VesselStatusCount> VesselsByStatus { get; set; } = new List<VesselStatusCount>();
    public List<DepartmentCrewCount> CrewByDepartment { get; set; } = new List<DepartmentCrewCount>();
}

public class VesselStatusCount
{
    public string Status { get; set; } = string.Empty;
    public int Count { get; set; }
}

public class DepartmentCrewCount
{
    public Guid DepartmentId { get; set; }
    public string DepartmentName { get; set; } = string.Empty;
    public int Count { get; set; }
}