using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class UserViewModel
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "Username is required")]
    [StringLength(50, MinimumLength = 3)]
    [Display(Name = "Username")]
    public string Username { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email address")]
    [Display(Name = "Email")]
    public string Email { get; set; } = string.Empty;
    
    [StringLength(100, MinimumLength = 6)]
    [DataType(DataType.Password)]
    [Display(Name = "Password")]
    public string? Password { get; set; }
    
    [DataType(DataType.Password)]
    [Display(Name = "Confirm Password")]
    [Compare("Password", ErrorMessage = "Passwords do not match")]
    public string? ConfirmPassword { get; set; }
    
    [Required(ErrorMessage = "First name is required")]
    [StringLength(100)]
    [Display(Name = "First Name")]
    public string FirstName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Last name is required")]
    [StringLength(100)]
    [Display(Name = "Last Name")]
    public string LastName { get; set; } = string.Empty;
    
    [Display(Name = "Display Name")]
    [StringLength(200)]
    public string? DisplayName { get; set; }
    
    [Display(Name = "Phone Number")]
    [Phone(ErrorMessage = "Invalid phone number")]
    public string? PhoneNumber { get; set; }
    
    [Display(Name = "Active")]
    public bool IsActive { get; set; } = true;
    
    [Display(Name = "Email Confirmed")]
    public bool EmailConfirmed { get; set; }
    
    [Display(Name = "Preferred Language")]
    public string PreferredLanguage { get; set; } = "en";
    
    // Employee Information
    [Display(Name = "Employee Code")]
    [StringLength(50)]
    public string? EmployeeCode { get; set; }
    
    [Display(Name = "Department")]
    public Guid? DepartmentId { get; set; }
    
    [Display(Name = "Hire Date")]
    [DataType(DataType.Date)]
    public DateTime? HireDate { get; set; }
    
    [Display(Name = "Date of Birth")]
    [DataType(DataType.Date)]
    public DateTime? DateOfBirth { get; set; }
    
    [Display(Name = "Nationality")]
    [StringLength(100)]
    public string? Nationality { get; set; }
    
    [Display(Name = "Place of Birth")]
    [StringLength(200)]
    public string? PlaceOfBirth { get; set; }
    
    // Maritime Documents
    [Display(Name = "Passport Number")]
    [StringLength(50)]
    public string? PassportNumber { get; set; }
    
    [Display(Name = "Passport Expiry Date")]
    [DataType(DataType.Date)]
    public DateTime? PassportExpiryDate { get; set; }
    
    [Display(Name = "Seaman Book Number")]
    [StringLength(50)]
    public string? SeamanBookNumber { get; set; }
    
    [Display(Name = "Seaman Book Expiry Date")]
    [DataType(DataType.Date)]
    public DateTime? SeamanBookExpiryDate { get; set; }
    
    // Assigned Roles
    [Display(Name = "Roles")]
    public List<Guid> SelectedRoleIds { get; set; } = new List<Guid>();
    
    // For display
    public string? DepartmentName { get; set; }
    public List<string> RoleNames { get; set; } = new List<string>();
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
}

public class UserFilterViewModel
{
    public string? SearchTerm { get; set; }
    public Guid? DepartmentId { get; set; }
    public Guid? RoleId { get; set; }
    public bool? IsActive { get; set; }
    public bool? HasExpiredDocuments { get; set; }
}