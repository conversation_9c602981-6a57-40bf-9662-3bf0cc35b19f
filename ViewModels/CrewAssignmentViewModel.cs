using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class CrewAssignmentViewModel
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "Vessel is required")]
    [Display(Name = "Vessel")]
    public Guid VesselId { get; set; }
    
    [Required(ErrorMessage = "Crew member is required")]
    [Display(Name = "Crew Member")]
    public Guid UserId { get; set; }
    
    [Required(ErrorMessage = "Rank is required")]
    [Display(Name = "Rank")]
    public Guid RankId { get; set; }
    
    [Display(Name = "Department")]
    public string? Department { get; set; }
    
    [Display(Name = "Sign On Date")]
    [DataType(DataType.Date)]
    public DateTime? SignOnDate { get; set; }
    
    [Display(Name = "Sign On Port")]
    [StringLength(200)]
    public string? SignOnPort { get; set; }
    
    [Display(Name = "Sign Off Date")]
    [DataType(DataType.Date)]
    public DateTime? SignOffDate { get; set; }
    
    [Display(Name = "Sign Off Port")]
    [StringLength(200)]
    public string? SignOffPort { get; set; }
    
    [Display(Name = "Contract Duration (Months)")]
    [Range(1, 24)]
    public int? ContractDurationMonths { get; set; }
    
    [Display(Name = "Is On Board")]
    public bool IsOnBoard { get; set; }
    
    [Display(Name = "Status")]
    public string? Status { get; set; }
    
    [Display(Name = "Relief Due Date")]
    [DataType(DataType.Date)]
    public DateTime? ReliefDueDate { get; set; }
    
    [Display(Name = "Relief Planned Date")]
    [DataType(DataType.Date)]
    public DateTime? ReliefPlannedDate { get; set; }
    
    [Display(Name = "Relief Crew Member")]
    public Guid? ReliefUserId { get; set; }
    
    [Display(Name = "Notes")]
    [DataType(DataType.MultilineText)]
    public string? Notes { get; set; }
    
    // For display
    public string? VesselName { get; set; }
    public string? CrewName { get; set; }
    public string? RankName { get; set; }
    public string? ReliefCrewName { get; set; }
}