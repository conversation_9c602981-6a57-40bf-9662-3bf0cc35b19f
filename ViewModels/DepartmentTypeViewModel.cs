using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class DepartmentTypeViewModel
{
    public Guid DepartmentTypeId { get; set; }
    
    [Required(ErrorMessage = "Name is required")]
    [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
    [Display(Name = "Name")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(250, ErrorMessage = "Description cannot exceed 250 characters")]
    [Display(Name = "Description")]
    public string? Description { get; set; }
    
    [Display(Name = "Is Active")]
    public bool IsActive { get; set; } = true;
    
    // For display purposes
    public int DepartmentCount { get; set; }
}