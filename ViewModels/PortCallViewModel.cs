using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class PortCallViewModel
{
    public Guid Id { get; set; }
    
    public Guid VoyageId { get; set; }
    
    [Required(ErrorMessage = "Port name is required")]
    [StringLength(200)]
    [Display(Name = "Port Name")]
    public string PortName { get; set; } = string.Empty;
    
    [StringLength(100)]
    [Display(Name = "Country")]
    public string? PortCountry { get; set; }
    
    [Display(Name = "Port Type")]
    public string? PortType { get; set; }
    
    [Display(Name = "ETA")]
    [DataType(DataType.DateTime)]
    public DateTime? Eta { get; set; }
    
    [Display(Name = "ETB")]
    [DataType(DataType.DateTime)]
    public DateTime? Etb { get; set; }
    
    [Display(Name = "ETD")]
    [DataType(DataType.DateTime)]
    public DateTime? Etd { get; set; }
    
    [Display(Name = "ATA")]
    [DataType(DataType.DateTime)]
    public DateTime? Ata { get; set; }
    
    [Display(Name = "ATB")]
    [DataType(DataType.DateTime)]
    public DateTime? Atb { get; set; }
    
    [Display(Name = "ATD")]
    [DataType(DataType.DateTime)]
    public DateTime? Atd { get; set; }
    
    [StringLength(100)]
    [Display(Name = "Berth")]
    public string? BerthName { get; set; }
    
    [StringLength(200)]
    [Display(Name = "Agent Name")]
    public string? AgentName { get; set; }
    
    [StringLength(500)]
    [Display(Name = "Agent Contact")]
    [DataType(DataType.MultilineText)]
    public string? AgentContact { get; set; }
    
    [Display(Name = "Port Activities")]
    [DataType(DataType.MultilineText)]
    public string? PortActivities { get; set; }
    
    [Display(Name = "Sequence")]
    public int PortSequence { get; set; }
}