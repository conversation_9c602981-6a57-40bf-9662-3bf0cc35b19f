using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class DepartmentTypeTranslationViewModel
{
    public Guid DepartmentTypeId { get; set; }
    public string OriginalName { get; set; } = string.Empty;
    public string? OriginalDescription { get; set; }
    
    public List<TranslationItem> Translations { get; set; } = new List<TranslationItem>();
}

public class TranslationItem
{
    public Guid LanguageId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string LanguageName { get; set; } = string.Empty;
    
    // Translations are optional - remove Required attribute
    [StringLength(150, ErrorMessage = "Name cannot exceed 150 characters")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string? Description { get; set; }
}