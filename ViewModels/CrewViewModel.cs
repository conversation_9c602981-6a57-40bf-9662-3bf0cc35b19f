using System.ComponentModel.DataAnnotations;

namespace SMS_Maritime_Web.ViewModels;

public class CrewViewModel
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "Employee code is required")]
    [StringLength(50)]
    [Display(Name = "Employee Code")]
    public string EmployeeCode { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "First name is required")]
    [StringLength(100)]
    [Display(Name = "First Name")]
    public string FirstName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Last name is required")]
    [StringLength(100)]
    [Display(Name = "Last Name")]
    public string LastName { get; set; } = string.Empty;
    
    [Display(Name = "Display Name")]
    [StringLength(200)]
    public string? DisplayName { get; set; }
    
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress]
    [Display(Name = "Email")]
    public string Email { get; set; } = string.Empty;
    
    [Display(Name = "Phone Number")]
    [Phone]
    public string? PhoneNumber { get; set; }
    
    [Display(Name = "Date of Birth")]
    [DataType(DataType.Date)]
    public DateTime? DateOfBirth { get; set; }
    
    [Display(Name = "Nationality")]
    [StringLength(3)]
    public string? Nationality { get; set; }
    
    [Display(Name = "Place of Birth")]
    [StringLength(200)]
    public string? PlaceOfBirth { get; set; }
    
    [Display(Name = "Department")]
    public Guid? DepartmentId { get; set; }
    
    // For display purposes
    public string? DepartmentName { get; set; }
    
    [Display(Name = "Hire Date")]
    [DataType(DataType.Date)]
    public DateTime? HireDate { get; set; }
    
    // Maritime documents
    [Display(Name = "Passport Number")]
    [StringLength(100)]
    public string? PassportNumber { get; set; }
    
    [Display(Name = "Passport Expiry Date")]
    [DataType(DataType.Date)]
    public DateTime? PassportExpiryDate { get; set; }
    
    [Display(Name = "Seaman Book Number")]
    [StringLength(100)]
    public string? SeamanBookNumber { get; set; }
    
    [Display(Name = "Seaman Book Expiry Date")]
    [DataType(DataType.Date)]
    public DateTime? SeamanBookExpiryDate { get; set; }
    
    [Display(Name = "Is Active")]
    public bool IsActive { get; set; } = true;
    
    // For display purposes
    public string FullName => $"{FirstName} {LastName}";
    public string? CurrentVesselName { get; set; }
    public string? CurrentRankName { get; set; }
    public DateTime? CurrentSignOnDate { get; set; }
    public bool IsOnBoard { get; set; }
}