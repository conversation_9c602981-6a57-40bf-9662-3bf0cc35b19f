// Vessel form tab switching
$(document).ready(function() {
    // Tab switching implementation
    function switchTab(tabName) {
        console.log('Switching to tab:', tabName);
        
        // Hide all tab panes
        $('.tab-pane').each(function() {
            $(this).removeClass('show active');
            $(this).css('display', 'none');
        });
        
        // Show the selected tab
        $('#' + tabName).addClass('show active');
        $('#' + tabName).css('display', 'block');
        
        // Update nav links
        $('#vesselTabs .nav-link').each(function() {
            $(this).removeClass('active');
            $(this).attr('aria-selected', 'false');
        });
        
        $('#' + tabName + '-tab').addClass('active');
        $('#' + tabName + '-tab').attr('aria-selected', 'true');
    }
    
    // Handle tab clicks
    $('#vesselTabs .nav-link').on('click', function(e) {
        e.preventDefault();
        var href = $(this).attr('href');
        var tabName = href.substring(1); // Remove the #
        switchTab(tabName);
        window.location.hash = tabName;
    });
    
    // Initialize based on URL hash
    var hash = window.location.hash;
    if (hash) {
        var tabName = hash.substring(1);
        switchTab(tabName);
    } else {
        switchTab('basic');
    }
});