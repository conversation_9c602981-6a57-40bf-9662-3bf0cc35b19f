<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Maritime - Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #0066cc; color: white; padding: 20px; text-align: center; }
        .nav { background-color: #f4f4f4; padding: 10px; margin-bottom: 20px; }
        .nav a { margin-right: 20px; text-decoration: none; color: #0066cc; }
        .section { padding: 20px; border: 1px solid #ddd; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #0066cc; color: white; }
        input, button { padding: 8px; margin: 5px; }
        .status-active { color: green; }
        .status-inactive { color: red; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SMS Maritime Web Application</h1>
        <p>Ship Management System</p>
    </div>
    
    <div class="nav">
        <a href="#" onclick="showSection('home'); return false;">Home</a>
        <a href="#" onclick="showSection('ships'); return false;">Ships</a>
        <a href="#" onclick="showSection('login'); return false;">Login</a>
    </div>
    
    <div id="home" class="section">
        <h2>Welcome to SMS Maritime</h2>
        <p>Manage your fleet efficiently with our comprehensive ship management system.</p>
        <div>
            <h3>Quick Stats</h3>
            <ul>
                <li>Total Ships: 25</li>
                <li>Active Vessels: 20</li>
                <li>In Port: 5</li>
            </ul>
        </div>
    </div>
    
    <div id="ships" class="section" style="display: none;">
        <h2>Ships Management</h2>
        <div>
            <input type="text" id="searchInput" placeholder="Search ships...">
            <button onclick="performSearch()">Search</button>
            <button onclick="addShip()">Add New Ship</button>
        </div>
        <table id="shipsTable">
            <thead>
                <tr>
                    <th>IMO Number</th>
                    <th>Ship Name</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>9876543</td>
                    <td>Maritime Explorer</td>
                    <td>Cargo</td>
                    <td class="status-active">Active</td>
                    <td><a href="#" onclick="viewDetails('9876543'); return false;">View Details</a></td>
                </tr>
                <tr>
                    <td>9876544</td>
                    <td>Ocean Voyager</td>
                    <td>Tanker</td>
                    <td class="status-active">Active</td>
                    <td><a href="#" onclick="viewDetails('9876544'); return false;">View Details</a></td>
                </tr>
                <tr>
                    <td>9876545</td>
                    <td>Pacific Dream</td>
                    <td>Container</td>
                    <td class="status-inactive">In Port</td>
                    <td><a href="#" onclick="viewDetails('9876545'); return false;">View Details</a></td>
                </tr>
            </tbody>
        </table>
        <div id="searchResult" style="margin-top: 10px; color: blue;"></div>
    </div>
    
    <div id="login" class="section" style="display: none;">
        <h2>Login</h2>
        <form onsubmit="handleLogin(event)">
            <div>
                <label>Email:</label><br>
                <input type="email" id="email" required>
            </div>
            <div>
                <label>Password:</label><br>
                <input type="password" id="password" required>
            </div>
            <div>
                <button type="submit">Login</button>
            </div>
        </form>
        <div id="loginMessage" style="margin-top: 10px;"></div>
    </div>
    
    <div id="details" class="section" style="display: none;">
        <h2>Ship Details</h2>
        <div id="shipDetails"></div>
        <button onclick="showSection('ships')">Back to Ships</button>
    </div>
    
    <script>
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            document.getElementById(sectionId).style.display = 'block';
        }
        
        function performSearch() {
            const searchValue = document.getElementById('searchInput').value;
            document.getElementById('searchResult').textContent = `Searching for: "${searchValue}"...`;
            
            // Simulate search
            setTimeout(() => {
                const rows = document.querySelectorAll('#shipsTable tbody tr');
                let found = 0;
                rows.forEach(row => {
                    const shipName = row.cells[1].textContent.toLowerCase();
                    if (shipName.includes(searchValue.toLowerCase())) {
                        row.style.display = '';
                        found++;
                    } else {
                        row.style.display = 'none';
                    }
                });
                document.getElementById('searchResult').textContent = `Found ${found} ships matching "${searchValue}"`;
            }, 500);
        }
        
        function viewDetails(imo) {
            const ships = {
                '9876543': { name: 'Maritime Explorer', type: 'Cargo', status: 'Active', captain: 'John Smith' },
                '9876544': { name: 'Ocean Voyager', type: 'Tanker', status: 'Active', captain: 'Jane Doe' },
                '9876545': { name: 'Pacific Dream', type: 'Container', status: 'In Port', captain: 'Bob Johnson' }
            };
            
            const ship = ships[imo];
            if (ship) {
                document.getElementById('shipDetails').innerHTML = `
                    <p><strong>IMO:</strong> ${imo}</p>
                    <p><strong>Name:</strong> ${ship.name}</p>
                    <p><strong>Type:</strong> ${ship.type}</p>
                    <p><strong>Status:</strong> ${ship.status}</p>
                    <p><strong>Captain:</strong> ${ship.captain}</p>
                `;
                showSection('details');
            }
        }
        
        function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (email && password) {
                document.getElementById('loginMessage').innerHTML = 
                    '<span style="color: green;">Login successful! Welcome ' + email + '</span>';
            } else {
                document.getElementById('loginMessage').innerHTML = 
                    '<span style="color: red;">Please fill all fields</span>';
            }
        }
        
        function addShip() {
            const tbody = document.querySelector('#shipsTable tbody');
            const newRow = tbody.insertRow();
            newRow.innerHTML = `
                <td>9876546</td>
                <td>New Vessel</td>
                <td>Cargo</td>
                <td class="status-active">Active</td>
                <td><a href="#" onclick="viewDetails('9876546'); return false;">View Details</a></td>
            `;
        }
    </script>
</body>
</html>