.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.login-box {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo img {
    max-width: 200px;
    height: auto;
}

.language-selector a {
    color: #667eea;
    text-decoration: none;
}

.language-selector a:hover {
    text-decoration: underline;
}

.input-group-text {
    background-color: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    box-shadow: none;
    border-color: #ced4da;
}

.btn-primary {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-primary:hover {
    background-color: #5a67d8;
    border-color: #5a67d8;
}

.language-selector .dropdown-toggle {
    width: 100%;
    text-align: left;
}

.language-selector .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

.language-selector .dropdown-item {
    padding: 10px 15px;
}

.language-selector .dropdown-item.active {
    background-color: #667eea;
    color: white;
}

.language-selector .dropdown-item:hover {
    background-color: #f8f9fa;
}

.language-selector .dropdown-item.active:hover {
    background-color: #5a67d8;
}

.fi {
    font-size: 1.2em;
    vertical-align: middle;
}