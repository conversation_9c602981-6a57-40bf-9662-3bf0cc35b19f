html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

/* Dashboard Cards */
.border-left-primary {
  border-left: 4px solid #4e73df;
}

.border-left-success {
  border-left: 4px solid #1cc88a;
}

.border-left-info {
  border-left: 4px solid #36b9cc;
}

.border-left-warning {
  border-left: 4px solid #f6c23e;
}

.text-gray-300 {
  color: #dddfeb !important;
}

.text-gray-800 {
  color: #5a5c69 !important;
}

.text-xs {
  font-size: .7rem;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

/* Badge styles */
.badge-info {
  background-color: #17a2b8;
}

.badge-primary {
  background-color: #007bff;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-success {
  background-color: #28a745;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-secondary {
  background-color: #6c757d;
}

/* Progress bars */
.progress {
  height: 1rem;
  font-size: .75rem;
  background-color: #eaecf4;
  border-radius: .35rem;
}

.progress-bar {
  transition: width .6s ease;
}

/* Chart container */
#vesselStatusChart {
  max-height: 300px;
}