.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

.sidebar {
    min-width: 250px;
    max-width: 250px;
    background: #7386D5;
    color: #fff;
    transition: all 0.3s;
    min-height: 100vh;
}

.sidebar.active {
    margin-left: -250px;
}

.sidebar .sidebar-header {
    padding: 20px;
    background: #6d7fcc;
}

.sidebar ul.components {
    padding: 20px 0;
    border-bottom: 1px solid #47748b;
}

.sidebar ul p {
    color: #fff;
    padding: 10px;
}

.sidebar ul li a {
    padding: 10px 20px;
    font-size: 1.1em;
    display: block;
    color: #fff;
    text-decoration: none;
}

.sidebar ul li a:hover {
    color: #7386D5;
    background: #fff;
}

.sidebar ul li.active > a,
a[aria-expanded="true"] {
    color: #fff;
    background: #6d7fcc;
}

.sidebar a[data-bs-toggle="collapse"] {
    position: relative;
}

.sidebar .dropdown-toggle::after {
    display: block;
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
}

.sidebar ul ul a {
    font-size: 0.9em !important;
    padding-left: 30px !important;
    background: #6d7fcc;
}

.content {
    width: 100%;
    padding: 0;
    min-height: 100vh;
    transition: all 0.3s;
}

.content.active {
    margin-left: -250px;
}

#sidebarCollapse {
    background: #7386D5;
    color: #fff;
    border: none;
}

#sidebarCollapse:hover {
    background: #6d7fcc;
}

@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px;
    }
    .sidebar.active {
        margin-left: 0;
    }
    .content {
        margin-left: 0;
    }
    .content.active {
        margin-left: 250px;
    }
}

/* Language dropdown styles */
.dropdown-item .fi {
    font-size: 1.2em;
    vertical-align: middle;
}

.dropdown-item.active {
    background-color: #7386D5;
    color: white;
}

.dropdown-item.active:hover {
    background-color: #6d7fcc;
    color: white;
}