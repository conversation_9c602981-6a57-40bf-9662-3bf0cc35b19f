<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Maritime Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .nav {
            background-color: #f4f4f4;
            padding: 10px;
            margin-bottom: 20px;
        }
        .nav a {
            margin-right: 20px;
            text-decoration: none;
            color: #0066cc;
        }
        .ships-table {
            width: 100%;
            border-collapse: collapse;
        }
        .ships-table th, .ships-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .ships-table th {
            background-color: #0066cc;
            color: white;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .search-box input {
            padding: 8px;
            width: 300px;
        }
        .search-box button {
            padding: 8px 15px;
            background-color: #0066cc;
            color: white;
            border: none;
            cursor: pointer;
        }
        .login-form {
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
        }
        .login-form input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .login-form button {
            width: 100%;
            padding: 10px;
            background-color: #0066cc;
            color: white;
            border: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SMS Maritime Web - Test Page</h1>
        </div>
        
        <div class="nav">
            <a href="#home">Home</a>
            <a href="#ships">Ships</a>
            <a href="#login">Login</a>
        </div>
        
        <div id="home">
            <h2>Welcome to SMS Maritime</h2>
            <p>This is a test page for Playwright automation testing.</p>
        </div>
        
        <div id="ships" style="display: none;">
            <h2>Ships Management</h2>
            <div class="search-box">
                <input type="search" placeholder="Search ships..." id="searchInput">
                <button onclick="searchShips()">Search</button>
            </div>
            <table class="ships-table">
                <thead>
                    <tr>
                        <th>IMO Number</th>
                        <th>Ship Name</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>9876543</td>
                        <td>Maritime Explorer</td>
                        <td>Cargo</td>
                        <td>Active</td>
                        <td><a href="#details">Details</a></td>
                    </tr>
                    <tr>
                        <td>9876544</td>
                        <td>Ocean Voyager</td>
                        <td>Tanker</td>
                        <td>Active</td>
                        <td><a href="#details">Details</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="login" style="display: none;">
            <h2>Login</h2>
            <form class="login-form">
                <input type="email" name="Username" placeholder="Email" required>
                <input type="password" placeholder="Password" required>
                <button type="submit">Login</button>
            </form>
        </div>
    </div>
    
    <script>
        // Simple navigation
        document.querySelectorAll('.nav a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.target.getAttribute('href').substring(1);
                document.querySelectorAll('#home, #ships, #login').forEach(section => {
                    section.style.display = 'none';
                });
                const targetSection = document.getElementById(target);
                if (targetSection) {
                    targetSection.style.display = 'block';
                }
            });
        });
        
        function searchShips() {
            const searchValue = document.getElementById('searchInput').value;
            alert('Searching for: ' + searchValue);
        }
    </script>
</body>
</html>