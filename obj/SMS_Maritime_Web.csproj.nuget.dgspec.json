{"format": 1, "restore": {"/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj": {}}, "projects": {"/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj", "projectName": "SMS_Maritime_Web", "projectPath": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.11, )"}, "Microsoft.Playwright": {"target": "Package", "version": "[1.53.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.11, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.405/PortableRuntimeIdentifierGraph.json"}}}}}