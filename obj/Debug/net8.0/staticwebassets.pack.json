{"Files": [{"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/Debug/net8.0/scopedcss/projectbundle/SMS_Maritime_Web.bundle.scp.css", "PackagePath": "staticwebassets/SMS_Maritime_Web.bundle.scp.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/blank.html", "PackagePath": "staticwebassets/blank.html"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/css/layout.css", "PackagePath": "staticwebassets/css/layout.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/css/login.css", "PackagePath": "staticwebassets/css/login.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/css/site.css", "PackagePath": "staticwebassets/css/site.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/images/favicon.ico", "PackagePath": "staticwebassets/images/favicon.ico"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/images/sms-maritime-logo.png", "PackagePath": "staticwebassets/images/sms-maritime-logo.png"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/js/site.js", "PackagePath": "staticwebassets/js/site.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/js/vessel-tabs.js", "PackagePath": "staticwebassets/js/vessel-tabs.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/LICENSE", "PackagePath": "staticwebassets/lib/bootstrap"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.js.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.min.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/datatables/i18n/en.json", "PackagePath": "staticwebassets/lib/datatables/i18n/en.json"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/datatables/i18n/tr.json", "PackagePath": "staticwebassets/lib/datatables/i18n/tr.json"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "PackagePath": "staticwebassets/lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/LICENSE.md", "PackagePath": "staticwebassets/lib/jquery-validation/LICENSE.md"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/additional-methods.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/additional-methods.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/additional-methods.min.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/jquery.validate.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/jquery.validate.min.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/LICENSE.txt", "PackagePath": "staticwebassets/lib/jquery/LICENSE.txt"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/dist/jquery.js", "PackagePath": "staticwebassets/lib/jquery/dist/jquery.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/dist/jquery.min.js", "PackagePath": "staticwebassets/lib/jquery/dist/jquery.min.js"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/dist/jquery.min.map", "PackagePath": "staticwebassets/lib/jquery/dist/jquery.min.map"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/simple-test.html", "PackagePath": "staticwebassets/simple-test.html"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/test.html", "PackagePath": "staticwebassets/test.html"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/uploads/user-documents/2025-07/086cab0c-9149-4873-af42-d8535e2701bc_638872155732158110.png", "PackagePath": "staticwebassets/uploads/user-documents/2025-07/086cab0c-9149-4873-af42-d8535e2701bc_638872155732158110.png"}, {"Id": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/uploads/user-documents/2025-07/543b863a-fce8-4f5b-9933-18473169261d_638872142327903060.png", "PackagePath": "staticwebassets/uploads/user-documents/2025-07/543b863a-fce8-4f5b-9933-18473169261d_638872142327903060.png"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.SMS_Maritime_Web.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.build.SMS_Maritime_Web.props", "PackagePath": "build\\SMS_Maritime_Web.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.SMS_Maritime_Web.props", "PackagePath": "buildMultiTargeting\\SMS_Maritime_Web.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.SMS_Maritime_Web.props", "PackagePath": "buildTransitive\\SMS_Maritime_Web.props"}], "ElementsToRemove": []}