{"Version": 1, "Hash": "ybKNahexF4sAbB8xZuihxAW56UzwRe2Dq5Vwbo1Sl5k=", "Source": "SMS_Maritime_Web", "BasePath": "_content/SMS_Maritime_Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "SMS_Maritime_Web/wwwroot", "Source": "SMS_Maritime_Web", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "Pattern": "**"}], "Assets": [{"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/Debug/net8.0/scopedcss/bundle/SMS_Maritime_Web.styles.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/Debug/net8.0/scopedcss/bundle/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "SMS_Maritime_Web.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/Debug/net8.0/scopedcss/bundle/SMS_Maritime_Web.styles.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/Debug/net8.0/scopedcss/projectbundle/SMS_Maritime_Web.bundle.scp.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Computed", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/Debug/net8.0/scopedcss/projectbundle/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "SMS_Maritime_Web.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/obj/Debug/net8.0/scopedcss/projectbundle/SMS_Maritime_Web.bundle.scp.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/blank.html", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "blank.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/blank.html"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/css/layout.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "css/layout.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/layout.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/css/login.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "css/login.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/login.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/css/site.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/site.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/images/favicon.ico", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "images/favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/favicon.ico"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/images/sms-maritime-logo.png", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "images/sms-maritime-logo.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/images/sms-maritime-logo.png"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/js/site.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/site.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/js/vessel-tabs.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "js/vessel-tabs.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/vessel-tabs.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/bootstrap/LICENSE", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/LICENSE"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/datatables/i18n/en.json", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/datatables/i18n/en.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/datatables/i18n/en.json"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/datatables/i18n/tr.json", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/datatables/i18n/tr.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/datatables/i18n/tr.json"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/additional-methods.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.min.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.min.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery-validation/LICENSE.md", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/LICENSE.md"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/dist/jquery.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/dist/jquery.min.js", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.js"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/dist/jquery.min.map", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.map"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/lib/jquery/LICENSE.txt", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/LICENSE.txt"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/simple-test.html", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "simple-test.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/simple-test.html"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/test.html", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "test.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/test.html"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/uploads/user-documents/2025-07/086cab0c-9149-4873-af42-d8535e2701bc_638872155732158110.png", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "uploads/user-documents/2025-07/086cab0c-9149-4873-af42-d8535e2701bc_638872155732158110.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/uploads/user-documents/2025-07/086cab0c-9149-4873-af42-d8535e2701bc_638872155732158110.png"}, {"Identity": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/uploads/user-documents/2025-07/543b863a-fce8-4f5b-9933-18473169261d_638872142327903060.png", "SourceId": "SMS_Maritime_Web", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/wwwroot/", "BasePath": "_content/SMS_Maritime_Web", "RelativePath": "uploads/user-documents/2025-07/543b863a-fce8-4f5b-9933-18473169261d_638872142327903060.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/uploads/user-documents/2025-07/543b863a-fce8-4f5b-9933-18473169261d_638872142327903060.png"}]}