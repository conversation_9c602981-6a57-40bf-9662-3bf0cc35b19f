using BCrypt.Net;

string storedHash = "$2a$11$O3EhRS7/4vk7lGe85Ti48unWJ/BiWZiC1tkQxVWE4e.yW7OqFhDE2";
string password = "Admin@123";

bool isValid = BCrypt.Net.BCrypt.Verify(password, storedHash);
Console.WriteLine($"Password verification result: {isValid}");

// Let's also generate a new hash for Admin@123 to compare
string newHash = BCrypt.Net.BCrypt.HashPassword(password);
Console.WriteLine($"New hash for Admin@123: {newHash}");