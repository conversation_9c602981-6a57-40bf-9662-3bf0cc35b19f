using SMS_Maritime_Web.Models;
using SMS_Maritime_Web.Services;

namespace SMS_Maritime_Web.Data;

public static class DbInitializer
{
    public static async Task InitializeAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var authService = scope.ServiceProvider.GetRequiredService<IAuthenticationService>();

        // Create default admin user
        var adminUsername = "admin";
        var adminUser = await authService.GetUserByUsernameAsync(adminUsername);

        if (adminUser == null)
        {
            adminUser = new User
            {
                Username = adminUsername,
                Email = "<EMAIL>",
                FirstName = "System",
                LastName = "Administrator",
                IsActive = true
            };

            await authService.CreateUserAsync(adminUser, "Admin@123");
            
            // Get the created user to get the ID
            adminUser = await authService.GetUserByUsernameAsync(adminUsername);
            
            if (adminUser != null)
            {
                // Find Admin role
                var adminRole = context.Roles.FirstOrDefault(r => r.Name == "Admin");
                if (adminRole != null)
                {
                    // Add user to admin role
                    context.UserRoles.Add(new UserRole
                    {
                        Id = Guid.NewGuid(),
                        UserId = adminUser.Id,
                        RoleId = adminRole.Id,
                        ValidFrom = DateTime.UtcNow.Date,
                        IsActive = true,
                        AssignedDate = DateTime.UtcNow
                    });
                    await context.SaveChangesAsync();
                }
            }
        }
    }
}