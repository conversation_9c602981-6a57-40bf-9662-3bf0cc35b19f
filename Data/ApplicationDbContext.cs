using Microsoft.EntityFrameworkCore;
using SMS_Maritime_Web.Models;

namespace SMS_Maritime_Web.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Language> Languages { get; set; }
    public DbSet<LanguageText> LanguageTexts { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<Menu> Menus { get; set; }
    public DbSet<MenuRole> MenuRoles { get; set; }
    
    // Vessel Management
    public DbSet<Vessel> Vessels { get; set; }
    public DbSet<VesselType> VesselTypes { get; set; }
    public DbSet<VesselStatus> VesselStatuses { get; set; }
    public DbSet<VesselClass> VesselClasses { get; set; }
    public DbSet<FlagState> FlagStates { get; set; }
    public DbSet<Fleet> Fleets { get; set; }
    
    // Crew Management
    public DbSet<CrewRank> CrewRanks { get; set; }
    public DbSet<CrewDepartment> CrewDepartments { get; set; }
    public DbSet<VesselCrewAssignment> VesselCrewAssignments { get; set; }
    
    // Voyage Management
    public DbSet<VesselVoyage> VesselVoyages { get; set; }
    public DbSet<VoyagePortCall> VoyagePortCalls { get; set; }
    public DbSet<VoyageStatus> VoyageStatuses { get; set; }
    
    // Document Management
    public DbSet<UserComplianceDocument> UserComplianceDocuments { get; set; }
    public DbSet<VesselCertificate> VesselCertificates { get; set; }
    public DbSet<CertificateType> CertificateTypes { get; set; }
    
    // Department Management
    public DbSet<DepartmentType> DepartmentTypes { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<DepartmentTypeTranslation> DepartmentTypeTranslations { get; set; }
    public DbSet<DepartmentTranslation> DepartmentTranslations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // User configuration
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("gen_random_uuid()");
            
            // Department relationship
            entity.HasOne(u => u.DepartmentEntity)
                .WithMany()
                .HasForeignKey(u => u.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // Role configuration
        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("gen_random_uuid()");
        });

        // UserRole configuration
        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("gen_random_uuid()");
            entity.HasIndex(e => new { e.UserId, e.RoleId, e.CompanyId, e.VesselId }).IsUnique();
        });

        // Language configuration
        modelBuilder.Entity<Language>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("gen_random_uuid()");
            entity.HasIndex(e => e.Code).IsUnique();
        });

        // LanguageText configuration
        modelBuilder.Entity<LanguageText>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasDefaultValueSql("gen_random_uuid()");
            entity.HasIndex(e => new { e.LanguageId, e.TextKey }).IsUnique();
        });


        // MenuRole composite key
        modelBuilder.Entity<MenuRole>()
            .HasKey(mr => new { mr.MenuId, mr.RoleId });

        // Relationships
        modelBuilder.Entity<UserRole>()
            .HasOne(ur => ur.User)
            .WithMany(u => u.UserRoles)
            .HasForeignKey(ur => ur.UserId);

        modelBuilder.Entity<UserRole>()
            .HasOne(ur => ur.Role)
            .WithMany(r => r.UserRoles)
            .HasForeignKey(ur => ur.RoleId);

        modelBuilder.Entity<MenuRole>()
            .HasOne(mr => mr.Menu)
            .WithMany(m => m.MenuRoles)
            .HasForeignKey(mr => mr.MenuId);

        modelBuilder.Entity<MenuRole>()
            .HasOne(mr => mr.Role)
            .WithMany(r => r.MenuRoles)
            .HasForeignKey(mr => mr.RoleId);

        modelBuilder.Entity<Menu>()
            .HasOne(m => m.ParentMenu)
            .WithMany(m => m.SubMenus)
            .HasForeignKey(m => m.ParentId);

        // Crew Management relationships
        modelBuilder.Entity<VesselCrewAssignment>()
            .HasOne(vca => vca.User)
            .WithMany(u => u.VesselCrewAssignments)
            .HasForeignKey(vca => vca.UserId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<VesselCrewAssignment>()
            .HasOne(vca => vca.Vessel)
            .WithMany()
            .HasForeignKey(vca => vca.VesselId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<VesselCrewAssignment>()
            .HasOne(vca => vca.Rank)
            .WithMany()
            .HasForeignKey(vca => vca.RankId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<VesselCrewAssignment>()
            .HasOne(vca => vca.ReliefUser)
            .WithMany()
            .HasForeignKey(vca => vca.ReliefUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Voyage Management relationships
        modelBuilder.Entity<VesselVoyage>()
            .HasOne(vv => vv.Vessel)
            .WithMany()
            .HasForeignKey(vv => vv.VesselId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<VoyagePortCall>()
            .HasOne(vpc => vpc.Voyage)
            .WithMany(vv => vv.PortCalls)
            .HasForeignKey(vpc => vpc.VoyageId)
            .OnDelete(DeleteBehavior.Cascade);

        // Document Management relationships
        modelBuilder.Entity<UserComplianceDocument>()
            .HasOne(d => d.User)
            .WithMany()
            .HasForeignKey(d => d.UserId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<UserComplianceDocument>()
            .HasOne(d => d.OriginalVerifier)
            .WithMany()
            .HasForeignKey(d => d.OriginalVerifiedBy)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<UserComplianceDocument>()
            .HasOne(d => d.Uploader)
            .WithMany()
            .HasForeignKey(d => d.UploadedBy)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<UserComplianceDocument>()
            .HasOne(d => d.Verifier)
            .WithMany()
            .HasForeignKey(d => d.VerifiedBy)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<VesselCertificate>()
            .HasOne(c => c.Vessel)
            .WithMany()
            .HasForeignKey(c => c.VesselId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<VesselCertificate>()
            .HasOne(c => c.CertificateType)
            .WithMany(ct => ct.VesselCertificates)
            .HasForeignKey(c => c.CertificateTypeId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<VesselCertificate>()
            .HasOne(c => c.AlertAcknowledger)
            .WithMany()
            .HasForeignKey(c => c.AlertAcknowledgedBy)
            .OnDelete(DeleteBehavior.Restrict);

        // Department Type configuration
        modelBuilder.Entity<DepartmentType>(entity =>
        {
            entity.HasKey(e => e.DepartmentTypeId);
            entity.Property(e => e.DepartmentTypeId).HasDefaultValueSql("gen_random_uuid()");
            entity.HasIndex(e => e.Name).IsUnique();
        });

        // Department configuration
        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.DepartmentId);
            entity.Property(e => e.DepartmentId).HasDefaultValueSql("gen_random_uuid()");
            entity.HasIndex(e => e.Name).IsUnique();
            
            // Department Type relationship
            entity.HasOne(d => d.DepartmentType)
                .WithMany(dt => dt.Departments)
                .HasForeignKey(d => d.DepartmentTypeId)
                .OnDelete(DeleteBehavior.Restrict);
                
            // Creator User relationship
            entity.HasOne(d => d.CreatorUser)
                .WithMany()
                .HasForeignKey(d => d.CreatorUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            // Last Modifier User relationship
            entity.HasOne(d => d.LastModifierUser)
                .WithMany()
                .HasForeignKey(d => d.LastModifierUserId)
                .OnDelete(DeleteBehavior.Restrict);
                
            // Deleter User relationship
            entity.HasOne(d => d.DeleterUser)
                .WithMany()
                .HasForeignKey(d => d.DeleterUserId)
                .OnDelete(DeleteBehavior.Restrict);
        });

    }

}