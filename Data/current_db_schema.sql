--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.5 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA public;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: check_password_complexity(text); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.check_password_complexity(password_hash text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    complexity_score INTEGER := 0;
BEGIN
    -- This is a placeholder - actual implementation would check the password before hashing
    -- In practice, this check should be done in application layer before hashing
    complexity_score := 75; -- Default score
    RETURN complexity_score;
END;
$$;


ALTER FUNCTION public.check_password_complexity(password_hash text) OWNER TO dbadmin;

--
-- Name: cleanup_expired_sessions(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.cleanup_expired_sessions() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE user_sessions 
    SET is_active = false,
        logout_date = CURRENT_TIMESTAMP,
        logout_reason = 'SessionExpired'
    WHERE is_active = true 
      AND expire_date < CURRENT_TIMESTAMP;
END;
$$;


ALTER FUNCTION public.cleanup_expired_sessions() OWNER TO dbadmin;

--
-- Name: get_text(character varying, character varying); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.get_text(p_key character varying, p_language_code character varying DEFAULT 'en'::character varying) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_text TEXT;
BEGIN
    SELECT lt.text_value INTO v_text
    FROM language_texts lt
    INNER JOIN languages l ON lt.language_id = l.id
    WHERE lt.text_key = p_key 
      AND l.code = p_language_code
      AND l.is_active = true;
    
    -- If not found, try default language
    IF v_text IS NULL THEN
        SELECT lt.text_value INTO v_text
        FROM language_texts lt
        INNER JOIN languages l ON lt.language_id = l.id
        WHERE lt.text_key = p_key 
          AND l.is_default = true;
    END IF;
    
    -- If still not found, return the key
    IF v_text IS NULL THEN
        v_text := p_key;
    END IF;
    
    RETURN v_text;
END;
$$;


ALTER FUNCTION public.get_text(p_key character varying, p_language_code character varying) OWNER TO dbadmin;

--
-- Name: normalize_user_data(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.normalize_user_data() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.normalized_username := UPPER(TRIM(NEW.username));
    NEW.normalized_email := UPPER(TRIM(NEW.email));
    NEW.display_name := COALESCE(NEW.display_name, NEW.first_name || ' ' || NEW.last_name);
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.normalize_user_data() OWNER TO dbadmin;

--
-- Name: refresh_dashboard_materialized_views(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.refresh_dashboard_materialized_views() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_dashboard_kpi_hourly;
END;
$$;


ALTER FUNCTION public.refresh_dashboard_materialized_views() OWNER TO dbadmin;

--
-- Name: update_modified_date(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_modified_date() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.modified_date := CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_modified_date() OWNER TO dbadmin;

--
-- Name: update_user_theme_preferences_modified_date(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.update_user_theme_preferences_modified_date() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.modified_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_user_theme_preferences_modified_date() OWNER TO dbadmin;

--
-- Name: validate_vessel_assignment(); Type: FUNCTION; Schema: public; Owner: dbadmin
--

CREATE FUNCTION public.validate_vessel_assignment() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Check for overlapping assignments
    IF EXISTS (
        SELECT 1 FROM user_vessel_assignments
        WHERE user_id = NEW.user_id
          AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
          AND is_active = true
          AND (
              (NEW.embarkation_date BETWEEN embarkation_date AND COALESCE(actual_disembarkation_date, planned_disembarkation_date))
              OR (COALESCE(NEW.actual_disembarkation_date, NEW.planned_disembarkation_date) BETWEEN embarkation_date AND COALESCE(actual_disembarkation_date, planned_disembarkation_date))
          )
    ) THEN
        RAISE EXCEPTION 'User already has an active assignment during this period';
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.validate_vessel_assignment() OWNER TO dbadmin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: VersionInfo; Type: TABLE; Schema: public; Owner: sms_user
--

CREATE TABLE public."VersionInfo" (
    "Version" bigint NOT NULL,
    "AppliedOn" timestamp without time zone,
    "Description" character varying(1024)
);


ALTER TABLE public."VersionInfo" OWNER TO sms_user;

--
-- Name: certificate_types; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.certificate_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(200) NOT NULL,
    category character varying(100),
    issuing_authority character varying(200),
    validity_period_months integer,
    is_mandatory boolean DEFAULT true,
    requires_annual_endorsement boolean DEFAULT false,
    requires_intermediate_survey boolean DEFAULT false,
    alert_days_before_expiry integer DEFAULT 90,
    description text,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.certificate_types OWNER TO dbadmin;

--
-- Name: crew_departments; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.crew_departments (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.crew_departments OWNER TO dbadmin;

--
-- Name: crew_ranks; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.crew_ranks (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    department character varying(50) NOT NULL,
    level integer NOT NULL,
    is_officer boolean DEFAULT false,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.crew_ranks OWNER TO dbadmin;

--
-- Name: dashboard_access_logs; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dashboard_access_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    dashboard_id uuid,
    access_type character varying(50),
    access_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    ip_address character varying(45),
    user_agent text,
    load_time_ms integer,
    widget_count integer,
    data_size_kb integer,
    session_id character varying(100),
    duration_seconds integer
);


ALTER TABLE public.dashboard_access_logs OWNER TO dbadmin;

--
-- Name: dashboard_definitions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dashboard_definitions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dashboard_type_id uuid,
    code character varying(100) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    icon character varying(50),
    target_role character varying(100),
    min_access_level integer DEFAULT 1,
    default_layout character varying(50) DEFAULT 'grid'::character varying,
    grid_columns integer DEFAULT 12,
    grid_row_height integer DEFAULT 80,
    is_customizable boolean DEFAULT true,
    allow_export boolean DEFAULT true,
    auto_refresh boolean DEFAULT true,
    refresh_interval_seconds integer DEFAULT 300,
    is_active boolean DEFAULT true,
    is_default boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.dashboard_definitions OWNER TO dbadmin;

--
-- Name: dashboard_permissions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dashboard_permissions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dashboard_definition_id uuid,
    user_id uuid,
    role_name character varying(100),
    can_view boolean DEFAULT true,
    can_create boolean DEFAULT false,
    can_edit boolean DEFAULT false,
    can_delete boolean DEFAULT false,
    can_share boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    CONSTRAINT chk_permission_target CHECK ((((user_id IS NOT NULL) AND (role_name IS NULL)) OR ((user_id IS NULL) AND (role_name IS NOT NULL))))
);


ALTER TABLE public.dashboard_permissions OWNER TO dbadmin;

--
-- Name: dashboard_shares; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dashboard_shares (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_dashboard_id uuid,
    shared_with_user_id uuid,
    shared_with_role character varying(100),
    can_view boolean DEFAULT true,
    can_copy boolean DEFAULT true,
    can_edit boolean DEFAULT false,
    shared_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    shared_by uuid,
    expires_date timestamp without time zone,
    is_active boolean DEFAULT true,
    CONSTRAINT chk_share_target CHECK ((((shared_with_user_id IS NOT NULL) AND (shared_with_role IS NULL)) OR ((shared_with_user_id IS NULL) AND (shared_with_role IS NOT NULL))))
);


ALTER TABLE public.dashboard_shares OWNER TO dbadmin;

--
-- Name: dashboard_types; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dashboard_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    icon character varying(50),
    is_system boolean DEFAULT false,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.dashboard_types OWNER TO dbadmin;

--
-- Name: dashboard_widgets; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.dashboard_widgets (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    dashboard_definition_id uuid,
    widget_id uuid,
    grid_x integer DEFAULT 0,
    grid_y integer DEFAULT 0,
    grid_width integer DEFAULT 4,
    grid_height integer DEFAULT 3,
    title character varying(200),
    show_title boolean DEFAULT true,
    config jsonb,
    filters jsonb,
    is_visible boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.dashboard_widgets OWNER TO dbadmin;

--
-- Name: engine_types; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.engine_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    manufacturer character varying(100),
    fuel_type character varying(50),
    description text,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.engine_types OWNER TO dbadmin;

--
-- Name: flag_states; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.flag_states (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    country_code character varying(3) NOT NULL,
    country_name character varying(100) NOT NULL,
    flag_code character varying(10),
    maritime_authority character varying(200),
    is_flag_of_convenience boolean DEFAULT false,
    is_paris_mou boolean DEFAULT false,
    is_tokyo_mou boolean DEFAULT false,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.flag_states OWNER TO dbadmin;

--
-- Name: inspection_deficiencies; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.inspection_deficiencies (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    inspection_id uuid NOT NULL,
    deficiency_code character varying(50),
    deficiency_category character varying(100),
    deficiency_description text NOT NULL,
    convention_reference character varying(100),
    severity character varying(20),
    is_detainable boolean DEFAULT false,
    action_required text,
    action_taken text,
    resolved_date date,
    resolved_by character varying(200),
    verification_required boolean DEFAULT false,
    verified_date date,
    verified_by character varying(200),
    status character varying(50) DEFAULT 'Open'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.inspection_deficiencies OWNER TO dbadmin;

--
-- Name: inspection_types; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.inspection_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(200) NOT NULL,
    category character varying(100),
    frequency_months integer,
    estimated_duration_hours integer,
    description text,
    checklist_template jsonb,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.inspection_types OWNER TO dbadmin;

--
-- Name: kpi_alert_rules; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_alert_rules (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    kpi_definition_id uuid,
    alert_name character varying(200) NOT NULL,
    alert_description text,
    trigger_on character varying(50),
    threshold_type character varying(20),
    consecutive_periods integer DEFAULT 1,
    severity character varying(20),
    notification_channels text[],
    recipient_roles text[],
    recipient_users uuid[],
    max_alerts_per_day integer DEFAULT 3,
    silence_period_hours integer DEFAULT 24,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.kpi_alert_rules OWNER TO dbadmin;

--
-- Name: kpi_alerts; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_alerts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    kpi_definition_id uuid,
    kpi_value_id uuid,
    alert_rule_id uuid,
    alert_type character varying(50),
    severity character varying(20),
    title character varying(500),
    message text,
    vessel_id uuid,
    actual_value numeric(20,4),
    threshold_value numeric(20,4),
    variance_percentage numeric(10,4),
    status character varying(50) DEFAULT 'new'::character varying,
    acknowledged_by uuid,
    acknowledged_date timestamp without time zone,
    resolved_by uuid,
    resolved_date timestamp without time zone,
    resolution_notes text,
    notifications_sent jsonb,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.kpi_alerts OWNER TO dbadmin;

--
-- Name: kpi_calculation_logs; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_calculation_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    kpi_definition_id uuid,
    calculation_start timestamp without time zone NOT NULL,
    calculation_end timestamp without time zone,
    calculation_duration_ms integer,
    vessel_count integer,
    period_type character varying(50),
    period_start date,
    period_end date,
    records_processed integer,
    values_calculated integer,
    alerts_generated integer,
    status character varying(50),
    error_message text,
    memory_used_mb integer,
    cpu_time_ms integer
);


ALTER TABLE public.kpi_calculation_logs OWNER TO dbadmin;

--
-- Name: kpi_calculation_rules; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_calculation_rules (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    kpi_definition_id uuid,
    rule_order integer DEFAULT 0,
    rule_type character varying(50),
    rule_condition text,
    rule_action text,
    parameters jsonb,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.kpi_calculation_rules OWNER TO dbadmin;

--
-- Name: kpi_categories; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_categories (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    parent_category_id uuid,
    code character varying(50) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    icon character varying(50),
    color character varying(7),
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.kpi_categories OWNER TO dbadmin;

--
-- Name: kpi_definitions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_definitions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    category_id uuid,
    code character varying(100) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    unit_of_measure character varying(50),
    decimal_places integer DEFAULT 2,
    calculation_type character varying(50),
    calculation_sql text,
    calculation_formula text,
    data_source character varying(200),
    aggregation_type character varying(50),
    aggregation_period character varying(50),
    display_format character varying(50),
    trend_direction character varying(10),
    threshold_type character varying(50),
    good_threshold_min numeric(20,4),
    good_threshold_max numeric(20,4),
    warning_threshold_min numeric(20,4),
    warning_threshold_max numeric(20,4),
    critical_threshold_min numeric(20,4),
    critical_threshold_max numeric(20,4),
    has_target boolean DEFAULT false,
    target_type character varying(50),
    is_accumulative boolean DEFAULT false,
    requires_vessel_filter boolean DEFAULT true,
    requires_date_filter boolean DEFAULT true,
    is_active boolean DEFAULT true,
    is_featured boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.kpi_definitions OWNER TO dbadmin;

--
-- Name: kpi_targets; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_targets (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    kpi_definition_id uuid,
    vessel_id uuid,
    department character varying(100),
    year integer NOT NULL,
    month integer,
    quarter integer,
    target_value numeric(20,4) NOT NULL,
    stretch_target_value numeric(20,4),
    minimum_acceptable_value numeric(20,4),
    is_approved boolean DEFAULT false,
    approved_by uuid,
    approved_date timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.kpi_targets OWNER TO dbadmin;

--
-- Name: kpi_values; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_values (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    kpi_definition_id uuid,
    vessel_id uuid,
    department character varying(100),
    period_type character varying(50),
    period_start date NOT NULL,
    period_end date NOT NULL,
    actual_value numeric(20,4),
    target_value numeric(20,4),
    previous_value numeric(20,4),
    variance_value numeric(20,4),
    variance_percentage numeric(10,4),
    trend character varying(10),
    trend_percentage numeric(10,4),
    status character varying(20),
    status_reason text,
    calculation_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_quality_score numeric(5,2),
    is_estimated boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.kpi_values OWNER TO dbadmin;

--
-- Name: kpi_widgets; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.kpi_widgets (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    widget_id uuid,
    kpi_definition_ids uuid[],
    display_type character varying(50),
    comparison_type character varying(50),
    chart_type character varying(50),
    show_trend boolean DEFAULT true,
    show_target boolean DEFAULT true,
    show_forecast boolean DEFAULT false,
    default_period character varying(50),
    allow_period_selection boolean DEFAULT true,
    default_vessel_filter character varying(50),
    vessel_ids uuid[],
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.kpi_widgets OWNER TO dbadmin;

--
-- Name: language_texts; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.language_texts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    language_id uuid NOT NULL,
    text_key character varying(500) NOT NULL,
    text_value text NOT NULL,
    module character varying(100),
    context character varying(500),
    is_html boolean DEFAULT false,
    version integer DEFAULT 1,
    is_approved boolean DEFAULT false,
    approved_by uuid,
    approved_date timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.language_texts OWNER TO dbadmin;

--
-- Name: languages; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.languages (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(10) NOT NULL,
    name character varying(100) NOT NULL,
    native_name character varying(100),
    flag_code character varying(5),
    is_active boolean DEFAULT true,
    is_default boolean DEFAULT false,
    is_rtl boolean DEFAULT false,
    sort_order integer DEFAULT 0,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.languages OWNER TO dbadmin;

--
-- Name: maintenance_categories; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.maintenance_categories (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(200) NOT NULL,
    parent_category_id uuid,
    description text,
    icon character varying(50),
    color character varying(7),
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.maintenance_categories OWNER TO dbadmin;

--
-- Name: maintenance_jobs; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.maintenance_jobs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    equipment_id uuid,
    job_code character varying(100) NOT NULL,
    job_title character varying(500) NOT NULL,
    job_description text,
    category_id uuid,
    frequency_hours integer,
    frequency_days integer,
    frequency_months integer,
    last_done_date date,
    last_done_hours numeric(10,2),
    next_due_date date,
    next_due_hours numeric(10,2),
    estimated_duration_hours numeric(5,2),
    required_personnel integer,
    required_skills text,
    spare_parts_required jsonb,
    tools_required jsonb,
    external_service_required boolean DEFAULT false,
    safety_precautions text,
    permits_required jsonb,
    priority character varying(20) DEFAULT 'Normal'::character varying,
    is_class_requirement boolean DEFAULT false,
    is_statutory_requirement boolean DEFAULT false,
    status character varying(50) DEFAULT 'Active'::character varying,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.maintenance_jobs OWNER TO dbadmin;

--
-- Name: menu_roles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.menu_roles (
    menu_id integer NOT NULL,
    role_id uuid NOT NULL
);


ALTER TABLE public.menu_roles OWNER TO dbadmin;

--
-- Name: menus; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.menus (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    text_key character varying(255) NOT NULL,
    url character varying(500) DEFAULT ''::character varying NOT NULL,
    icon character varying(100) DEFAULT ''::character varying NOT NULL,
    parent_id integer,
    display_order integer DEFAULT 0 NOT NULL,
    is_active boolean DEFAULT true NOT NULL
);


ALTER TABLE public.menus OWNER TO dbadmin;

--
-- Name: menus_id_seq; Type: SEQUENCE; Schema: public; Owner: dbadmin
--

CREATE SEQUENCE public.menus_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.menus_id_seq OWNER TO dbadmin;

--
-- Name: menus_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dbadmin
--

ALTER SEQUENCE public.menus_id_seq OWNED BY public.menus.id;


--
-- Name: vessel_inspections; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_inspections (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    inspection_type_id uuid NOT NULL,
    inspection_number character varying(100),
    inspection_date date NOT NULL,
    inspection_port character varying(100),
    inspection_country character varying(100),
    inspector_name character varying(200),
    inspector_organization character varying(200),
    result character varying(50),
    deficiencies_count integer DEFAULT 0,
    observations_count integer DEFAULT 0,
    detention_days integer DEFAULT 0,
    followup_required boolean DEFAULT false,
    followup_deadline date,
    followup_completed boolean DEFAULT false,
    followup_date date,
    report_path character varying(500),
    deficiency_list jsonb,
    status character varying(50) DEFAULT 'Completed'::character varying,
    remarks text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.vessel_inspections OWNER TO dbadmin;

--
-- Name: vessel_performance_daily; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_performance_daily (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    report_date date NOT NULL,
    voyage_number character varying(50),
    position_latitude numeric(10,6),
    position_longitude numeric(11,6),
    position_timestamp timestamp without time zone,
    departure_port character varying(100),
    departure_date timestamp without time zone,
    arrival_port character varying(100),
    arrival_date timestamp without time zone,
    next_port character varying(100),
    eta_next_port timestamp without time zone,
    distance_to_go numeric(10,2),
    distance_covered_24h numeric(10,2),
    average_speed numeric(5,2),
    sailing_hours numeric(5,2),
    port_hours numeric(5,2),
    anchor_hours numeric(5,2),
    drifting_hours numeric(5,2),
    hfo_consumption numeric(10,3),
    mdo_consumption numeric(10,3),
    mgo_consumption numeric(10,3),
    lng_consumption numeric(10,3),
    hfo_rob numeric(10,3),
    mdo_rob numeric(10,3),
    mgo_rob numeric(10,3),
    lng_rob numeric(10,3),
    fresh_water_produced numeric(10,3),
    fresh_water_consumed numeric(10,3),
    fresh_water_rob numeric(10,3),
    wind_force integer,
    wind_direction integer,
    sea_state integer,
    swell_height numeric(5,2),
    visibility character varying(50),
    main_engine_hours numeric(10,2),
    main_engine_rpm_avg integer,
    main_engine_load_pct numeric(5,2),
    cargo_loaded numeric(12,2),
    cargo_discharged numeric(12,2),
    cargo_onboard numeric(12,2),
    master_remarks text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.vessel_performance_daily OWNER TO dbadmin;

--
-- Name: vessels; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessels (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_code character varying(50) NOT NULL,
    vessel_name character varying(200) NOT NULL,
    former_names text,
    imo_number character varying(20) NOT NULL,
    mmsi_number character varying(20),
    call_sign character varying(20),
    vessel_type_id uuid,
    vessel_class_id uuid,
    vessel_status_id uuid,
    flag_state_id uuid,
    port_of_registry character varying(100),
    builder_name character varying(200),
    builder_yard character varying(100),
    build_year integer,
    delivery_date date,
    hull_number character varying(50),
    length_overall numeric(10,2),
    length_between_perpendiculars numeric(10,2),
    breadth_moulded numeric(10,2),
    depth_moulded numeric(10,2),
    draft_summer numeric(10,2),
    draft_winter numeric(10,2),
    gross_tonnage numeric(12,2),
    net_tonnage numeric(12,2),
    deadweight_summer numeric(12,2),
    deadweight_winter numeric(12,2),
    displacement numeric(12,2),
    lightweight numeric(12,2),
    cargo_capacity_grain numeric(12,2),
    cargo_capacity_bale numeric(12,2),
    cargo_holds integer,
    cargo_hatches integer,
    teu_capacity integer,
    passengers_capacity integer,
    cars_capacity integer,
    main_engine_id uuid,
    main_engine_maker character varying(100),
    main_engine_model character varying(100),
    main_engine_power_kw numeric(10,2),
    main_engine_rpm integer,
    auxiliary_engines integer,
    auxiliary_engine_power_kw numeric(10,2),
    speed_service numeric(5,2),
    speed_maximum numeric(5,2),
    fuel_consumption_sailing numeric(10,2),
    fuel_consumption_port numeric(10,2),
    fuel_consumption_anchor numeric(10,2),
    eco_design boolean DEFAULT false,
    scrubber_fitted boolean DEFAULT false,
    ballast_water_treatment boolean DEFAULT false,
    imo_tier integer,
    eedi_value numeric(10,4),
    eexi_value numeric(10,4),
    cii_rating character varying(1),
    satellite_phone character varying(50),
    satellite_fax character varying(50),
    satellite_email character varying(100),
    vsat_phone character varying(50),
    inmarsat_id character varying(50),
    hull_insurance_value numeric(15,2),
    hull_insurance_currency character varying(3),
    pni_club character varying(200),
    technical_manager_company character varying(200),
    commercial_manager_company character varying(200),
    crew_manager_company character varying(200),
    trading_area character varying(200),
    trade_type character varying(100),
    charter_type character varying(50),
    daily_hire_rate numeric(12,2),
    is_active boolean DEFAULT true,
    is_owned boolean DEFAULT true,
    is_in_fleet boolean DEFAULT true,
    technical_data jsonb,
    commercial_data jsonb,
    custom_fields jsonb,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.vessels OWNER TO dbadmin;

--
-- Name: mv_dashboard_kpi_hourly; Type: MATERIALIZED VIEW; Schema: public; Owner: dbadmin
--

CREATE MATERIALIZED VIEW public.mv_dashboard_kpi_hourly AS
 SELECT v.id AS vessel_id,
    v.vessel_name,
    date_trunc('hour'::text, CURRENT_TIMESTAMP) AS calculation_time,
        CASE
            WHEN ((vpd.sailing_hours + vpd.port_hours) > (0)::numeric) THEN ((vpd.sailing_hours / (vpd.sailing_hours + vpd.port_hours)) * (100)::numeric)
            ELSE (0)::numeric
        END AS utilization_rate,
    ( SELECT count(*) AS count
           FROM public.maintenance_jobs mj
          WHERE ((mj.vessel_id = v.id) AND (mj.is_active = true) AND (mj.next_due_date >= CURRENT_DATE))) AS pms_compliance_count,
    ( SELECT count(*) AS count
           FROM public.vessel_inspections vi
          WHERE ((vi.vessel_id = v.id) AND (vi.inspection_date >= (CURRENT_DATE - '365 days'::interval)) AND ((vi.result)::text <> 'Detained'::text))) AS inspection_performance,
    ((15000)::double precision + (random() * (5000)::double precision)) AS daily_opex,
    v.daily_hire_rate AS daily_revenue
   FROM (public.vessels v
     LEFT JOIN LATERAL ( SELECT vpd2.vessel_id,
            sum(vpd2.sailing_hours) AS sailing_hours,
            sum(vpd2.port_hours) AS port_hours
           FROM public.vessel_performance_daily vpd2
          WHERE ((vpd2.vessel_id = v.id) AND (vpd2.report_date >= (CURRENT_DATE - '30 days'::interval)))
          GROUP BY vpd2.vessel_id) vpd ON (true))
  WHERE (v.is_deleted = false)
  WITH NO DATA;


ALTER MATERIALIZED VIEW public.mv_dashboard_kpi_hourly OWNER TO dbadmin;

--
-- Name: roles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.roles (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(256) NOT NULL,
    normalized_name character varying(256) NOT NULL,
    description text,
    role_type character varying(50) NOT NULL,
    role_category character varying(50),
    is_system_role boolean DEFAULT false,
    permission_level integer DEFAULT 1,
    can_access_all_vessels boolean DEFAULT false,
    can_access_all_companies boolean DEFAULT false,
    is_active boolean DEFAULT true,
    is_deleted boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    CONSTRAINT chk_role_type CHECK (((role_type)::text = ANY ((ARRAY['System'::character varying, 'Company'::character varying, 'Vessel'::character varying, 'Custom'::character varying])::text[])))
);


ALTER TABLE public.roles OWNER TO dbadmin;

--
-- Name: TABLE roles; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.roles IS 'System and custom roles for authorization';


--
-- Name: user_activity_logs; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_activity_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    activity_type character varying(100) NOT NULL,
    activity_category character varying(50),
    activity_description text,
    entity_type character varying(100),
    entity_id uuid,
    entity_name character varying(500),
    old_values jsonb,
    new_values jsonb,
    changed_fields text[],
    module_name character varying(100),
    screen_name character varying(100),
    action_name character varying(100),
    ip_address inet,
    user_agent text,
    request_id uuid,
    session_id uuid,
    request_url text,
    http_method character varying(10),
    request_headers jsonb,
    request_body jsonb,
    response_status_code integer,
    response_time_ms integer,
    error_message text,
    stack_trace text,
    device_id character varying(200),
    device_type character varying(50),
    browser character varying(100),
    operating_system character varying(100),
    location character varying(200),
    latitude numeric(10,8),
    longitude numeric(11,8),
    vessel_id uuid,
    is_offline_sync boolean DEFAULT false,
    sync_date timestamp without time zone,
    database_queries integer,
    database_time_ms integer,
    total_time_ms integer,
    is_suspicious boolean DEFAULT false,
    security_alerts text[],
    activity_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    activity_year integer GENERATED ALWAYS AS (EXTRACT(year FROM activity_date)) STORED,
    activity_month integer GENERATED ALWAYS AS (EXTRACT(month FROM activity_date)) STORED
);


ALTER TABLE public.user_activity_logs OWNER TO dbadmin;

--
-- Name: TABLE user_activity_logs; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.user_activity_logs IS 'Comprehensive audit trail of all user actions in the system';


--
-- Name: user_api_keys; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_api_keys (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    key_name character varying(256) NOT NULL,
    key_description text,
    api_key_hash character varying(256) NOT NULL,
    key_prefix character varying(10) NOT NULL,
    scopes text[] NOT NULL,
    allowed_origins text[],
    allowed_ips inet[],
    allowed_methods text[],
    rate_limit_per_hour integer DEFAULT 1000,
    rate_limit_per_day integer DEFAULT 10000,
    last_used_date timestamp without time zone,
    last_used_ip inet,
    last_used_endpoint character varying(500),
    total_requests bigint DEFAULT 0,
    failed_requests bigint DEFAULT 0,
    valid_from timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    expire_date timestamp without time zone,
    is_active boolean DEFAULT true,
    requires_ip_whitelist boolean DEFAULT false,
    requires_origin_check boolean DEFAULT true,
    allow_localhost boolean DEFAULT false,
    revoked_date timestamp without time zone,
    revoked_by uuid,
    revoke_reason text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.user_api_keys OWNER TO dbadmin;

--
-- Name: user_company_access; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_company_access (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    company_id uuid NOT NULL,
    access_type character varying(50) NOT NULL,
    access_role character varying(50),
    can_view_all_vessels boolean DEFAULT false,
    can_view_financials boolean DEFAULT false,
    can_manage_crew boolean DEFAULT false,
    can_approve_purchases boolean DEFAULT false,
    purchase_limit numeric(12,2),
    fleet_ids uuid[],
    restricted_vessel_ids uuid[],
    valid_from date DEFAULT CURRENT_DATE NOT NULL,
    valid_until date,
    contract_number character varying(100),
    contract_document_path character varying(500),
    is_active boolean DEFAULT true,
    is_primary_company boolean DEFAULT false,
    granted_by uuid,
    granted_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    revoked_by uuid,
    revoked_date timestamp without time zone,
    revoke_reason text
);


ALTER TABLE public.user_company_access OWNER TO dbadmin;

--
-- Name: user_compliance_documents; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_compliance_documents (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    document_type character varying(100) NOT NULL,
    document_category character varying(50),
    document_number character varying(200) NOT NULL,
    issuing_authority character varying(200) NOT NULL,
    issuing_country character varying(3) NOT NULL,
    issuing_place character varying(200),
    issue_date date NOT NULL,
    expiry_date date,
    is_mandatory boolean DEFAULT true,
    flag_states character varying(3)[],
    vessel_types text[],
    is_original_seen boolean DEFAULT false,
    original_verified_by uuid,
    original_verified_date date,
    requires_flag_endorsement boolean DEFAULT false,
    flag_endorsement_number character varying(100),
    flag_endorsement_date date,
    flag_endorsement_expiry date,
    document_path character varying(500),
    document_size_bytes bigint,
    document_hash character varying(256),
    uploaded_date timestamp without time zone,
    uploaded_by uuid,
    verification_status character varying(50) DEFAULT 'Pending'::character varying,
    verification_notes text,
    verified_by uuid,
    verified_date timestamp without time zone,
    rejection_reason text,
    alert_days_before_expiry integer DEFAULT 90,
    first_alert_sent timestamp without time zone,
    last_alert_sent timestamp without time zone,
    alert_count integer DEFAULT 0,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    is_deleted boolean DEFAULT false,
    deleted_date timestamp without time zone,
    deleted_by uuid
);


ALTER TABLE public.user_compliance_documents OWNER TO dbadmin;

--
-- Name: TABLE user_compliance_documents; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.user_compliance_documents IS 'All compliance documents including passports, certificates, and licenses';


--
-- Name: user_dashboards; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_dashboards (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    dashboard_definition_id uuid,
    name character varying(200),
    is_favorite boolean DEFAULT false,
    is_home_dashboard boolean DEFAULT false,
    custom_layout jsonb,
    auto_refresh boolean DEFAULT true,
    refresh_interval_seconds integer,
    theme character varying(50),
    last_accessed timestamp without time zone,
    access_count integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.user_dashboards OWNER TO dbadmin;

--
-- Name: user_digital_identity; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_digital_identity (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    fingerprint_template_hash text,
    fingerprint_enrolled_date timestamp without time zone,
    face_template_hash text,
    face_enrolled_date timestamp without time zone,
    iris_template_hash text,
    iris_enrolled_date timestamp without time zone,
    smart_card_uid character varying(200),
    smart_card_type character varying(50),
    smart_card_issued_date date,
    smart_card_expiry_date date,
    smart_card_pin_hash text,
    digital_certificate_serial character varying(200),
    digital_certificate_thumbprint character varying(256),
    digital_certificate_issued_date date,
    digital_certificate_expiry_date date,
    certificate_authority character varying(200),
    mfa_secret_encrypted text,
    mfa_backup_codes_encrypted text,
    mfa_enabled boolean DEFAULT false,
    mfa_methods text[],
    trusted_devices jsonb,
    max_trusted_devices integer DEFAULT 5,
    security_question_1 character varying(500),
    security_answer_1_hash text,
    security_question_2 character varying(500),
    security_answer_2_hash text,
    security_question_3 character varying(500),
    security_answer_3_hash text,
    security_awareness_level character varying(50),
    last_security_training_date date,
    phishing_test_score integer,
    nfc_enabled boolean DEFAULT false,
    bluetooth_enabled boolean DEFAULT false,
    qr_code_access boolean DEFAULT false,
    pin_code_hash text,
    vessel_access_cards jsonb,
    gangway_access_enabled boolean DEFAULT true,
    bridge_access_enabled boolean DEFAULT false,
    engine_room_access_enabled boolean DEFAULT false,
    gdpr_consent_date timestamp without time zone,
    biometric_consent_date timestamp without time zone,
    data_retention_consent boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    last_verified_date timestamp without time zone,
    verified_by uuid
);


ALTER TABLE public.user_digital_identity OWNER TO dbadmin;

--
-- Name: user_emergency_contacts; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_emergency_contacts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    contact_type character varying(50) NOT NULL,
    relationship character varying(100) NOT NULL,
    full_name character varying(200) NOT NULL,
    phone_primary character varying(50) NOT NULL,
    phone_secondary character varying(50),
    phone_work character varying(50),
    email character varying(256),
    address_line1 character varying(200),
    address_line2 character varying(200),
    city character varying(100),
    state_province character varying(100),
    postal_code character varying(20),
    country character varying(3) NOT NULL,
    speaks_english boolean DEFAULT true,
    preferred_language character varying(10),
    preferred_contact_time character varying(100),
    preferred_contact_method character varying(50),
    priority_order integer DEFAULT 1,
    is_primary boolean DEFAULT false,
    notify_for_emergency boolean DEFAULT true,
    notify_for_arrival boolean DEFAULT false,
    has_power_of_attorney boolean DEFAULT false,
    is_beneficiary boolean DEFAULT false,
    special_instructions text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    verified_date timestamp without time zone,
    verified_by uuid
);


ALTER TABLE public.user_emergency_contacts OWNER TO dbadmin;

--
-- Name: TABLE user_emergency_contacts; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.user_emergency_contacts IS 'Emergency contact information for crew members';


--
-- Name: user_group_members; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_group_members (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    group_id uuid NOT NULL,
    joined_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    joined_by uuid,
    left_date timestamp without time zone,
    left_reason character varying(200),
    is_group_admin boolean DEFAULT false,
    is_active boolean DEFAULT true
);


ALTER TABLE public.user_group_members OWNER TO dbadmin;

--
-- Name: user_groups; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_groups (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(256) NOT NULL,
    code character varying(50) NOT NULL,
    description text,
    group_type character varying(50) NOT NULL,
    parent_group_id uuid,
    company_id uuid,
    can_login_from_vessel boolean DEFAULT false,
    requires_vessel_assignment boolean DEFAULT false,
    is_admin_group boolean DEFAULT false,
    max_concurrent_users integer,
    is_active boolean DEFAULT true,
    is_deleted boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.user_groups OWNER TO dbadmin;

--
-- Name: user_login_attempts; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_login_attempts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    username character varying(256),
    email character varying(256),
    user_id uuid,
    attempt_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    ip_address inet NOT NULL,
    ip_location character varying(200),
    is_successful boolean DEFAULT false,
    failure_reason character varying(100),
    error_details text,
    user_agent text,
    device_fingerprint character varying(256),
    browser character varying(100),
    browser_version character varying(50),
    operating_system character varying(100),
    os_version character varying(50),
    device_type character varying(50),
    is_known_device boolean DEFAULT false,
    is_suspicious boolean DEFAULT false,
    risk_score integer DEFAULT 0,
    captcha_required boolean DEFAULT false,
    captcha_passed boolean,
    two_factor_required boolean DEFAULT false,
    two_factor_passed boolean,
    response_action character varying(50),
    lockout_until timestamp without time zone
);


ALTER TABLE public.user_login_attempts OWNER TO dbadmin;

--
-- Name: user_maritime_profiles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_maritime_profiles (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    imo_number character varying(20),
    seaman_book_number character varying(100),
    seaman_book_country character varying(3),
    seaman_book_issue_date date,
    seaman_book_expiry_date date,
    coc_number character varying(100),
    coc_rank character varying(100),
    coc_type character varying(50),
    coc_issuing_country character varying(3),
    coc_issue_date date,
    coc_expiry_date date,
    department character varying(50),
    current_rank_id uuid,
    rank_start_date date,
    total_sea_service_days integer DEFAULT 0,
    tanker_experience_days integer DEFAULT 0,
    gas_carrier_days integer DEFAULT 0,
    passenger_ship_days integer DEFAULT 0,
    availability_status character varying(50) DEFAULT 'Available'::character varying,
    current_vessel_id uuid,
    current_contract_id uuid,
    next_available_date date,
    home_airport character(3),
    nearest_international_airport character(3),
    passport_ready boolean DEFAULT true,
    us_visa_type character varying(50),
    us_visa_expiry date,
    schengen_visa_expiry date,
    preferred_vessel_types text[],
    preferred_routes text[],
    minimum_contract_days integer DEFAULT 90,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modified_date timestamp without time zone
);


ALTER TABLE public.user_maritime_profiles OWNER TO dbadmin;

--
-- Name: TABLE user_maritime_profiles; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.user_maritime_profiles IS 'Maritime-specific information including certificates and experience';


--
-- Name: user_medical_records; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_medical_records (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    medical_type character varying(100) NOT NULL,
    examination_date date NOT NULL,
    expiry_date date NOT NULL,
    result character varying(50) NOT NULL,
    restrictions text,
    follow_up_required boolean DEFAULT false,
    follow_up_date date,
    blood_type character varying(10),
    blood_rh character varying(10),
    allergies text,
    chronic_conditions text,
    current_medications text,
    emergency_medications text,
    height_cm integer,
    weight_kg numeric(5,2),
    bmi numeric(4,2),
    blood_pressure_systolic integer,
    blood_pressure_diastolic integer,
    pulse_rate integer,
    vision_left character varying(20),
    vision_right character varying(20),
    vision_corrected boolean DEFAULT false,
    color_vision_normal boolean DEFAULT true,
    hearing_normal boolean DEFAULT true,
    clinic_name character varying(200) NOT NULL,
    clinic_address text,
    clinic_country character varying(3),
    doctor_name character varying(200),
    doctor_license character varying(100),
    certificate_number character varying(100),
    certificate_path character varying(500),
    yellow_fever_date date,
    yellow_fever_expiry date,
    yellow_fever_certificate character varying(100),
    covid19_vaccine_type character varying(100),
    covid19_doses integer DEFAULT 0,
    covid19_last_dose_date date,
    covid19_certificate character varying(100),
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    is_confidential boolean DEFAULT true,
    shared_with_vessel boolean DEFAULT false
);


ALTER TABLE public.user_medical_records OWNER TO dbadmin;

--
-- Name: user_notifications; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_notifications (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    title character varying(256) NOT NULL,
    message text NOT NULL,
    summary character varying(500),
    notification_type character varying(50) NOT NULL,
    category character varying(50) NOT NULL,
    severity character varying(20) DEFAULT 'normal'::character varying,
    related_entity_type character varying(50),
    related_entity_id uuid,
    related_entity_name character varying(200),
    action_required boolean DEFAULT false,
    action_url character varying(500),
    action_text character varying(100),
    action_deadline timestamp without time zone,
    channels text[],
    email_sent boolean DEFAULT false,
    email_sent_date timestamp without time zone,
    sms_sent boolean DEFAULT false,
    sms_sent_date timestamp without time zone,
    push_sent boolean DEFAULT false,
    push_sent_date timestamp without time zone,
    is_read boolean DEFAULT false,
    read_date timestamp without time zone,
    is_archived boolean DEFAULT false,
    archived_date timestamp without time zone,
    is_deleted boolean DEFAULT false,
    deleted_date timestamp without time zone,
    group_id uuid,
    is_group_parent boolean DEFAULT false,
    scheduled_date timestamp without time zone,
    sent_date timestamp without time zone,
    expire_date timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid
);


ALTER TABLE public.user_notifications OWNER TO dbadmin;

--
-- Name: user_password_history; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_password_history (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    password_hash text NOT NULL,
    password_salt text,
    changed_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    changed_by uuid,
    change_reason character varying(100),
    ip_address inet,
    user_agent text,
    old_password_validated boolean DEFAULT true,
    force_change boolean DEFAULT false,
    expires_date timestamp without time zone,
    complexity_score integer,
    contains_common_patterns boolean DEFAULT false
);


ALTER TABLE public.user_password_history OWNER TO dbadmin;

--
-- Name: user_password_reset_tokens; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_password_reset_tokens (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    token_hash character varying(100) NOT NULL,
    expires_at timestamp without time zone NOT NULL,
    is_used boolean DEFAULT false,
    used_date timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.user_password_reset_tokens OWNER TO dbadmin;

--
-- Name: TABLE user_password_reset_tokens; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.user_password_reset_tokens IS 'Stores password reset tokens for users';


--
-- Name: COLUMN user_password_reset_tokens.token_hash; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_password_reset_tokens.token_hash IS 'SHA256 hash of the reset token';


--
-- Name: COLUMN user_password_reset_tokens.expires_at; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_password_reset_tokens.expires_at IS 'When this token expires';


--
-- Name: COLUMN user_password_reset_tokens.is_used; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_password_reset_tokens.is_used IS 'Whether this token has been used';


--
-- Name: COLUMN user_password_reset_tokens.used_date; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_password_reset_tokens.used_date IS 'When this token was used';


--
-- Name: user_payroll_settings; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_payroll_settings (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    base_salary numeric(12,2),
    salary_currency character(3) DEFAULT 'USD'::bpchar,
    payment_frequency character varying(50) DEFAULT 'Monthly'::character varying,
    bank_name character varying(200),
    bank_branch character varying(200),
    bank_address text,
    bank_country character varying(3),
    account_number_encrypted text,
    account_name character varying(200),
    bank_swift_code character varying(20),
    bank_iban character varying(50),
    bank_routing_number character varying(50),
    allotment_amount numeric(12,2),
    allotment_percentage numeric(5,2),
    allotment_currency character(3),
    allotment_bank_name character varying(200),
    allotment_account_encrypted text,
    allotment_account_name character varying(200),
    allotment_swift_code character varying(20),
    cash_advance_allowed boolean DEFAULT true,
    max_cash_advance_percentage numeric(5,2) DEFAULT 50.00,
    cash_advance_currency character(3) DEFAULT 'USD'::bpchar,
    slop_chest_limit numeric(10,2),
    tax_resident_country character varying(3),
    tax_identification_number character varying(50),
    tax_rate numeric(5,2),
    double_taxation_treaty boolean DEFAULT false,
    last_payment_date date,
    last_payment_amount numeric(12,2),
    total_paid_ytd numeric(12,2),
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    last_verified_date timestamp without time zone,
    verified_by uuid
);


ALTER TABLE public.user_payroll_settings OWNER TO dbadmin;

--
-- Name: user_permissions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_permissions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    role_id uuid,
    group_id uuid,
    module_code character varying(100) NOT NULL,
    permission_type character varying(50) NOT NULL,
    company_id uuid,
    vessel_id uuid,
    is_granted boolean DEFAULT true,
    granted_by uuid,
    granted_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    valid_from date DEFAULT CURRENT_DATE,
    valid_until date,
    reason text,
    CONSTRAINT chk_permission_target CHECK ((((user_id IS NOT NULL) AND (role_id IS NULL) AND (group_id IS NULL)) OR ((user_id IS NULL) AND (role_id IS NOT NULL) AND (group_id IS NULL)) OR ((user_id IS NULL) AND (role_id IS NULL) AND (group_id IS NOT NULL))))
);


ALTER TABLE public.user_permissions OWNER TO dbadmin;

--
-- Name: user_preferences; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_preferences (
    user_id uuid NOT NULL,
    theme character varying(20) DEFAULT 'light'::character varying,
    color_scheme character varying(50) DEFAULT 'blue'::character varying,
    font_size character varying(20) DEFAULT 'medium'::character varying,
    language character varying(10) DEFAULT 'en'::character varying,
    secondary_language character varying(10),
    date_format character varying(20) DEFAULT 'dd/MM/yyyy'::character varying,
    time_format character varying(20) DEFAULT 'HH:mm'::character varying,
    timezone character varying(50) DEFAULT 'UTC'::character varying,
    first_day_of_week integer DEFAULT 1,
    number_format character varying(20) DEFAULT '1,234.56'::character varying,
    currency character varying(3) DEFAULT 'USD'::character varying,
    email_notifications boolean DEFAULT true,
    email_frequency character varying(50) DEFAULT 'immediate'::character varying,
    sms_notifications boolean DEFAULT false,
    push_notifications boolean DEFAULT true,
    desktop_notifications boolean DEFAULT true,
    certificate_expiry_alert_days integer DEFAULT 90,
    document_expiry_alert_days integer DEFAULT 60,
    medical_expiry_alert_days integer DEFAULT 90,
    training_expiry_alert_days integer DEFAULT 90,
    contract_end_alert_days integer DEFAULT 30,
    email_signature text,
    auto_cc_email character varying(256),
    out_of_office boolean DEFAULT false,
    out_of_office_message text,
    default_dashboard character varying(50) DEFAULT 'overview'::character varying,
    dashboard_layout jsonb,
    widget_preferences jsonb,
    favorite_vessels uuid[],
    default_page_size integer DEFAULT 20,
    show_inactive_records boolean DEFAULT false,
    default_sort_order character varying(10) DEFAULT 'asc'::character varying,
    pinned_modules text[],
    recent_items_count integer DEFAULT 10,
    quick_links jsonb,
    saved_filters jsonb,
    favorite_reports uuid[],
    report_format character varying(20) DEFAULT 'pdf'::character varying,
    include_logo_in_reports boolean DEFAULT true,
    show_profile_photo boolean DEFAULT true,
    show_contact_info boolean DEFAULT true,
    allow_vessel_tracking boolean DEFAULT true,
    high_contrast_mode boolean DEFAULT false,
    screen_reader_mode boolean DEFAULT false,
    keyboard_navigation boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modified_date timestamp without time zone
);


ALTER TABLE public.user_preferences OWNER TO dbadmin;

--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_roles (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    role_id uuid NOT NULL,
    company_id uuid,
    vessel_id uuid,
    assigned_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    assigned_by uuid,
    valid_from date DEFAULT CURRENT_DATE NOT NULL,
    valid_until date,
    is_active boolean DEFAULT true,
    notes text,
    CONSTRAINT chk_role_scope CHECK ((((company_id IS NULL) AND (vessel_id IS NULL)) OR ((company_id IS NOT NULL) AND (vessel_id IS NULL)) OR ((company_id IS NOT NULL) AND (vessel_id IS NOT NULL))))
);


ALTER TABLE public.user_roles OWNER TO dbadmin;

--
-- Name: user_rotation_plans; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_rotation_plans (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    vessel_id uuid NOT NULL,
    rotation_type character varying(50) NOT NULL,
    contract_type character varying(50),
    rotation_pattern character varying(50),
    work_months integer NOT NULL,
    leave_months integer NOT NULL,
    planned_on_date date NOT NULL,
    planned_off_date date NOT NULL,
    actual_on_date date,
    actual_off_date date,
    relief_user_id uuid,
    relief_confirmed boolean DEFAULT false,
    relief_overlap_days integer DEFAULT 3,
    travel_requested boolean DEFAULT false,
    travel_approved boolean DEFAULT false,
    estimated_travel_cost numeric(10,2),
    travel_advance numeric(10,2),
    visa_required boolean DEFAULT false,
    visa_type character varying(100),
    yellow_fever_required boolean DEFAULT false,
    additional_documents text,
    status character varying(50) DEFAULT 'Draft'::character varying,
    approval_status character varying(50),
    approved_by uuid,
    approved_date timestamp without time zone,
    planning_notes text,
    cancellation_reason text,
    created_by uuid,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modified_by uuid,
    modified_date timestamp without time zone
);


ALTER TABLE public.user_rotation_plans OWNER TO dbadmin;

--
-- Name: user_sessions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_sessions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    session_token character varying(500) NOT NULL,
    refresh_token character varying(500),
    ip_address inet,
    user_agent text,
    device_id character varying(200),
    device_type character varying(50),
    device_name character varying(200),
    browser character varying(100),
    browser_version character varying(50),
    operating_system character varying(100),
    os_version character varying(50),
    login_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    last_activity timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    expire_date timestamp without time zone NOT NULL,
    logout_date timestamp without time zone,
    logout_reason character varying(100),
    is_active boolean DEFAULT true,
    is_persistent boolean DEFAULT false,
    login_location character varying(256),
    latitude numeric(10,8),
    longitude numeric(11,8),
    vessel_id uuid,
    failed_refresh_attempts integer DEFAULT 0,
    is_locked boolean DEFAULT false
);


ALTER TABLE public.user_sessions OWNER TO dbadmin;

--
-- Name: user_theme_preferences; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_theme_preferences (
    user_id uuid NOT NULL,
    theme_name character varying(50) DEFAULT 'ocean-blue'::character varying NOT NULL,
    is_dark_mode boolean DEFAULT false NOT NULL,
    custom_settings jsonb DEFAULT '{}'::jsonb,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT valid_theme_name CHECK (((theme_name)::text = ANY ((ARRAY['ocean-blue'::character varying, 'sunset-harbor'::character varying, 'emerald-sea'::character varying, 'classic-maritime'::character varying])::text[])))
);


ALTER TABLE public.user_theme_preferences OWNER TO dbadmin;

--
-- Name: TABLE user_theme_preferences; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.user_theme_preferences IS 'Stores user theme and display preferences';


--
-- Name: COLUMN user_theme_preferences.user_id; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_theme_preferences.user_id IS 'Reference to the user';


--
-- Name: COLUMN user_theme_preferences.theme_name; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_theme_preferences.theme_name IS 'Selected theme name';


--
-- Name: COLUMN user_theme_preferences.is_dark_mode; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_theme_preferences.is_dark_mode IS 'Whether dark mode is enabled';


--
-- Name: COLUMN user_theme_preferences.custom_settings; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON COLUMN public.user_theme_preferences.custom_settings IS 'Additional custom settings in JSON format';


--
-- Name: user_training_records; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_training_records (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    training_code character varying(100) NOT NULL,
    training_name character varying(200) NOT NULL,
    training_type character varying(100) NOT NULL,
    training_category character varying(50),
    stcw_regulation character varying(50),
    stcw_code character varying(50),
    certificate_number character varying(100),
    completion_date date NOT NULL,
    expiry_date date,
    grade character varying(50),
    score numeric(5,2),
    institution_name character varying(200) NOT NULL,
    institution_code character varying(50),
    institution_country character varying(3),
    instructor_name character varying(200),
    training_location character varying(200),
    approved_by_flag_state boolean DEFAULT false,
    flag_state_approval character varying(3)[],
    imo_approved boolean DEFAULT false,
    competency_level character varying(50),
    practical_assessment boolean DEFAULT false,
    theory_assessment boolean DEFAULT false,
    assessed_by uuid,
    assessment_date date,
    assessment_notes text,
    requires_refresher boolean DEFAULT true,
    refresher_interval_months integer,
    next_refresher_date date,
    refresher_alert_sent timestamp without time zone,
    certificate_path character varying(500),
    certificate_hash character varying(256),
    is_verified boolean DEFAULT false,
    verified_by uuid,
    verified_date timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.user_training_records OWNER TO dbadmin;

--
-- Name: user_vessel_assignments; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_vessel_assignments (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    vessel_id uuid NOT NULL,
    assignment_type character varying(50) NOT NULL,
    "position" character varying(100) NOT NULL,
    department character varying(50) NOT NULL,
    watch_keeping_duties boolean DEFAULT false,
    access_level character varying(50) DEFAULT 'Standard'::character varying,
    can_sign_documents boolean DEFAULT false,
    is_vessel_representative boolean DEFAULT false,
    contract_id uuid,
    embarkation_date date NOT NULL,
    planned_disembarkation_date date,
    actual_disembarkation_date date,
    contract_days integer,
    embarkation_port character varying(200),
    embarkation_country character varying(3),
    disembarkation_port character varying(200),
    disembarkation_country character varying(3),
    relieving_user_id uuid,
    handover_days integer DEFAULT 3,
    handover_completed boolean DEFAULT false,
    handover_notes text,
    status character varying(50) DEFAULT 'Planned'::character varying,
    is_active boolean DEFAULT true,
    assigned_by uuid,
    assigned_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modified_by uuid,
    modified_date timestamp without time zone,
    CONSTRAINT chk_assignment_dates CHECK (((actual_disembarkation_date IS NULL) OR (actual_disembarkation_date >= embarkation_date)))
);


ALTER TABLE public.user_vessel_assignments OWNER TO dbadmin;

--
-- Name: TABLE user_vessel_assignments; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.user_vessel_assignments IS 'Current and historical vessel assignments for crew';


--
-- Name: user_vessel_connectivity; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_vessel_connectivity (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    vessel_id uuid NOT NULL,
    assignment_id uuid,
    ship_email character varying(256),
    inmarsat_id character varying(100),
    iridium_number character varying(50),
    vsat_phone character varying(50),
    daily_data_limit_mb integer DEFAULT 100,
    monthly_data_limit_mb integer DEFAULT 3000,
    used_data_today_mb integer DEFAULT 0,
    used_data_month_mb integer DEFAULT 0,
    data_reset_date date,
    email_sync_enabled boolean DEFAULT true,
    sync_frequency_hours integer DEFAULT 12,
    last_sync_success timestamp without time zone,
    last_sync_attempt timestamp without time zone,
    sync_priority integer DEFAULT 5,
    personal_email_allowed boolean DEFAULT true,
    social_media_allowed boolean DEFAULT false,
    video_call_allowed boolean DEFAULT false,
    attachment_size_limit_mb integer DEFAULT 5,
    is_currently_onboard boolean DEFAULT false,
    connectivity_status character varying(50),
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modified_date timestamp without time zone
);


ALTER TABLE public.user_vessel_connectivity OWNER TO dbadmin;

--
-- Name: user_widget_preferences; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.user_widget_preferences (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    dashboard_widget_id uuid,
    custom_x integer,
    custom_y integer,
    custom_width integer,
    custom_height integer,
    is_collapsed boolean DEFAULT false,
    is_hidden boolean DEFAULT false,
    custom_config jsonb,
    custom_filters jsonb,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.user_widget_preferences OWNER TO dbadmin;

--
-- Name: users; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.users (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    username character varying(256) NOT NULL,
    normalized_username character varying(256) NOT NULL,
    email character varying(256) NOT NULL,
    normalized_email character varying(256) NOT NULL,
    email_confirmed boolean DEFAULT false,
    password_hash text,
    security_stamp text DEFAULT (gen_random_uuid())::text,
    first_name character varying(100) NOT NULL,
    middle_name character varying(100),
    last_name character varying(100) NOT NULL,
    display_name character varying(200),
    date_of_birth date,
    gender character varying(10),
    nationality character varying(3),
    place_of_birth character varying(200),
    phone_number character varying(50),
    phone_confirmed boolean DEFAULT false,
    mobile_number character varying(50),
    profile_picture character varying(500),
    company_id uuid,
    department_id uuid,
    employee_code character varying(50),
    hire_date date,
    two_factor_enabled boolean DEFAULT false,
    lockout_end timestamp with time zone,
    lockout_enabled boolean DEFAULT true,
    access_failed_count integer DEFAULT 0,
    must_change_password boolean DEFAULT false,
    last_password_change timestamp without time zone,
    password_expires_date timestamp without time zone,
    preferred_language character varying(10) DEFAULT 'en'::character varying,
    timezone character varying(50) DEFAULT 'UTC'::character varying,
    culture character varying(10) DEFAULT 'en-US'::character varying,
    is_active boolean DEFAULT true,
    is_deleted boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    employee_number character varying(50),
    passport_number character varying(100),
    passport_expiry_date date,
    seaman_book_number character varying(100),
    seaman_book_expiry_date date,
    department character varying(100),
    role character varying(100),
    access_level integer,
    CONSTRAINT chk_email CHECK (((email)::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text))
);


ALTER TABLE public.users OWNER TO dbadmin;

--
-- Name: TABLE users; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON TABLE public.users IS 'Core user table for authentication and profile information';


--
-- Name: v_active_crew_assignments; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_active_crew_assignments AS
 SELECT u.id AS user_id,
    u.username,
    u.first_name,
    u.last_name,
    u.email,
    mp.current_rank_id,
    mp.department,
    mp.availability_status,
    va.vessel_id,
    va."position",
    va.embarkation_date,
    va.planned_disembarkation_date,
    va.status AS assignment_status,
        CASE
            WHEN (va.planned_disembarkation_date < CURRENT_DATE) THEN 'Overdue'::text
            WHEN (va.planned_disembarkation_date < (CURRENT_DATE + '30 days'::interval)) THEN 'Due Soon'::text
            ELSE 'On Schedule'::text
        END AS disembarkation_status
   FROM ((public.users u
     JOIN public.user_maritime_profiles mp ON ((u.id = mp.user_id)))
     LEFT JOIN public.user_vessel_assignments va ON (((u.id = va.user_id) AND (va.is_active = true))))
  WHERE ((u.is_active = true) AND (u.is_deleted = false));


ALTER VIEW public.v_active_crew_assignments OWNER TO dbadmin;

--
-- Name: vessel_certificates; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_certificates (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    certificate_type_id uuid NOT NULL,
    certificate_number character varying(100),
    issue_date date NOT NULL,
    expiry_date date NOT NULL,
    last_endorsement_date date,
    next_endorsement_date date,
    last_intermediate_date date,
    next_intermediate_date date,
    issued_by character varying(200),
    issued_at character varying(100),
    issuing_authority character varying(200),
    survey_type character varying(100),
    surveyor_name character varying(200),
    survey_company character varying(200),
    document_path character varying(500),
    document_size bigint,
    document_hash character varying(64),
    status character varying(50) DEFAULT 'Valid'::character varying,
    is_original boolean DEFAULT true,
    remarks text,
    alert_sent boolean DEFAULT false,
    alert_sent_date timestamp without time zone,
    alert_acknowledged boolean DEFAULT false,
    alert_acknowledged_by uuid,
    alert_acknowledged_date timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.vessel_certificates OWNER TO dbadmin;

--
-- Name: vessel_crew_assignments; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_crew_assignments (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    user_id uuid NOT NULL,
    rank_id uuid,
    department character varying(50),
    sign_on_date date NOT NULL,
    sign_on_port character varying(100),
    expected_sign_off_date date,
    actual_sign_off_date date,
    sign_off_port character varying(100),
    relief_due_date date,
    relief_user_id uuid,
    relief_confirmed boolean DEFAULT false,
    status character varying(50) DEFAULT 'Onboard'::character varying,
    remarks text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.vessel_crew_assignments OWNER TO dbadmin;

--
-- Name: v_dashboard_active_alerts; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_active_alerts AS
 SELECT 'certificate_expiry'::text AS alert_type,
    'Certificate Expiry'::text AS alert_category,
    v.id AS vessel_id,
    v.vessel_name,
    'warning'::text AS severity,
    ((((ct.name)::text || ' expires in '::text) || (vc.expiry_date - CURRENT_DATE)) || ' days'::text) AS alert_message,
    vc.expiry_date AS due_date,
    'vessel_certificates'::text AS source_table,
    vc.id AS source_id,
    2 AS sort_order
   FROM ((public.vessel_certificates vc
     JOIN public.vessels v ON ((vc.vessel_id = v.id)))
     JOIN public.certificate_types ct ON ((vc.certificate_type_id = ct.id)))
  WHERE ((vc.is_deleted = false) AND (v.is_deleted = false) AND ((vc.status)::text = 'Valid'::text) AND ((vc.expiry_date >= CURRENT_DATE) AND (vc.expiry_date <= (CURRENT_DATE + '90 days'::interval))))
UNION ALL
 SELECT 'maintenance_overdue'::text AS alert_type,
    'Maintenance'::text AS alert_category,
    v.id AS vessel_id,
    v.vessel_name,
        CASE
            WHEN ((mj.priority)::text = 'Critical'::text) THEN 'critical'::text
            WHEN (mj.next_due_date < (CURRENT_DATE - '30 days'::interval)) THEN 'critical'::text
            ELSE 'warning'::text
        END AS severity,
    ((((mj.job_title)::text || ' is overdue by '::text) || (CURRENT_DATE - mj.next_due_date)) || ' days'::text) AS alert_message,
    mj.next_due_date AS due_date,
    'maintenance_jobs'::text AS source_table,
    mj.id AS source_id,
        CASE
            WHEN ((mj.priority)::text = 'Critical'::text) THEN 1
            WHEN (mj.next_due_date < (CURRENT_DATE - '30 days'::interval)) THEN 1
            ELSE 2
        END AS sort_order
   FROM (public.maintenance_jobs mj
     JOIN public.vessels v ON ((mj.vessel_id = v.id)))
  WHERE ((mj.is_active = true) AND (v.is_deleted = false) AND (mj.next_due_date < CURRENT_DATE))
UNION ALL
 SELECT 'crew_change'::text AS alert_type,
    'Crew Management'::text AS alert_category,
    v.id AS vessel_id,
    v.vessel_name,
    'info'::text AS severity,
    (('Crew member sign-off due in '::text || (vca.expected_sign_off_date - CURRENT_DATE)) || ' days'::text) AS alert_message,
    vca.expected_sign_off_date AS due_date,
    'vessel_crew_assignments'::text AS source_table,
    vca.id AS source_id,
    3 AS sort_order
   FROM (public.vessel_crew_assignments vca
     JOIN public.vessels v ON ((vca.vessel_id = v.id)))
  WHERE ((vca.is_deleted = false) AND (v.is_deleted = false) AND ((vca.status)::text = 'Onboard'::text) AND ((vca.expected_sign_off_date >= CURRENT_DATE) AND (vca.expected_sign_off_date <= (CURRENT_DATE + '30 days'::interval))))
  ORDER BY 10, 7;


ALTER VIEW public.v_dashboard_active_alerts OWNER TO dbadmin;

--
-- Name: v_dashboard_crew_summary; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_crew_summary AS
 SELECT v.id AS vessel_id,
    v.vessel_name,
    count(DISTINCT vca.user_id) FILTER (WHERE ((vca.status)::text = 'Onboard'::text)) AS total_crew_onboard,
    count(DISTINCT vca.user_id) FILTER (WHERE (((vca.status)::text = 'Onboard'::text) AND ((vca.department)::text = 'Deck'::text))) AS deck_crew,
    count(DISTINCT vca.user_id) FILTER (WHERE (((vca.status)::text = 'Onboard'::text) AND ((vca.department)::text = 'Engine'::text))) AS engine_crew,
    count(DISTINCT vca.user_id) FILTER (WHERE (((vca.status)::text = 'Onboard'::text) AND ((vca.department)::text = 'Catering'::text))) AS catering_crew,
    count(DISTINCT vca.user_id) FILTER (WHERE (vca.expected_sign_off_date <= (CURRENT_DATE + '30 days'::interval))) AS crew_changes_next_30_days,
    count(DISTINCT vca.user_id) FILTER (WHERE (vca.relief_due_date <= (CURRENT_DATE + '30 days'::interval))) AS relief_due_30_days,
    count(DISTINCT vca.user_id) FILTER (WHERE ((vca.relief_confirmed = false) AND (vca.relief_due_date <= (CURRENT_DATE + '60 days'::interval)))) AS unconfirmed_reliefs,
    count(DISTINCT ucd.id) FILTER (WHERE (ucd.expiry_date <= (CURRENT_DATE + '90 days'::interval))) AS expiring_documents,
    count(DISTINCT umr.id) FILTER (WHERE (umr.expiry_date <= (CURRENT_DATE + '90 days'::interval))) AS expiring_medicals,
    count(DISTINCT utr.id) FILTER (WHERE (utr.expiry_date <= (CURRENT_DATE + '90 days'::interval))) AS expiring_trainings,
    avg(EXTRACT(year FROM age((vca.sign_on_date)::timestamp with time zone))) FILTER (WHERE ((vca.status)::text = 'Onboard'::text)) AS avg_months_onboard,
    min(vca.sign_on_date) FILTER (WHERE ((vca.status)::text = 'Onboard'::text)) AS earliest_sign_on
   FROM ((((public.vessels v
     LEFT JOIN public.vessel_crew_assignments vca ON (((vca.vessel_id = v.id) AND (vca.is_deleted = false))))
     LEFT JOIN public.user_compliance_documents ucd ON (((ucd.user_id = vca.user_id) AND (ucd.is_deleted = false))))
     LEFT JOIN public.user_medical_records umr ON ((umr.user_id = vca.user_id)))
     LEFT JOIN public.user_training_records utr ON ((utr.user_id = vca.user_id)))
  WHERE (v.is_deleted = false)
  GROUP BY v.id, v.vessel_name;


ALTER VIEW public.v_dashboard_crew_summary OWNER TO dbadmin;

--
-- Name: v_dashboard_financial_summary; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_financial_summary AS
 WITH opex_data AS (
         SELECT v.id AS vessel_id,
            v.vessel_name,
            ((15000)::double precision + (random() * (5000)::double precision)) AS daily_opex,
            v.daily_hire_rate,
                CASE
                    WHEN (v.daily_hire_rate > (0)::numeric) THEN ((v.daily_hire_rate)::double precision - ((15000)::double precision + (random() * (5000)::double precision)))
                    ELSE (0)::double precision
                END AS daily_profit
           FROM public.vessels v
          WHERE (v.is_deleted = false)
        )
 SELECT vessel_id,
    vessel_name,
    daily_opex,
    daily_hire_rate,
    daily_profit,
        CASE
            WHEN (daily_hire_rate > (0)::numeric) THEN ((daily_profit / (daily_hire_rate)::double precision) * (100)::double precision)
            ELSE (0)::double precision
        END AS profit_margin,
    (daily_opex * (30)::double precision) AS monthly_opex,
    (daily_hire_rate * (30)::numeric) AS monthly_revenue,
    (daily_profit * (30)::double precision) AS monthly_profit
   FROM opex_data;


ALTER VIEW public.v_dashboard_financial_summary OWNER TO dbadmin;

--
-- Name: vessel_statuses; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_statuses (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    color character varying(7),
    is_operational boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.vessel_statuses OWNER TO dbadmin;

--
-- Name: vessel_types; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    icon character varying(50),
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.vessel_types OWNER TO dbadmin;

--
-- Name: v_dashboard_fleet_summary; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_fleet_summary AS
 SELECT count(DISTINCT v.id) AS total_vessels,
    count(DISTINCT
        CASE
            WHEN (v.is_active = true) THEN v.id
            ELSE NULL::uuid
        END) AS active_vessels,
    count(DISTINCT
        CASE
            WHEN ((vs.code)::text = 'ACT'::text) THEN v.id
            ELSE NULL::uuid
        END) AS operational_vessels,
    count(DISTINCT
        CASE
            WHEN ((vs.code)::text = 'DRY'::text) THEN v.id
            ELSE NULL::uuid
        END) AS dry_dock_vessels,
    count(DISTINCT
        CASE
            WHEN ((vs.code)::text = ANY ((ARRAY['LAY'::character varying, 'ARR'::character varying])::text[])) THEN v.id
            ELSE NULL::uuid
        END) AS idle_vessels,
    sum(v.deadweight_summer) AS total_deadweight,
    sum(v.gross_tonnage) AS total_gross_tonnage,
    avg((EXTRACT(year FROM CURRENT_DATE) - (v.build_year)::numeric)) AS average_age,
    count(DISTINCT
        CASE
            WHEN ((vt.code)::text = 'BULK'::text) THEN v.id
            ELSE NULL::uuid
        END) AS bulk_carriers,
    count(DISTINCT
        CASE
            WHEN ((vt.code)::text = 'CONT'::text) THEN v.id
            ELSE NULL::uuid
        END) AS container_vessels,
    count(DISTINCT
        CASE
            WHEN ((vt.code)::text = 'TANK'::text) THEN v.id
            ELSE NULL::uuid
        END) AS tankers,
    count(DISTINCT
        CASE
            WHEN ((vt.code)::text = 'LNG'::text) THEN v.id
            ELSE NULL::uuid
        END) AS lng_carriers,
    avg(vpd.average_speed) AS fleet_avg_speed,
    avg(vpd.fuel_consumption_sailing) AS fleet_avg_consumption,
    max(v.modified_date) AS last_update
   FROM (((public.vessels v
     LEFT JOIN public.vessel_types vt ON ((v.vessel_type_id = vt.id)))
     LEFT JOIN public.vessel_statuses vs ON ((v.vessel_status_id = vs.id)))
     LEFT JOIN LATERAL ( SELECT vessel_performance_daily.vessel_id,
            avg(vessel_performance_daily.average_speed) AS average_speed,
            avg(((vessel_performance_daily.hfo_consumption + vessel_performance_daily.mdo_consumption) + vessel_performance_daily.mgo_consumption)) AS fuel_consumption_sailing
           FROM public.vessel_performance_daily
          WHERE (vessel_performance_daily.report_date >= (CURRENT_DATE - '7 days'::interval))
          GROUP BY vessel_performance_daily.vessel_id) vpd ON ((vpd.vessel_id = v.id)))
  WHERE (v.is_deleted = false);


ALTER VIEW public.v_dashboard_fleet_summary OWNER TO dbadmin;

--
-- Name: vessel_equipment; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_equipment (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    parent_equipment_id uuid,
    equipment_code character varying(100) NOT NULL,
    equipment_name character varying(200) NOT NULL,
    category_id uuid,
    maker character varying(200),
    model character varying(200),
    serial_number character varying(100),
    installation_date date,
    running_hours numeric(10,2),
    last_maintenance_date date,
    maintenance_interval_hours integer,
    maintenance_interval_days integer,
    is_critical boolean DEFAULT false,
    is_spare_required boolean DEFAULT false,
    status character varying(50) DEFAULT 'Operational'::character varying,
    location character varying(200),
    remarks text,
    manual_path character varying(500),
    drawing_path character varying(500),
    specifications jsonb,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.vessel_equipment OWNER TO dbadmin;

--
-- Name: work_orders; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.work_orders (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    maintenance_job_id uuid,
    work_order_number character varying(100) NOT NULL,
    title character varying(500) NOT NULL,
    description text,
    work_type character varying(50),
    priority character varying(20) DEFAULT 'Normal'::character varying,
    planned_start_date date,
    planned_end_date date,
    planned_hours numeric(10,2),
    actual_start_date timestamp without time zone,
    actual_end_date timestamp without time zone,
    actual_hours numeric(10,2),
    assigned_to character varying(200),
    assigned_by uuid,
    assigned_date timestamp without time zone,
    completed_by character varying(200),
    completed_date timestamp without time zone,
    completion_remarks text,
    verified_by character varying(200),
    verified_date timestamp without time zone,
    verification_remarks text,
    spare_parts_used jsonb,
    cost_estimate numeric(12,2),
    actual_cost numeric(12,2),
    status character varying(50) DEFAULT 'Open'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.work_orders OWNER TO dbadmin;

--
-- Name: v_dashboard_maintenance_summary; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_maintenance_summary AS
 SELECT v.id AS vessel_id,
    v.vessel_name,
    vt.name AS vessel_type,
    count(DISTINCT mj.id) AS total_maintenance_jobs,
    count(DISTINCT mj.id) FILTER (WHERE (mj.next_due_date < CURRENT_DATE)) AS overdue_jobs,
    count(DISTINCT mj.id) FILTER (WHERE ((mj.next_due_date >= CURRENT_DATE) AND (mj.next_due_date <= (CURRENT_DATE + '30 days'::interval)))) AS due_within_30_days,
    count(DISTINCT mj.id) FILTER (WHERE ((mj.next_due_date >= CURRENT_DATE) AND (mj.next_due_date <= (CURRENT_DATE + '90 days'::interval)))) AS due_within_90_days,
    count(DISTINCT mj.id) FILTER (WHERE ((mj.priority)::text = 'Critical'::text)) AS critical_jobs,
    count(DISTINCT mj.id) FILTER (WHERE (((mj.priority)::text = 'Critical'::text) AND (mj.next_due_date < CURRENT_DATE))) AS overdue_critical_jobs,
    count(DISTINCT wo.id) AS total_work_orders,
    count(DISTINCT wo.id) FILTER (WHERE ((wo.status)::text = 'Open'::text)) AS open_work_orders,
    count(DISTINCT wo.id) FILTER (WHERE ((wo.status)::text = 'In Progress'::text)) AS in_progress_work_orders,
    count(DISTINCT wo.id) FILTER (WHERE (((wo.status)::text = 'Completed'::text) AND (wo.completed_date >= (CURRENT_DATE - '30 days'::interval)))) AS completed_last_30_days,
    avg((EXTRACT(epoch FROM (wo.actual_end_date - wo.actual_start_date)) / (3600)::numeric)) FILTER (WHERE ((wo.status)::text = 'Completed'::text)) AS avg_completion_hours,
    count(DISTINCT wo.id) FILTER (WHERE (((wo.status)::text = 'Completed'::text) AND (wo.actual_end_date <= wo.planned_end_date))) AS on_time_completions,
    count(DISTINCT ve.id) AS total_equipment,
    count(DISTINCT ve.id) FILTER (WHERE (ve.is_critical = true)) AS critical_equipment,
    count(DISTINCT ve.id) FILTER (WHERE ((ve.status)::text <> 'Operational'::text)) AS non_operational_equipment
   FROM ((((public.vessels v
     JOIN public.vessel_types vt ON ((v.vessel_type_id = vt.id)))
     LEFT JOIN public.maintenance_jobs mj ON (((mj.vessel_id = v.id) AND (mj.is_active = true))))
     LEFT JOIN public.work_orders wo ON (((wo.vessel_id = v.id) AND (wo.is_deleted = false))))
     LEFT JOIN public.vessel_equipment ve ON (((ve.vessel_id = v.id) AND (ve.is_deleted = false))))
  WHERE (v.is_deleted = false)
  GROUP BY v.id, v.vessel_name, vt.name;


ALTER VIEW public.v_dashboard_maintenance_summary OWNER TO dbadmin;

--
-- Name: v_dashboard_port_calls; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_port_calls AS
 SELECT v.id AS vessel_id,
    v.vessel_name,
    vpd.next_port AS port_name,
    vpd.eta_next_port,
    vpd.departure_port AS last_port,
    vpd.position_latitude,
    vpd.position_longitude,
    vpd.average_speed,
        CASE
            WHEN (vpd.eta_next_port IS NOT NULL) THEN (EXTRACT(epoch FROM ((vpd.eta_next_port)::timestamp with time zone - CURRENT_TIMESTAMP)) / (3600)::numeric)
            ELSE NULL::numeric
        END AS hours_to_arrival
   FROM (public.vessels v
     JOIN LATERAL ( SELECT vessel_performance_daily.id,
            vessel_performance_daily.vessel_id,
            vessel_performance_daily.report_date,
            vessel_performance_daily.voyage_number,
            vessel_performance_daily.position_latitude,
            vessel_performance_daily.position_longitude,
            vessel_performance_daily.position_timestamp,
            vessel_performance_daily.departure_port,
            vessel_performance_daily.departure_date,
            vessel_performance_daily.arrival_port,
            vessel_performance_daily.arrival_date,
            vessel_performance_daily.next_port,
            vessel_performance_daily.eta_next_port,
            vessel_performance_daily.distance_to_go,
            vessel_performance_daily.distance_covered_24h,
            vessel_performance_daily.average_speed,
            vessel_performance_daily.sailing_hours,
            vessel_performance_daily.port_hours,
            vessel_performance_daily.anchor_hours,
            vessel_performance_daily.drifting_hours,
            vessel_performance_daily.hfo_consumption,
            vessel_performance_daily.mdo_consumption,
            vessel_performance_daily.mgo_consumption,
            vessel_performance_daily.lng_consumption,
            vessel_performance_daily.hfo_rob,
            vessel_performance_daily.mdo_rob,
            vessel_performance_daily.mgo_rob,
            vessel_performance_daily.lng_rob,
            vessel_performance_daily.fresh_water_produced,
            vessel_performance_daily.fresh_water_consumed,
            vessel_performance_daily.fresh_water_rob,
            vessel_performance_daily.wind_force,
            vessel_performance_daily.wind_direction,
            vessel_performance_daily.sea_state,
            vessel_performance_daily.swell_height,
            vessel_performance_daily.visibility,
            vessel_performance_daily.main_engine_hours,
            vessel_performance_daily.main_engine_rpm_avg,
            vessel_performance_daily.main_engine_load_pct,
            vessel_performance_daily.cargo_loaded,
            vessel_performance_daily.cargo_discharged,
            vessel_performance_daily.cargo_onboard,
            vessel_performance_daily.master_remarks,
            vessel_performance_daily.created_date,
            vessel_performance_daily.created_by,
            vessel_performance_daily.modified_date,
            vessel_performance_daily.modified_by
           FROM public.vessel_performance_daily
          WHERE (vessel_performance_daily.vessel_id = v.id)
          ORDER BY vessel_performance_daily.report_date DESC
         LIMIT 1) vpd ON (true))
  WHERE ((v.is_deleted = false) AND (v.is_active = true) AND (vpd.next_port IS NOT NULL));


ALTER VIEW public.v_dashboard_port_calls OWNER TO dbadmin;

--
-- Name: v_dashboard_safety_summary; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_safety_summary AS
 SELECT v.id AS vessel_id,
    v.vessel_name,
    count(DISTINCT vi.id) AS total_inspections_ytd,
    count(DISTINCT vi.id) FILTER (WHERE ((vi.result)::text = 'Pass'::text)) AS passed_inspections,
    count(DISTINCT vi.id) FILTER (WHERE ((vi.result)::text = 'Detained'::text)) AS detentions,
    sum(vi.deficiencies_count) AS total_deficiencies,
    avg(vi.deficiencies_count) AS avg_deficiencies_per_inspection,
    count(DISTINCT vi.id) FILTER (WHERE ((it.code)::text = 'PSC'::text)) AS psc_inspections,
    sum(vi.deficiencies_count) FILTER (WHERE ((it.code)::text = 'PSC'::text)) AS psc_deficiencies,
    count(DISTINCT vid.id) FILTER (WHERE ((vid.status)::text = 'Open'::text)) AS open_deficiencies,
    count(DISTINCT vid.id) FILTER (WHERE (((vid.status)::text = 'Open'::text) AND ((vid.severity)::text = 'Major'::text))) AS major_deficiencies,
    count(DISTINCT vid.id) FILTER (WHERE (vid.is_detainable = true)) AS detainable_deficiencies,
    count(DISTINCT vc.id) FILTER (WHERE ((vc.status)::text = 'Expired'::text)) AS expired_certificates,
    count(DISTINCT vc.id) FILTER (WHERE (vc.expiry_date <= (CURRENT_DATE + '30 days'::interval))) AS certificates_expiring_soon
   FROM ((((public.vessels v
     LEFT JOIN public.vessel_inspections vi ON (((vi.vessel_id = v.id) AND (vi.inspection_date >= date_trunc('year'::text, (CURRENT_DATE)::timestamp with time zone)) AND (vi.is_deleted = false))))
     LEFT JOIN public.inspection_types it ON ((vi.inspection_type_id = it.id)))
     LEFT JOIN public.inspection_deficiencies vid ON ((vid.inspection_id = vi.id)))
     LEFT JOIN public.vessel_certificates vc ON (((vc.vessel_id = v.id) AND (vc.is_deleted = false))))
  WHERE (v.is_deleted = false)
  GROUP BY v.id, v.vessel_name;


ALTER VIEW public.v_dashboard_safety_summary OWNER TO dbadmin;

--
-- Name: v_dashboard_vessel_details; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_vessel_details AS
 SELECT v.id,
    v.vessel_code,
    v.vessel_name,
    v.imo_number,
    vt.name AS vessel_type,
    vs.name AS vessel_status,
    vs.color AS status_color,
    fs.country_name AS flag_state,
    v.gross_tonnage,
    v.deadweight_summer,
    (EXTRACT(year FROM CURRENT_DATE) - (v.build_year)::numeric) AS vessel_age,
    cert.total_certificates,
    cert.valid_certificates,
    cert.expiring_30_days,
    cert.expiring_90_days,
    cert.expired_certificates,
    cert.next_expiry_date,
    cert.next_expiry_certificate,
    insp.total_inspections,
    insp.last_inspection_date,
    insp.last_inspection_type,
    insp.open_deficiencies,
    insp.overdue_deficiencies,
    maint.total_jobs,
    maint.overdue_jobs,
    maint.due_30_days,
    maint.open_work_orders,
    maint.critical_work_orders,
    crew.total_crew,
    crew.officers_onboard,
    crew.ratings_onboard,
    crew.crew_changes_30_days,
    perf.current_position_lat,
    perf.current_position_lon,
    perf.current_speed,
    perf.current_port,
    perf.next_port,
    perf.eta_next_port,
    perf.fuel_rob_total,
    comm.current_charter_type,
    comm.current_charterer,
    comm.current_cargo,
    comm.daily_hire_rate,
    v.is_active,
    v.modified_date
   FROM (((((((((public.vessels v
     LEFT JOIN public.vessel_types vt ON ((v.vessel_type_id = vt.id)))
     LEFT JOIN public.vessel_statuses vs ON ((v.vessel_status_id = vs.id)))
     LEFT JOIN public.flag_states fs ON ((v.flag_state_id = fs.id)))
     LEFT JOIN LATERAL ( SELECT vc.vessel_id,
            count(*) AS total_certificates,
            count(*) FILTER (WHERE (((vc.status)::text = 'Valid'::text) AND (vc.is_deleted = false))) AS valid_certificates,
            count(*) FILTER (WHERE ((vc.expiry_date <= (CURRENT_DATE + '30 days'::interval)) AND ((vc.status)::text = 'Valid'::text))) AS expiring_30_days,
            count(*) FILTER (WHERE ((vc.expiry_date <= (CURRENT_DATE + '90 days'::interval)) AND ((vc.status)::text = 'Valid'::text))) AS expiring_90_days,
            count(*) FILTER (WHERE (((vc.status)::text = 'Expired'::text) AND (vc.is_deleted = false))) AS expired_certificates,
            min(vc.expiry_date) FILTER (WHERE (((vc.status)::text = 'Valid'::text) AND (vc.expiry_date > CURRENT_DATE))) AS next_expiry_date,
            (array_agg(ct.name ORDER BY vc.expiry_date))[1] AS next_expiry_certificate
           FROM (public.vessel_certificates vc
             JOIN public.certificate_types ct ON ((vc.certificate_type_id = ct.id)))
          WHERE (vc.vessel_id = v.id)
          GROUP BY vc.vessel_id) cert ON (true))
     LEFT JOIN LATERAL ( SELECT vi.vessel_id,
            count(*) AS total_inspections,
            max(vi.inspection_date) AS last_inspection_date,
            (array_agg(it.name ORDER BY vi.inspection_date DESC))[1] AS last_inspection_type,
            sum(vi.deficiencies_count) FILTER (WHERE (((vi.status)::text = 'Completed'::text) AND (vi.inspection_date >= (CURRENT_DATE - '1 year'::interval)))) AS open_deficiencies,
            count(DISTINCT vid.id) FILTER (WHERE (((vid.status)::text = 'Open'::text) AND (vid.resolved_date IS NULL))) AS overdue_deficiencies
           FROM ((public.vessel_inspections vi
             LEFT JOIN public.inspection_types it ON ((vi.inspection_type_id = it.id)))
             LEFT JOIN public.inspection_deficiencies vid ON ((vid.inspection_id = vi.id)))
          WHERE ((vi.vessel_id = v.id) AND (vi.is_deleted = false))
          GROUP BY vi.vessel_id) insp ON (true))
     LEFT JOIN LATERAL ( SELECT mj.vessel_id,
            count(DISTINCT mj.id) AS total_jobs,
            count(DISTINCT mj.id) FILTER (WHERE (mj.next_due_date < CURRENT_DATE)) AS overdue_jobs,
            count(DISTINCT mj.id) FILTER (WHERE (mj.next_due_date <= (CURRENT_DATE + '30 days'::interval))) AS due_30_days,
            count(DISTINCT wo.id) FILTER (WHERE ((wo.status)::text = ANY ((ARRAY['Open'::character varying, 'In Progress'::character varying])::text[]))) AS open_work_orders,
            count(DISTINCT wo.id) FILTER (WHERE (((wo.status)::text = ANY ((ARRAY['Open'::character varying, 'In Progress'::character varying])::text[])) AND ((wo.priority)::text = 'Critical'::text))) AS critical_work_orders
           FROM (public.maintenance_jobs mj
             LEFT JOIN public.work_orders wo ON ((wo.vessel_id = mj.vessel_id)))
          WHERE ((mj.vessel_id = v.id) AND (mj.is_active = true))
          GROUP BY mj.vessel_id) maint ON (true))
     LEFT JOIN LATERAL ( SELECT vca.vessel_id,
            count(*) FILTER (WHERE ((vca.status)::text = 'Onboard'::text)) AS total_crew,
            count(*) FILTER (WHERE (((vca.status)::text = 'Onboard'::text) AND ((vca.department)::text = 'Deck'::text))) AS officers_onboard,
            count(*) FILTER (WHERE (((vca.status)::text = 'Onboard'::text) AND ((vca.department)::text = ANY ((ARRAY['Engine'::character varying, 'Catering'::character varying])::text[])))) AS ratings_onboard,
            count(*) FILTER (WHERE (vca.expected_sign_off_date <= (CURRENT_DATE + '30 days'::interval))) AS crew_changes_30_days
           FROM public.vessel_crew_assignments vca
          WHERE ((vca.vessel_id = v.id) AND (vca.is_deleted = false))
          GROUP BY vca.vessel_id) crew ON (true))
     LEFT JOIN LATERAL ( SELECT vpd.vessel_id,
            vpd.position_latitude AS current_position_lat,
            vpd.position_longitude AS current_position_lon,
            vpd.average_speed AS current_speed,
            vpd.departure_port AS current_port,
            vpd.next_port,
            vpd.eta_next_port,
            ((vpd.hfo_rob + vpd.mdo_rob) + vpd.mgo_rob) AS fuel_rob_total
           FROM public.vessel_performance_daily vpd
          WHERE (vpd.vessel_id = v.id)
          ORDER BY vpd.report_date DESC
         LIMIT 1) perf ON (true))
     LEFT JOIN LATERAL ( SELECT v.charter_type AS current_charter_type,
            'Charter Company'::text AS current_charterer,
            'General Cargo'::text AS current_cargo,
            v.daily_hire_rate
           FROM public.vessels
          WHERE (vessels.id = v.id)) comm ON (true))
  WHERE (v.is_deleted = false);


ALTER VIEW public.v_dashboard_vessel_details OWNER TO dbadmin;

--
-- Name: v_dashboard_voyage_summary; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_dashboard_voyage_summary AS
 WITH voyage_data AS (
         SELECT v.id AS vessel_id,
            v.vessel_name,
            vt.name AS vessel_type,
            count(DISTINCT vpd.voyage_number) AS total_voyages,
            sum(vpd.distance_covered_24h) AS total_distance,
            avg(vpd.average_speed) AS avg_speed,
            sum(((vpd.hfo_consumption + vpd.mdo_consumption) + vpd.mgo_consumption)) AS total_fuel_consumed,
            sum(vpd.sailing_hours) AS total_sailing_hours,
            sum(vpd.port_hours) AS total_port_hours,
            avg(
                CASE
                    WHEN (vpd.sailing_hours > (0)::numeric) THEN (((vpd.hfo_consumption + vpd.mdo_consumption) + vpd.mgo_consumption) / vpd.sailing_hours)
                    ELSE (0)::numeric
                END) AS avg_consumption_per_hour
           FROM ((public.vessels v
             JOIN public.vessel_types vt ON ((v.vessel_type_id = vt.id)))
             LEFT JOIN public.vessel_performance_daily vpd ON ((vpd.vessel_id = v.id)))
          WHERE ((v.is_deleted = false) AND (vpd.report_date >= (CURRENT_DATE - '30 days'::interval)))
          GROUP BY v.id, v.vessel_name, vt.name
        )
 SELECT vessel_id,
    vessel_name,
    vessel_type,
    total_voyages,
    total_distance,
    avg_speed,
    total_fuel_consumed,
    total_sailing_hours,
    total_port_hours,
    avg_consumption_per_hour,
        CASE
            WHEN ((total_sailing_hours + total_port_hours) > (0)::numeric) THEN ((total_sailing_hours / (total_sailing_hours + total_port_hours)) * (100)::numeric)
            ELSE (0)::numeric
        END AS utilization_rate
   FROM voyage_data;


ALTER VIEW public.v_dashboard_voyage_summary OWNER TO dbadmin;

--
-- Name: v_document_expiry_alerts; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_document_expiry_alerts AS
 SELECT u.id AS user_id,
    (((u.first_name)::text || ' '::text) || (u.last_name)::text) AS full_name,
    u.employee_code,
    cd.document_type,
    cd.document_number,
    cd.expiry_date,
        CASE
            WHEN (cd.expiry_date < CURRENT_DATE) THEN 'Expired'::text
            WHEN (cd.expiry_date < (CURRENT_DATE + '30 days'::interval)) THEN 'Critical'::text
            WHEN (cd.expiry_date < (CURRENT_DATE + '60 days'::interval)) THEN 'Warning'::text
            WHEN (cd.expiry_date < (CURRENT_DATE + '90 days'::interval)) THEN 'Notice'::text
            ELSE 'Valid'::text
        END AS status,
    (cd.expiry_date - CURRENT_DATE) AS days_until_expiry,
    va.vessel_id,
    cd.verification_status
   FROM ((public.users u
     JOIN public.user_compliance_documents cd ON ((u.id = cd.user_id)))
     LEFT JOIN public.user_vessel_assignments va ON (((u.id = va.user_id) AND (va.is_active = true))))
  WHERE ((cd.is_deleted = false) AND (cd.expiry_date IS NOT NULL) AND (u.is_active = true))
  ORDER BY cd.expiry_date;


ALTER VIEW public.v_document_expiry_alerts OWNER TO dbadmin;

--
-- Name: v_translations; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_translations AS
 SELECT l.code AS language_code,
    l.name AS language_name,
    lt.text_key,
    lt.text_value,
    lt.module,
    lt.is_approved
   FROM (public.language_texts lt
     JOIN public.languages l ON ((lt.language_id = l.id)))
  WHERE (l.is_active = true)
  ORDER BY l.sort_order, lt.module, lt.text_key;


ALTER VIEW public.v_translations OWNER TO dbadmin;

--
-- Name: v_user_effective_permissions; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_user_effective_permissions AS
 WITH role_permissions AS (
         SELECT ur.user_id,
            p.module_code,
            p.permission_type,
            p.company_id,
            p.vessel_id,
            ('Role: '::text || (r.name)::text) AS source
           FROM ((public.user_roles ur
             JOIN public.roles r ON ((ur.role_id = r.id)))
             JOIN public.user_permissions p ON ((p.role_id = r.id)))
          WHERE ((ur.is_active = true) AND (r.is_active = true) AND (p.is_granted = true) AND ((p.valid_until IS NULL) OR (p.valid_until >= CURRENT_DATE)))
        ), group_permissions AS (
         SELECT ugm.user_id,
            p.module_code,
            p.permission_type,
            p.company_id,
            p.vessel_id,
            ('Group: '::text || (ug.name)::text) AS source
           FROM ((public.user_group_members ugm
             JOIN public.user_groups ug ON ((ugm.group_id = ug.id)))
             JOIN public.user_permissions p ON ((p.group_id = ug.id)))
          WHERE ((ugm.is_active = true) AND (ug.is_active = true) AND (p.is_granted = true) AND ((p.valid_until IS NULL) OR (p.valid_until >= CURRENT_DATE)))
        ), direct_permissions AS (
         SELECT p.user_id,
            p.module_code,
            p.permission_type,
            p.company_id,
            p.vessel_id,
            'Direct Assignment'::text AS source
           FROM public.user_permissions p
          WHERE ((p.user_id IS NOT NULL) AND (p.is_granted = true) AND ((p.valid_until IS NULL) OR (p.valid_until >= CURRENT_DATE)))
        )
 SELECT DISTINCT user_id,
    module_code,
    permission_type,
    company_id,
    vessel_id,
    source
   FROM ( SELECT role_permissions.user_id,
            role_permissions.module_code,
            role_permissions.permission_type,
            role_permissions.company_id,
            role_permissions.vessel_id,
            role_permissions.source
           FROM role_permissions
        UNION ALL
         SELECT group_permissions.user_id,
            group_permissions.module_code,
            group_permissions.permission_type,
            group_permissions.company_id,
            group_permissions.vessel_id,
            group_permissions.source
           FROM group_permissions
        UNION ALL
         SELECT direct_permissions.user_id,
            direct_permissions.module_code,
            direct_permissions.permission_type,
            direct_permissions.company_id,
            direct_permissions.vessel_id,
            direct_permissions.source
           FROM direct_permissions) all_permissions;


ALTER VIEW public.v_user_effective_permissions OWNER TO dbadmin;

--
-- Name: v_vessel_dashboard_summary; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.v_vessel_dashboard_summary AS
 SELECT v.id,
    v.vessel_code,
    v.vessel_name,
    v.imo_number,
    vt.name AS vessel_type,
    vs.name AS vessel_status,
    vs.color AS status_color,
    fs.country_name AS flag_state,
    v.gross_tonnage,
    v.deadweight_summer,
    ( SELECT count(*) AS count
           FROM public.vessel_certificates vc
          WHERE ((vc.vessel_id = v.id) AND ((vc.status)::text = 'Valid'::text) AND (vc.is_deleted = false))) AS valid_certificates,
    ( SELECT count(*) AS count
           FROM public.vessel_certificates vc
          WHERE ((vc.vessel_id = v.id) AND (vc.expiry_date <= (CURRENT_DATE + '90 days'::interval)) AND ((vc.status)::text = 'Valid'::text) AND (vc.is_deleted = false))) AS expiring_certificates,
    ( SELECT count(*) AS count
           FROM public.vessel_certificates vc
          WHERE ((vc.vessel_id = v.id) AND ((vc.status)::text = 'Expired'::text) AND (vc.is_deleted = false))) AS expired_certificates,
    ( SELECT vi.inspection_date
           FROM public.vessel_inspections vi
          WHERE ((vi.vessel_id = v.id) AND (vi.is_deleted = false))
          ORDER BY vi.inspection_date DESC
         LIMIT 1) AS last_inspection_date,
    ( SELECT vi.deficiencies_count
           FROM public.vessel_inspections vi
          WHERE ((vi.vessel_id = v.id) AND (vi.is_deleted = false))
          ORDER BY vi.inspection_date DESC
         LIMIT 1) AS last_inspection_deficiencies,
    ( SELECT count(*) AS count
           FROM public.maintenance_jobs mj
          WHERE ((mj.vessel_id = v.id) AND (mj.next_due_date <= (CURRENT_DATE + '30 days'::interval)) AND (mj.is_active = true))) AS due_maintenance_jobs,
    ( SELECT count(*) AS count
           FROM public.work_orders wo
          WHERE ((wo.vessel_id = v.id) AND ((wo.status)::text = ANY ((ARRAY['Open'::character varying, 'In Progress'::character varying])::text[])) AND (wo.is_deleted = false))) AS open_work_orders,
    ( SELECT count(*) AS count
           FROM public.vessel_crew_assignments vca
          WHERE ((vca.vessel_id = v.id) AND ((vca.status)::text = 'Onboard'::text) AND (vca.is_deleted = false))) AS crew_onboard,
    ( SELECT vpd.average_speed
           FROM public.vessel_performance_daily vpd
          WHERE (vpd.vessel_id = v.id)
          ORDER BY vpd.report_date DESC
         LIMIT 1) AS current_speed,
    ( SELECT vpd.next_port
           FROM public.vessel_performance_daily vpd
          WHERE (vpd.vessel_id = v.id)
          ORDER BY vpd.report_date DESC
         LIMIT 1) AS next_port,
    v.is_active,
    v.created_date,
    v.modified_date
   FROM (((public.vessels v
     LEFT JOIN public.vessel_types vt ON ((v.vessel_type_id = vt.id)))
     LEFT JOIN public.vessel_statuses vs ON ((v.vessel_status_id = vs.id)))
     LEFT JOIN public.flag_states fs ON ((v.flag_state_id = fs.id)))
  WHERE (v.is_deleted = false);


ALTER VIEW public.v_vessel_dashboard_summary OWNER TO dbadmin;

--
-- Name: VIEW v_vessel_dashboard_summary; Type: COMMENT; Schema: public; Owner: dbadmin
--

COMMENT ON VIEW public.v_vessel_dashboard_summary IS 'Summary view for vessel dashboard displaying key metrics and status information';


--
-- Name: vessel_alerts; Type: TABLE; Schema: public; Owner: zekigalip
--

CREATE TABLE public.vessel_alerts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    alert_type character varying(50) NOT NULL,
    severity character varying(20) NOT NULL,
    status character varying(20) DEFAULT 'Open'::character varying NOT NULL,
    title character varying(500) NOT NULL,
    description text,
    detected_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    acknowledged_date timestamp without time zone,
    resolved_date timestamp without time zone,
    acknowledged_by uuid,
    resolved_by uuid,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    CONSTRAINT vessel_alerts_severity_check CHECK (((severity)::text = ANY ((ARRAY['Critical'::character varying, 'Warning'::character varying, 'Info'::character varying])::text[]))),
    CONSTRAINT vessel_alerts_status_check CHECK (((status)::text = ANY ((ARRAY['Open'::character varying, 'Acknowledged'::character varying, 'Resolved'::character varying, 'Closed'::character varying])::text[])))
);


ALTER TABLE public.vessel_alerts OWNER TO zekigalip;

--
-- Name: vessel_classes; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_classes (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    full_name character varying(200),
    country character varying(100),
    website character varying(200),
    is_iacs_member boolean DEFAULT false,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.vessel_classes OWNER TO dbadmin;

--
-- Name: vessel_positions; Type: TABLE; Schema: public; Owner: zekigalip
--

CREATE TABLE public.vessel_positions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    latitude numeric(10,8) NOT NULL,
    longitude numeric(11,8) NOT NULL,
    heading integer,
    speed_knots numeric(5,2),
    draught numeric(5,2),
    destination character varying(200),
    eta timestamp without time zone,
    navigation_status character varying(50),
    current_location character varying(256),
    next_port character varying(256),
    fuel_level numeric(5,2),
    last_updated timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    position_source character varying(50),
    is_active boolean DEFAULT true
);


ALTER TABLE public.vessel_positions OWNER TO zekigalip;

--
-- Name: vessel_voyages; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.vessel_voyages (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vessel_id uuid NOT NULL,
    voyage_number character varying(50) NOT NULL,
    voyage_type character varying(50),
    departure_port character varying(200) NOT NULL,
    departure_date timestamp without time zone NOT NULL,
    arrival_port character varying(200) NOT NULL,
    eta timestamp without time zone,
    ata timestamp without time zone,
    distance_miles numeric(10,2),
    cargo_description text,
    cargo_quantity numeric(15,2),
    cargo_unit character varying(50),
    charter_party_ref character varying(100),
    status character varying(50) DEFAULT 'Planned'::character varying,
    remarks text,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid,
    deleted_date timestamp without time zone,
    deleted_by uuid,
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.vessel_voyages OWNER TO dbadmin;

--
-- Name: voyage_port_calls; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.voyage_port_calls (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    voyage_id uuid NOT NULL,
    port_name character varying(200) NOT NULL,
    port_country character varying(100),
    port_type character varying(50),
    eta timestamp without time zone,
    etb timestamp without time zone,
    etd timestamp without time zone,
    ata timestamp without time zone,
    atb timestamp without time zone,
    atd timestamp without time zone,
    berth_name character varying(100),
    agent_name character varying(200),
    agent_contact character varying(500),
    port_activities text,
    port_sequence integer,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.voyage_port_calls OWNER TO dbadmin;

--
-- Name: voyage_statuses; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.voyage_statuses (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    color character varying(7),
    is_final boolean DEFAULT false,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.voyage_statuses OWNER TO dbadmin;

--
-- Name: vw_user_themes; Type: VIEW; Schema: public; Owner: dbadmin
--

CREATE VIEW public.vw_user_themes AS
 SELECT u.id AS user_id,
    u.username,
    u.email,
    COALESCE(utp.theme_name, 'ocean-blue'::character varying) AS theme_name,
    COALESCE(utp.is_dark_mode, false) AS is_dark_mode,
    COALESCE(utp.custom_settings, '{}'::jsonb) AS custom_settings,
    COALESCE(utp.modified_date, u.created_date) AS theme_modified_date
   FROM (public.users u
     LEFT JOIN public.user_theme_preferences utp ON ((u.id = utp.user_id)))
  WHERE (u.is_deleted = false);


ALTER VIEW public.vw_user_themes OWNER TO dbadmin;

--
-- Name: widget_interaction_logs; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.widget_interaction_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    widget_id uuid,
    dashboard_widget_id uuid,
    interaction_type character varying(50),
    interaction_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    interaction_data jsonb,
    response_time_ms integer
);


ALTER TABLE public.widget_interaction_logs OWNER TO dbadmin;

--
-- Name: widget_permissions; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.widget_permissions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    widget_id uuid,
    user_id uuid,
    role_name character varying(100),
    can_view boolean DEFAULT true,
    can_use boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    CONSTRAINT chk_widget_permission_target CHECK ((((user_id IS NOT NULL) AND (role_name IS NULL)) OR ((user_id IS NULL) AND (role_name IS NOT NULL))))
);


ALTER TABLE public.widget_permissions OWNER TO dbadmin;

--
-- Name: widget_types; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.widget_types (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    code character varying(50) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    icon character varying(50),
    component_name character varying(100),
    min_width integer DEFAULT 2,
    min_height integer DEFAULT 2,
    max_width integer DEFAULT 12,
    max_height integer DEFAULT 8,
    default_width integer DEFAULT 4,
    default_height integer DEFAULT 3,
    supports_resize boolean DEFAULT true,
    supports_refresh boolean DEFAULT true,
    supports_export boolean DEFAULT true,
    supports_fullscreen boolean DEFAULT true,
    config_schema jsonb,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.widget_types OWNER TO dbadmin;

--
-- Name: widgets; Type: TABLE; Schema: public; Owner: dbadmin
--

CREATE TABLE public.widgets (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    widget_type_id uuid,
    code character varying(100) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    icon character varying(50),
    category character varying(100),
    subcategory character varying(100),
    tags text[],
    data_source character varying(200),
    data_query text,
    data_refresh_interval integer DEFAULT 300,
    default_title character varying(200),
    show_title boolean DEFAULT true,
    show_border boolean DEFAULT true,
    show_shadow boolean DEFAULT true,
    required_permission character varying(200),
    required_role character varying(100),
    default_config jsonb,
    is_active boolean DEFAULT true,
    is_featured boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by uuid,
    modified_date timestamp without time zone,
    modified_by uuid
);


ALTER TABLE public.widgets OWNER TO dbadmin;

--
-- Name: menus id; Type: DEFAULT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.menus ALTER COLUMN id SET DEFAULT nextval('public.menus_id_seq'::regclass);


--
-- Name: certificate_types certificate_types_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.certificate_types
    ADD CONSTRAINT certificate_types_code_key UNIQUE (code);


--
-- Name: certificate_types certificate_types_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.certificate_types
    ADD CONSTRAINT certificate_types_pkey PRIMARY KEY (id);


--
-- Name: crew_departments crew_departments_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.crew_departments
    ADD CONSTRAINT crew_departments_code_key UNIQUE (code);


--
-- Name: crew_departments crew_departments_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.crew_departments
    ADD CONSTRAINT crew_departments_pkey PRIMARY KEY (id);


--
-- Name: crew_ranks crew_ranks_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.crew_ranks
    ADD CONSTRAINT crew_ranks_code_key UNIQUE (code);


--
-- Name: crew_ranks crew_ranks_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.crew_ranks
    ADD CONSTRAINT crew_ranks_pkey PRIMARY KEY (id);


--
-- Name: dashboard_access_logs dashboard_access_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_access_logs
    ADD CONSTRAINT dashboard_access_logs_pkey PRIMARY KEY (id);


--
-- Name: dashboard_definitions dashboard_definitions_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_definitions
    ADD CONSTRAINT dashboard_definitions_code_key UNIQUE (code);


--
-- Name: dashboard_definitions dashboard_definitions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_definitions
    ADD CONSTRAINT dashboard_definitions_pkey PRIMARY KEY (id);


--
-- Name: dashboard_permissions dashboard_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_permissions
    ADD CONSTRAINT dashboard_permissions_pkey PRIMARY KEY (id);


--
-- Name: dashboard_shares dashboard_shares_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_shares
    ADD CONSTRAINT dashboard_shares_pkey PRIMARY KEY (id);


--
-- Name: dashboard_types dashboard_types_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_types
    ADD CONSTRAINT dashboard_types_code_key UNIQUE (code);


--
-- Name: dashboard_types dashboard_types_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_types
    ADD CONSTRAINT dashboard_types_pkey PRIMARY KEY (id);


--
-- Name: dashboard_widgets dashboard_widgets_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_widgets
    ADD CONSTRAINT dashboard_widgets_pkey PRIMARY KEY (id);


--
-- Name: engine_types engine_types_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.engine_types
    ADD CONSTRAINT engine_types_code_key UNIQUE (code);


--
-- Name: engine_types engine_types_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.engine_types
    ADD CONSTRAINT engine_types_pkey PRIMARY KEY (id);


--
-- Name: flag_states flag_states_country_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.flag_states
    ADD CONSTRAINT flag_states_country_code_key UNIQUE (country_code);


--
-- Name: flag_states flag_states_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.flag_states
    ADD CONSTRAINT flag_states_pkey PRIMARY KEY (id);


--
-- Name: inspection_deficiencies inspection_deficiencies_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.inspection_deficiencies
    ADD CONSTRAINT inspection_deficiencies_pkey PRIMARY KEY (id);


--
-- Name: inspection_types inspection_types_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.inspection_types
    ADD CONSTRAINT inspection_types_code_key UNIQUE (code);


--
-- Name: inspection_types inspection_types_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.inspection_types
    ADD CONSTRAINT inspection_types_pkey PRIMARY KEY (id);


--
-- Name: kpi_alert_rules kpi_alert_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_alert_rules
    ADD CONSTRAINT kpi_alert_rules_pkey PRIMARY KEY (id);


--
-- Name: kpi_alerts kpi_alerts_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_alerts
    ADD CONSTRAINT kpi_alerts_pkey PRIMARY KEY (id);


--
-- Name: kpi_calculation_logs kpi_calculation_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_calculation_logs
    ADD CONSTRAINT kpi_calculation_logs_pkey PRIMARY KEY (id);


--
-- Name: kpi_calculation_rules kpi_calculation_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_calculation_rules
    ADD CONSTRAINT kpi_calculation_rules_pkey PRIMARY KEY (id);


--
-- Name: kpi_categories kpi_categories_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_categories
    ADD CONSTRAINT kpi_categories_code_key UNIQUE (code);


--
-- Name: kpi_categories kpi_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_categories
    ADD CONSTRAINT kpi_categories_pkey PRIMARY KEY (id);


--
-- Name: kpi_definitions kpi_definitions_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_definitions
    ADD CONSTRAINT kpi_definitions_code_key UNIQUE (code);


--
-- Name: kpi_definitions kpi_definitions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_definitions
    ADD CONSTRAINT kpi_definitions_pkey PRIMARY KEY (id);


--
-- Name: kpi_targets kpi_targets_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_targets
    ADD CONSTRAINT kpi_targets_pkey PRIMARY KEY (id);


--
-- Name: kpi_values kpi_values_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_values
    ADD CONSTRAINT kpi_values_pkey PRIMARY KEY (id);


--
-- Name: kpi_widgets kpi_widgets_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_widgets
    ADD CONSTRAINT kpi_widgets_pkey PRIMARY KEY (id);


--
-- Name: language_texts language_texts_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.language_texts
    ADD CONSTRAINT language_texts_pkey PRIMARY KEY (id);


--
-- Name: languages languages_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.languages
    ADD CONSTRAINT languages_code_key UNIQUE (code);


--
-- Name: languages languages_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.languages
    ADD CONSTRAINT languages_pkey PRIMARY KEY (id);


--
-- Name: maintenance_categories maintenance_categories_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_categories
    ADD CONSTRAINT maintenance_categories_code_key UNIQUE (code);


--
-- Name: maintenance_categories maintenance_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_categories
    ADD CONSTRAINT maintenance_categories_pkey PRIMARY KEY (id);


--
-- Name: maintenance_jobs maintenance_jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_jobs
    ADD CONSTRAINT maintenance_jobs_pkey PRIMARY KEY (id);


--
-- Name: menu_roles menu_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.menu_roles
    ADD CONSTRAINT menu_roles_pkey PRIMARY KEY (menu_id, role_id);


--
-- Name: menus menus_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.menus
    ADD CONSTRAINT menus_pkey PRIMARY KEY (id);


--
-- Name: roles roles_name_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_name_key UNIQUE (name);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: user_api_keys uk_api_key_hash; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT uk_api_key_hash UNIQUE (api_key_hash);


--
-- Name: dashboard_permissions uk_dashboard_permission; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_permissions
    ADD CONSTRAINT uk_dashboard_permission UNIQUE (dashboard_definition_id, user_id, role_name);


--
-- Name: dashboard_widgets uk_dashboard_widget_position; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_widgets
    ADD CONSTRAINT uk_dashboard_widget_position UNIQUE (dashboard_definition_id, grid_x, grid_y);


--
-- Name: kpi_targets uk_kpi_target; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_targets
    ADD CONSTRAINT uk_kpi_target UNIQUE (kpi_definition_id, vessel_id, year, month);


--
-- Name: kpi_values uk_kpi_value; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_values
    ADD CONSTRAINT uk_kpi_value UNIQUE (kpi_definition_id, vessel_id, period_type, period_start);


--
-- Name: language_texts uk_language_text_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.language_texts
    ADD CONSTRAINT uk_language_text_key UNIQUE (language_id, text_key);


--
-- Name: maintenance_jobs uk_maintenance_job; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_jobs
    ADD CONSTRAINT uk_maintenance_job UNIQUE (vessel_id, job_code);


--
-- Name: user_company_access uk_user_company; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_company_access
    ADD CONSTRAINT uk_user_company UNIQUE (user_id, company_id);


--
-- Name: user_dashboards uk_user_dashboard; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_dashboards
    ADD CONSTRAINT uk_user_dashboard UNIQUE (user_id, dashboard_definition_id);


--
-- Name: user_compliance_documents uk_user_doc_number; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_compliance_documents
    ADD CONSTRAINT uk_user_doc_number UNIQUE (user_id, document_type, document_number);


--
-- Name: user_group_members uk_user_group; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_group_members
    ADD CONSTRAINT uk_user_group UNIQUE (user_id, group_id);


--
-- Name: user_roles uk_user_role_scope; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT uk_user_role_scope UNIQUE (user_id, role_id, company_id, vessel_id);


--
-- Name: user_vessel_connectivity uk_user_vessel_conn; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_connectivity
    ADD CONSTRAINT uk_user_vessel_conn UNIQUE (user_id, vessel_id, assignment_id);


--
-- Name: user_widget_preferences uk_user_widget_pref; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_widget_preferences
    ADD CONSTRAINT uk_user_widget_pref UNIQUE (user_id, dashboard_widget_id);


--
-- Name: vessel_certificates uk_vessel_certificate; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_certificates
    ADD CONSTRAINT uk_vessel_certificate UNIQUE (vessel_id, certificate_type_id, issue_date);


--
-- Name: vessel_crew_assignments uk_vessel_crew_active; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_crew_assignments
    ADD CONSTRAINT uk_vessel_crew_active UNIQUE (vessel_id, user_id, sign_on_date);


--
-- Name: vessel_equipment uk_vessel_equipment; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_equipment
    ADD CONSTRAINT uk_vessel_equipment UNIQUE (vessel_id, equipment_code);


--
-- Name: vessel_performance_daily uk_vessel_performance_daily; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_performance_daily
    ADD CONSTRAINT uk_vessel_performance_daily UNIQUE (vessel_id, report_date);


--
-- Name: widget_permissions uk_widget_permission; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_permissions
    ADD CONSTRAINT uk_widget_permission UNIQUE (widget_id, user_id, role_name);


--
-- Name: user_activity_logs user_activity_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_activity_logs
    ADD CONSTRAINT user_activity_logs_pkey PRIMARY KEY (id);


--
-- Name: user_api_keys user_api_keys_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT user_api_keys_pkey PRIMARY KEY (id);


--
-- Name: user_company_access user_company_access_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_company_access
    ADD CONSTRAINT user_company_access_pkey PRIMARY KEY (id);


--
-- Name: user_compliance_documents user_compliance_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_compliance_documents
    ADD CONSTRAINT user_compliance_documents_pkey PRIMARY KEY (id);


--
-- Name: user_dashboards user_dashboards_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_dashboards
    ADD CONSTRAINT user_dashboards_pkey PRIMARY KEY (id);


--
-- Name: user_digital_identity user_digital_identity_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_digital_identity
    ADD CONSTRAINT user_digital_identity_pkey PRIMARY KEY (id);


--
-- Name: user_digital_identity user_digital_identity_user_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_digital_identity
    ADD CONSTRAINT user_digital_identity_user_id_key UNIQUE (user_id);


--
-- Name: user_emergency_contacts user_emergency_contacts_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_emergency_contacts
    ADD CONSTRAINT user_emergency_contacts_pkey PRIMARY KEY (id);


--
-- Name: user_group_members user_group_members_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_group_members
    ADD CONSTRAINT user_group_members_pkey PRIMARY KEY (id);


--
-- Name: user_groups user_groups_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_groups
    ADD CONSTRAINT user_groups_code_key UNIQUE (code);


--
-- Name: user_groups user_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_groups
    ADD CONSTRAINT user_groups_pkey PRIMARY KEY (id);


--
-- Name: user_login_attempts user_login_attempts_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_login_attempts
    ADD CONSTRAINT user_login_attempts_pkey PRIMARY KEY (id);


--
-- Name: user_maritime_profiles user_maritime_profiles_imo_number_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_maritime_profiles
    ADD CONSTRAINT user_maritime_profiles_imo_number_key UNIQUE (imo_number);


--
-- Name: user_maritime_profiles user_maritime_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_maritime_profiles
    ADD CONSTRAINT user_maritime_profiles_pkey PRIMARY KEY (id);


--
-- Name: user_maritime_profiles user_maritime_profiles_user_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_maritime_profiles
    ADD CONSTRAINT user_maritime_profiles_user_id_key UNIQUE (user_id);


--
-- Name: user_medical_records user_medical_records_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_medical_records
    ADD CONSTRAINT user_medical_records_pkey PRIMARY KEY (id);


--
-- Name: user_notifications user_notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_notifications
    ADD CONSTRAINT user_notifications_pkey PRIMARY KEY (id);


--
-- Name: user_password_history user_password_history_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_password_history
    ADD CONSTRAINT user_password_history_pkey PRIMARY KEY (id);


--
-- Name: user_password_reset_tokens user_password_reset_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_password_reset_tokens
    ADD CONSTRAINT user_password_reset_tokens_pkey PRIMARY KEY (id);


--
-- Name: user_payroll_settings user_payroll_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_payroll_settings
    ADD CONSTRAINT user_payroll_settings_pkey PRIMARY KEY (id);


--
-- Name: user_payroll_settings user_payroll_settings_user_id_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_payroll_settings
    ADD CONSTRAINT user_payroll_settings_user_id_key UNIQUE (user_id);


--
-- Name: user_permissions user_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_pkey PRIMARY KEY (id);


--
-- Name: user_preferences user_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_preferences
    ADD CONSTRAINT user_preferences_pkey PRIMARY KEY (user_id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: user_rotation_plans user_rotation_plans_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_rotation_plans
    ADD CONSTRAINT user_rotation_plans_pkey PRIMARY KEY (id);


--
-- Name: user_sessions user_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (id);


--
-- Name: user_sessions user_sessions_refresh_token_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_refresh_token_key UNIQUE (refresh_token);


--
-- Name: user_sessions user_sessions_session_token_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_session_token_key UNIQUE (session_token);


--
-- Name: user_theme_preferences user_theme_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_theme_preferences
    ADD CONSTRAINT user_theme_preferences_pkey PRIMARY KEY (user_id);


--
-- Name: user_training_records user_training_records_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_training_records
    ADD CONSTRAINT user_training_records_pkey PRIMARY KEY (id);


--
-- Name: user_vessel_assignments user_vessel_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_assignments
    ADD CONSTRAINT user_vessel_assignments_pkey PRIMARY KEY (id);


--
-- Name: user_vessel_connectivity user_vessel_connectivity_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_connectivity
    ADD CONSTRAINT user_vessel_connectivity_pkey PRIMARY KEY (id);


--
-- Name: user_widget_preferences user_widget_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_widget_preferences
    ADD CONSTRAINT user_widget_preferences_pkey PRIMARY KEY (id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_employee_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_employee_code_key UNIQUE (employee_code);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: vessel_alerts vessel_alerts_pkey; Type: CONSTRAINT; Schema: public; Owner: zekigalip
--

ALTER TABLE ONLY public.vessel_alerts
    ADD CONSTRAINT vessel_alerts_pkey PRIMARY KEY (id);


--
-- Name: vessel_certificates vessel_certificates_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_certificates
    ADD CONSTRAINT vessel_certificates_pkey PRIMARY KEY (id);


--
-- Name: vessel_classes vessel_classes_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_classes
    ADD CONSTRAINT vessel_classes_code_key UNIQUE (code);


--
-- Name: vessel_classes vessel_classes_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_classes
    ADD CONSTRAINT vessel_classes_pkey PRIMARY KEY (id);


--
-- Name: vessel_crew_assignments vessel_crew_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_crew_assignments
    ADD CONSTRAINT vessel_crew_assignments_pkey PRIMARY KEY (id);


--
-- Name: vessel_equipment vessel_equipment_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_equipment
    ADD CONSTRAINT vessel_equipment_pkey PRIMARY KEY (id);


--
-- Name: vessel_inspections vessel_inspections_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_inspections
    ADD CONSTRAINT vessel_inspections_pkey PRIMARY KEY (id);


--
-- Name: vessel_performance_daily vessel_performance_daily_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_performance_daily
    ADD CONSTRAINT vessel_performance_daily_pkey PRIMARY KEY (id);


--
-- Name: vessel_positions vessel_positions_pkey; Type: CONSTRAINT; Schema: public; Owner: zekigalip
--

ALTER TABLE ONLY public.vessel_positions
    ADD CONSTRAINT vessel_positions_pkey PRIMARY KEY (id);


--
-- Name: vessel_statuses vessel_statuses_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_statuses
    ADD CONSTRAINT vessel_statuses_code_key UNIQUE (code);


--
-- Name: vessel_statuses vessel_statuses_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_statuses
    ADD CONSTRAINT vessel_statuses_pkey PRIMARY KEY (id);


--
-- Name: vessel_types vessel_types_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_types
    ADD CONSTRAINT vessel_types_code_key UNIQUE (code);


--
-- Name: vessel_types vessel_types_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_types
    ADD CONSTRAINT vessel_types_pkey PRIMARY KEY (id);


--
-- Name: vessel_voyages vessel_voyages_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_voyages
    ADD CONSTRAINT vessel_voyages_pkey PRIMARY KEY (id);


--
-- Name: vessels vessels_imo_number_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_imo_number_key UNIQUE (imo_number);


--
-- Name: vessels vessels_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_pkey PRIMARY KEY (id);


--
-- Name: vessels vessels_vessel_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_vessel_code_key UNIQUE (vessel_code);


--
-- Name: voyage_port_calls voyage_port_calls_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.voyage_port_calls
    ADD CONSTRAINT voyage_port_calls_pkey PRIMARY KEY (id);


--
-- Name: voyage_statuses voyage_statuses_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.voyage_statuses
    ADD CONSTRAINT voyage_statuses_code_key UNIQUE (code);


--
-- Name: voyage_statuses voyage_statuses_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.voyage_statuses
    ADD CONSTRAINT voyage_statuses_pkey PRIMARY KEY (id);


--
-- Name: widget_interaction_logs widget_interaction_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_interaction_logs
    ADD CONSTRAINT widget_interaction_logs_pkey PRIMARY KEY (id);


--
-- Name: widget_permissions widget_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_permissions
    ADD CONSTRAINT widget_permissions_pkey PRIMARY KEY (id);


--
-- Name: widget_types widget_types_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_types
    ADD CONSTRAINT widget_types_code_key UNIQUE (code);


--
-- Name: widget_types widget_types_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_types
    ADD CONSTRAINT widget_types_pkey PRIMARY KEY (id);


--
-- Name: widgets widgets_code_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widgets
    ADD CONSTRAINT widgets_code_key UNIQUE (code);


--
-- Name: widgets widgets_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widgets
    ADD CONSTRAINT widgets_pkey PRIMARY KEY (id);


--
-- Name: work_orders work_orders_pkey; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.work_orders
    ADD CONSTRAINT work_orders_pkey PRIMARY KEY (id);


--
-- Name: work_orders work_orders_work_order_number_key; Type: CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.work_orders
    ADD CONSTRAINT work_orders_work_order_number_key UNIQUE (work_order_number);


--
-- Name: UC_Version; Type: INDEX; Schema: public; Owner: sms_user
--

CREATE UNIQUE INDEX "UC_Version" ON public."VersionInfo" USING btree ("Version");


--
-- Name: idx_activity_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_activity_date ON public.user_activity_logs USING btree (activity_date DESC);


--
-- Name: idx_activity_entity; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_activity_entity ON public.user_activity_logs USING btree (entity_type, entity_id);


--
-- Name: idx_activity_ip; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_activity_ip ON public.user_activity_logs USING btree (ip_address);


--
-- Name: idx_activity_type_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_activity_type_date ON public.user_activity_logs USING btree (activity_type, activity_date DESC);


--
-- Name: idx_activity_user_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_activity_user_date ON public.user_activity_logs USING btree (user_id, activity_date DESC);


--
-- Name: idx_activity_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_activity_vessel ON public.user_activity_logs USING btree (vessel_id) WHERE (vessel_id IS NOT NULL);


--
-- Name: idx_activity_year_month; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_activity_year_month ON public.user_activity_logs USING btree (activity_year, activity_month);


--
-- Name: idx_api_keys_expire; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_api_keys_expire ON public.user_api_keys USING btree (expire_date);


--
-- Name: idx_api_keys_hash; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_api_keys_hash ON public.user_api_keys USING btree (api_key_hash) WHERE (is_active = true);


--
-- Name: idx_api_keys_prefix; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_api_keys_prefix ON public.user_api_keys USING btree (key_prefix);


--
-- Name: idx_api_keys_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_api_keys_user ON public.user_api_keys USING btree (user_id) WHERE (is_active = true);


--
-- Name: idx_company_access_company; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_company_access_company ON public.user_company_access USING btree (company_id) WHERE (is_active = true);


--
-- Name: idx_company_access_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_company_access_user ON public.user_company_access USING btree (user_id) WHERE (is_active = true);


--
-- Name: idx_company_access_validity; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_company_access_validity ON public.user_company_access USING btree (valid_from, valid_until);


--
-- Name: idx_compliance_expiry; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_compliance_expiry ON public.user_compliance_documents USING btree (expiry_date);


--
-- Name: idx_compliance_mandatory; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_compliance_mandatory ON public.user_compliance_documents USING btree (is_mandatory, expiry_date);


--
-- Name: idx_compliance_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_compliance_status ON public.user_compliance_documents USING btree (verification_status);


--
-- Name: idx_compliance_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_compliance_type ON public.user_compliance_documents USING btree (document_type);


--
-- Name: idx_compliance_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_compliance_user ON public.user_compliance_documents USING btree (user_id) WHERE (is_deleted = false);


--
-- Name: idx_dashboard_access_logs_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_access_logs_date ON public.dashboard_access_logs USING btree (access_date);


--
-- Name: idx_dashboard_access_logs_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_access_logs_user ON public.dashboard_access_logs USING btree (user_id);


--
-- Name: idx_dashboard_definitions_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_definitions_active ON public.dashboard_definitions USING btree (is_active) WHERE (is_deleted = false);


--
-- Name: idx_dashboard_definitions_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_definitions_role ON public.dashboard_definitions USING btree (target_role) WHERE (is_deleted = false);


--
-- Name: idx_dashboard_definitions_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_definitions_type ON public.dashboard_definitions USING btree (dashboard_type_id) WHERE (is_deleted = false);


--
-- Name: idx_dashboard_permissions_dashboard; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_permissions_dashboard ON public.dashboard_permissions USING btree (dashboard_definition_id);


--
-- Name: idx_dashboard_permissions_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_permissions_role ON public.dashboard_permissions USING btree (role_name) WHERE (role_name IS NOT NULL);


--
-- Name: idx_dashboard_permissions_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_permissions_user ON public.dashboard_permissions USING btree (user_id) WHERE (user_id IS NOT NULL);


--
-- Name: idx_dashboard_widgets_dashboard; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_widgets_dashboard ON public.dashboard_widgets USING btree (dashboard_definition_id);


--
-- Name: idx_dashboard_widgets_position; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_widgets_position ON public.dashboard_widgets USING btree (dashboard_definition_id, grid_x, grid_y);


--
-- Name: idx_dashboard_widgets_widget; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_dashboard_widgets_widget ON public.dashboard_widgets USING btree (widget_id);


--
-- Name: idx_digital_identity_card; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_digital_identity_card ON public.user_digital_identity USING btree (smart_card_uid);


--
-- Name: idx_digital_identity_cert; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_digital_identity_cert ON public.user_digital_identity USING btree (digital_certificate_serial);


--
-- Name: idx_digital_identity_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_digital_identity_user ON public.user_digital_identity USING btree (user_id);


--
-- Name: idx_emergency_primary; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_emergency_primary ON public.user_emergency_contacts USING btree (user_id, is_primary);


--
-- Name: idx_emergency_priority; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_emergency_priority ON public.user_emergency_contacts USING btree (user_id, priority_order);


--
-- Name: idx_emergency_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_emergency_user ON public.user_emergency_contacts USING btree (user_id);


--
-- Name: idx_group_members_group; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_group_members_group ON public.user_group_members USING btree (group_id) WHERE (is_active = true);


--
-- Name: idx_group_members_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_group_members_user ON public.user_group_members USING btree (user_id) WHERE (is_active = true);


--
-- Name: idx_kpi_alerts_created; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_alerts_created ON public.kpi_alerts USING btree (created_date);


--
-- Name: idx_kpi_alerts_definition; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_alerts_definition ON public.kpi_alerts USING btree (kpi_definition_id);


--
-- Name: idx_kpi_alerts_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_alerts_status ON public.kpi_alerts USING btree (status, created_date);


--
-- Name: idx_kpi_alerts_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_alerts_vessel ON public.kpi_alerts USING btree (vessel_id) WHERE (vessel_id IS NOT NULL);


--
-- Name: idx_kpi_categories_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_categories_active ON public.kpi_categories USING btree (is_active);


--
-- Name: idx_kpi_categories_parent; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_categories_parent ON public.kpi_categories USING btree (parent_category_id);


--
-- Name: idx_kpi_definitions_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_definitions_active ON public.kpi_definitions USING btree (is_active);


--
-- Name: idx_kpi_definitions_category; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_definitions_category ON public.kpi_definitions USING btree (category_id);


--
-- Name: idx_kpi_definitions_featured; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_definitions_featured ON public.kpi_definitions USING btree (is_featured) WHERE (is_active = true);


--
-- Name: idx_kpi_targets_definition; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_targets_definition ON public.kpi_targets USING btree (kpi_definition_id);


--
-- Name: idx_kpi_targets_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_targets_vessel ON public.kpi_targets USING btree (vessel_id) WHERE (vessel_id IS NOT NULL);


--
-- Name: idx_kpi_targets_year; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_targets_year ON public.kpi_targets USING btree (year);


--
-- Name: idx_kpi_values_definition; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_values_definition ON public.kpi_values USING btree (kpi_definition_id);


--
-- Name: idx_kpi_values_period; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_values_period ON public.kpi_values USING btree (period_start, period_end);


--
-- Name: idx_kpi_values_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_values_status ON public.kpi_values USING btree (status);


--
-- Name: idx_kpi_values_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_kpi_values_vessel ON public.kpi_values USING btree (vessel_id) WHERE (vessel_id IS NOT NULL);


--
-- Name: idx_language_texts_key; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_language_texts_key ON public.language_texts USING btree (text_key);


--
-- Name: idx_language_texts_language; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_language_texts_language ON public.language_texts USING btree (language_id);


--
-- Name: idx_language_texts_module; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_language_texts_module ON public.language_texts USING btree (module);


--
-- Name: idx_login_attempts_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_login_attempts_date ON public.user_login_attempts USING btree (attempt_date DESC);


--
-- Name: idx_login_attempts_ip; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_login_attempts_ip ON public.user_login_attempts USING btree (ip_address);


--
-- Name: idx_login_attempts_success; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_login_attempts_success ON public.user_login_attempts USING btree (is_successful);


--
-- Name: idx_login_attempts_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_login_attempts_user ON public.user_login_attempts USING btree (user_id);


--
-- Name: idx_login_attempts_username; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_login_attempts_username ON public.user_login_attempts USING btree (username);


--
-- Name: idx_maintenance_jobs_due; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_maintenance_jobs_due ON public.maintenance_jobs USING btree (next_due_date) WHERE (is_active = true);


--
-- Name: idx_maintenance_jobs_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_maintenance_jobs_vessel ON public.maintenance_jobs USING btree (vessel_id);


--
-- Name: idx_maritime_availability; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_maritime_availability ON public.user_maritime_profiles USING btree (availability_status);


--
-- Name: idx_maritime_imo; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_maritime_imo ON public.user_maritime_profiles USING btree (imo_number);


--
-- Name: idx_maritime_rank; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_maritime_rank ON public.user_maritime_profiles USING btree (current_rank_id);


--
-- Name: idx_medical_expiry; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_medical_expiry ON public.user_medical_records USING btree (expiry_date);


--
-- Name: idx_medical_result; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_medical_result ON public.user_medical_records USING btree (result);


--
-- Name: idx_medical_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_medical_type ON public.user_medical_records USING btree (medical_type);


--
-- Name: idx_medical_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_medical_user ON public.user_medical_records USING btree (user_id);


--
-- Name: idx_mv_dashboard_kpi_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_mv_dashboard_kpi_vessel ON public.mv_dashboard_kpi_hourly USING btree (vessel_id);


--
-- Name: idx_notifications_category; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_notifications_category ON public.user_notifications USING btree (category);


--
-- Name: idx_notifications_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_notifications_date ON public.user_notifications USING btree (created_date DESC);


--
-- Name: idx_notifications_expire; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_notifications_expire ON public.user_notifications USING btree (expire_date);


--
-- Name: idx_notifications_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_notifications_type ON public.user_notifications USING btree (notification_type);


--
-- Name: idx_notifications_unread; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_notifications_unread ON public.user_notifications USING btree (user_id, is_read) WHERE (is_deleted = false);


--
-- Name: idx_notifications_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_notifications_user ON public.user_notifications USING btree (user_id) WHERE (is_deleted = false);


--
-- Name: idx_password_hist_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_password_hist_date ON public.user_password_history USING btree (changed_date DESC);


--
-- Name: idx_password_hist_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_password_hist_user ON public.user_password_history USING btree (user_id);


--
-- Name: idx_password_reset_expires_at; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_password_reset_expires_at ON public.user_password_reset_tokens USING btree (expires_at);


--
-- Name: idx_password_reset_token_hash; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_password_reset_token_hash ON public.user_password_reset_tokens USING btree (token_hash);


--
-- Name: idx_password_reset_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_password_reset_user_id ON public.user_password_reset_tokens USING btree (user_id);


--
-- Name: idx_payroll_currency; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_payroll_currency ON public.user_payroll_settings USING btree (salary_currency);


--
-- Name: idx_payroll_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_payroll_user ON public.user_payroll_settings USING btree (user_id);


--
-- Name: idx_permissions_group; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_permissions_group ON public.user_permissions USING btree (group_id) WHERE (is_granted = true);


--
-- Name: idx_permissions_module; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_permissions_module ON public.user_permissions USING btree (module_code);


--
-- Name: idx_permissions_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_permissions_role ON public.user_permissions USING btree (role_id) WHERE (is_granted = true);


--
-- Name: idx_permissions_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_permissions_user ON public.user_permissions USING btree (user_id) WHERE (is_granted = true);


--
-- Name: idx_permissions_validity; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_permissions_validity ON public.user_permissions USING btree (valid_from, valid_until);


--
-- Name: idx_preferences_language; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_preferences_language ON public.user_preferences USING btree (language);


--
-- Name: idx_preferences_theme; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_preferences_theme ON public.user_preferences USING btree (theme);


--
-- Name: idx_roles_name; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_roles_name ON public.roles USING btree (name) WHERE (is_deleted = false);


--
-- Name: idx_roles_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_roles_type ON public.roles USING btree (role_type) WHERE (is_deleted = false);


--
-- Name: idx_rotation_dates; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_rotation_dates ON public.user_rotation_plans USING btree (planned_on_date, planned_off_date);


--
-- Name: idx_rotation_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_rotation_status ON public.user_rotation_plans USING btree (status);


--
-- Name: idx_rotation_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_rotation_user ON public.user_rotation_plans USING btree (user_id);


--
-- Name: idx_rotation_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_rotation_vessel ON public.user_rotation_plans USING btree (vessel_id);


--
-- Name: idx_sessions_device; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_sessions_device ON public.user_sessions USING btree (device_id);


--
-- Name: idx_sessions_expire; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_sessions_expire ON public.user_sessions USING btree (expire_date);


--
-- Name: idx_sessions_token; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_sessions_token ON public.user_sessions USING btree (session_token) WHERE (is_active = true);


--
-- Name: idx_sessions_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_sessions_user ON public.user_sessions USING btree (user_id) WHERE (is_active = true);


--
-- Name: idx_training_expiry; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_training_expiry ON public.user_training_records USING btree (expiry_date);


--
-- Name: idx_training_refresher; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_training_refresher ON public.user_training_records USING btree (next_refresher_date);


--
-- Name: idx_training_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_training_type ON public.user_training_records USING btree (training_type);


--
-- Name: idx_training_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_training_user ON public.user_training_records USING btree (user_id);


--
-- Name: idx_user_dashboards_definition; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_dashboards_definition ON public.user_dashboards USING btree (dashboard_definition_id);


--
-- Name: idx_user_dashboards_favorite; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_dashboards_favorite ON public.user_dashboards USING btree (user_id, is_favorite) WHERE (is_active = true);


--
-- Name: idx_user_dashboards_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_dashboards_user ON public.user_dashboards USING btree (user_id);


--
-- Name: idx_user_groups_code; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_groups_code ON public.user_groups USING btree (code) WHERE (is_deleted = false);


--
-- Name: idx_user_groups_company; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_groups_company ON public.user_groups USING btree (company_id);


--
-- Name: idx_user_groups_parent; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_groups_parent ON public.user_groups USING btree (parent_group_id);


--
-- Name: idx_user_roles_role; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_roles_role ON public.user_roles USING btree (role_id) WHERE (is_active = true);


--
-- Name: idx_user_roles_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_roles_user ON public.user_roles USING btree (user_id) WHERE (is_active = true);


--
-- Name: idx_user_roles_validity; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_roles_validity ON public.user_roles USING btree (valid_from, valid_until);


--
-- Name: idx_user_theme_preferences_user_id; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_user_theme_preferences_user_id ON public.user_theme_preferences USING btree (user_id);


--
-- Name: idx_users_company; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_company ON public.users USING btree (company_id) WHERE (is_deleted = false);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_email ON public.users USING btree (email) WHERE (is_deleted = false);


--
-- Name: idx_users_employee_code; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_employee_code ON public.users USING btree (employee_code) WHERE (is_deleted = false);


--
-- Name: idx_users_username; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_users_username ON public.users USING btree (username) WHERE (is_deleted = false);


--
-- Name: idx_vessel_assign_dates; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_assign_dates ON public.user_vessel_assignments USING btree (embarkation_date, planned_disembarkation_date);


--
-- Name: idx_vessel_assign_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_assign_status ON public.user_vessel_assignments USING btree (status);


--
-- Name: idx_vessel_assign_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_assign_user ON public.user_vessel_assignments USING btree (user_id);


--
-- Name: idx_vessel_assign_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_assign_vessel ON public.user_vessel_assignments USING btree (vessel_id);


--
-- Name: idx_vessel_certificates_alert; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_certificates_alert ON public.vessel_certificates USING btree (alert_sent, expiry_date) WHERE (is_deleted = false);


--
-- Name: idx_vessel_certificates_expiry; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_certificates_expiry ON public.vessel_certificates USING btree (expiry_date) WHERE (is_deleted = false);


--
-- Name: idx_vessel_certificates_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_certificates_type ON public.vessel_certificates USING btree (certificate_type_id) WHERE (is_deleted = false);


--
-- Name: idx_vessel_certificates_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_certificates_vessel ON public.vessel_certificates USING btree (vessel_id) WHERE (is_deleted = false);


--
-- Name: idx_vessel_conn_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_conn_status ON public.user_vessel_connectivity USING btree (connectivity_status);


--
-- Name: idx_vessel_conn_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_conn_user ON public.user_vessel_connectivity USING btree (user_id);


--
-- Name: idx_vessel_conn_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_conn_vessel ON public.user_vessel_connectivity USING btree (vessel_id);


--
-- Name: idx_vessel_crew_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_crew_active ON public.vessel_crew_assignments USING btree (vessel_id, status) WHERE (is_deleted = false);


--
-- Name: idx_vessel_crew_assignments_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_crew_assignments_active ON public.vessel_crew_assignments USING btree (vessel_id, status, expected_sign_off_date) WHERE (is_deleted = false);


--
-- Name: idx_vessel_crew_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_crew_user ON public.vessel_crew_assignments USING btree (user_id) WHERE (is_deleted = false);


--
-- Name: idx_vessel_crew_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_crew_vessel ON public.vessel_crew_assignments USING btree (vessel_id) WHERE (is_deleted = false);


--
-- Name: idx_vessel_inspections_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_inspections_date ON public.vessel_inspections USING btree (inspection_date) WHERE (is_deleted = false);


--
-- Name: idx_vessel_inspections_result; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_inspections_result ON public.vessel_inspections USING btree (result) WHERE (is_deleted = false);


--
-- Name: idx_vessel_inspections_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_inspections_vessel ON public.vessel_inspections USING btree (vessel_id) WHERE (is_deleted = false);


--
-- Name: idx_vessel_performance_daily_latest; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_performance_daily_latest ON public.vessel_performance_daily USING btree (vessel_id, report_date DESC);


--
-- Name: idx_vessel_performance_vessel_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_performance_vessel_date ON public.vessel_performance_daily USING btree (vessel_id, report_date);


--
-- Name: idx_vessel_voyages_dates; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_voyages_dates ON public.vessel_voyages USING btree (departure_date, eta) WHERE (is_deleted = false);


--
-- Name: idx_vessel_voyages_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_voyages_status ON public.vessel_voyages USING btree (status) WHERE (is_deleted = false);


--
-- Name: idx_vessel_voyages_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessel_voyages_vessel ON public.vessel_voyages USING btree (vessel_id) WHERE (is_deleted = false);


--
-- Name: idx_vessels_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessels_active ON public.vessels USING btree (is_active) WHERE (is_deleted = false);


--
-- Name: idx_vessels_flag; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessels_flag ON public.vessels USING btree (flag_state_id) WHERE (is_deleted = false);


--
-- Name: idx_vessels_imo; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessels_imo ON public.vessels USING btree (imo_number) WHERE (is_deleted = false);


--
-- Name: idx_vessels_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessels_status ON public.vessels USING btree (vessel_status_id) WHERE (is_deleted = false);


--
-- Name: idx_vessels_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_vessels_type ON public.vessels USING btree (vessel_type_id) WHERE (is_deleted = false);


--
-- Name: idx_voyage_port_calls_sequence; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_voyage_port_calls_sequence ON public.voyage_port_calls USING btree (voyage_id, port_sequence);


--
-- Name: idx_voyage_port_calls_voyage; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_voyage_port_calls_voyage ON public.voyage_port_calls USING btree (voyage_id);


--
-- Name: idx_widget_interaction_logs_date; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_widget_interaction_logs_date ON public.widget_interaction_logs USING btree (interaction_date);


--
-- Name: idx_widget_interaction_logs_user; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_widget_interaction_logs_user ON public.widget_interaction_logs USING btree (user_id);


--
-- Name: idx_widgets_active; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_widgets_active ON public.widgets USING btree (is_active);


--
-- Name: idx_widgets_category; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_widgets_category ON public.widgets USING btree (category);


--
-- Name: idx_widgets_tags; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_widgets_tags ON public.widgets USING gin (tags);


--
-- Name: idx_widgets_type; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_widgets_type ON public.widgets USING btree (widget_type_id);


--
-- Name: idx_work_orders_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_work_orders_status ON public.work_orders USING btree (status) WHERE (is_deleted = false);


--
-- Name: idx_work_orders_vessel; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_work_orders_vessel ON public.work_orders USING btree (vessel_id) WHERE (is_deleted = false);


--
-- Name: idx_work_orders_vessel_status; Type: INDEX; Schema: public; Owner: dbadmin
--

CREATE INDEX idx_work_orders_vessel_status ON public.work_orders USING btree (vessel_id, status) WHERE (is_deleted = false);


--
-- Name: users trg_normalize_user_data; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_normalize_user_data BEFORE INSERT OR UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.normalize_user_data();


--
-- Name: roles trg_roles_modified; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_roles_modified BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.update_modified_date();


--
-- Name: users trg_users_modified; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_users_modified BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_modified_date();


--
-- Name: user_vessel_assignments trg_validate_vessel_assignment; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trg_validate_vessel_assignment BEFORE INSERT OR UPDATE ON public.user_vessel_assignments FOR EACH ROW EXECUTE FUNCTION public.validate_vessel_assignment();


--
-- Name: user_theme_preferences trigger_user_theme_preferences_modified_date; Type: TRIGGER; Schema: public; Owner: dbadmin
--

CREATE TRIGGER trigger_user_theme_preferences_modified_date BEFORE UPDATE ON public.user_theme_preferences FOR EACH ROW EXECUTE FUNCTION public.update_user_theme_preferences_modified_date();


--
-- Name: dashboard_definitions dashboard_definitions_dashboard_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_definitions
    ADD CONSTRAINT dashboard_definitions_dashboard_type_id_fkey FOREIGN KEY (dashboard_type_id) REFERENCES public.dashboard_types(id);


--
-- Name: dashboard_permissions dashboard_permissions_dashboard_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_permissions
    ADD CONSTRAINT dashboard_permissions_dashboard_definition_id_fkey FOREIGN KEY (dashboard_definition_id) REFERENCES public.dashboard_definitions(id);


--
-- Name: dashboard_shares dashboard_shares_user_dashboard_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_shares
    ADD CONSTRAINT dashboard_shares_user_dashboard_id_fkey FOREIGN KEY (user_dashboard_id) REFERENCES public.user_dashboards(id);


--
-- Name: dashboard_widgets dashboard_widgets_dashboard_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_widgets
    ADD CONSTRAINT dashboard_widgets_dashboard_definition_id_fkey FOREIGN KEY (dashboard_definition_id) REFERENCES public.dashboard_definitions(id);


--
-- Name: dashboard_widgets dashboard_widgets_widget_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.dashboard_widgets
    ADD CONSTRAINT dashboard_widgets_widget_id_fkey FOREIGN KEY (widget_id) REFERENCES public.widgets(id);


--
-- Name: user_password_reset_tokens fk_password_reset_user; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_password_reset_tokens
    ADD CONSTRAINT fk_password_reset_user FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: inspection_deficiencies inspection_deficiencies_inspection_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.inspection_deficiencies
    ADD CONSTRAINT inspection_deficiencies_inspection_id_fkey FOREIGN KEY (inspection_id) REFERENCES public.vessel_inspections(id);


--
-- Name: kpi_alert_rules kpi_alert_rules_kpi_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_alert_rules
    ADD CONSTRAINT kpi_alert_rules_kpi_definition_id_fkey FOREIGN KEY (kpi_definition_id) REFERENCES public.kpi_definitions(id);


--
-- Name: kpi_alerts kpi_alerts_alert_rule_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_alerts
    ADD CONSTRAINT kpi_alerts_alert_rule_id_fkey FOREIGN KEY (alert_rule_id) REFERENCES public.kpi_alert_rules(id);


--
-- Name: kpi_alerts kpi_alerts_kpi_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_alerts
    ADD CONSTRAINT kpi_alerts_kpi_definition_id_fkey FOREIGN KEY (kpi_definition_id) REFERENCES public.kpi_definitions(id);


--
-- Name: kpi_alerts kpi_alerts_kpi_value_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_alerts
    ADD CONSTRAINT kpi_alerts_kpi_value_id_fkey FOREIGN KEY (kpi_value_id) REFERENCES public.kpi_values(id);


--
-- Name: kpi_calculation_logs kpi_calculation_logs_kpi_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_calculation_logs
    ADD CONSTRAINT kpi_calculation_logs_kpi_definition_id_fkey FOREIGN KEY (kpi_definition_id) REFERENCES public.kpi_definitions(id);


--
-- Name: kpi_calculation_rules kpi_calculation_rules_kpi_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_calculation_rules
    ADD CONSTRAINT kpi_calculation_rules_kpi_definition_id_fkey FOREIGN KEY (kpi_definition_id) REFERENCES public.kpi_definitions(id);


--
-- Name: kpi_categories kpi_categories_parent_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_categories
    ADD CONSTRAINT kpi_categories_parent_category_id_fkey FOREIGN KEY (parent_category_id) REFERENCES public.kpi_categories(id);


--
-- Name: kpi_definitions kpi_definitions_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_definitions
    ADD CONSTRAINT kpi_definitions_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.kpi_categories(id);


--
-- Name: kpi_targets kpi_targets_kpi_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_targets
    ADD CONSTRAINT kpi_targets_kpi_definition_id_fkey FOREIGN KEY (kpi_definition_id) REFERENCES public.kpi_definitions(id);


--
-- Name: kpi_values kpi_values_kpi_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_values
    ADD CONSTRAINT kpi_values_kpi_definition_id_fkey FOREIGN KEY (kpi_definition_id) REFERENCES public.kpi_definitions(id);


--
-- Name: kpi_widgets kpi_widgets_widget_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.kpi_widgets
    ADD CONSTRAINT kpi_widgets_widget_id_fkey FOREIGN KEY (widget_id) REFERENCES public.widgets(id);


--
-- Name: language_texts language_texts_language_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.language_texts
    ADD CONSTRAINT language_texts_language_id_fkey FOREIGN KEY (language_id) REFERENCES public.languages(id);


--
-- Name: maintenance_categories maintenance_categories_parent_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_categories
    ADD CONSTRAINT maintenance_categories_parent_category_id_fkey FOREIGN KEY (parent_category_id) REFERENCES public.maintenance_categories(id);


--
-- Name: maintenance_jobs maintenance_jobs_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_jobs
    ADD CONSTRAINT maintenance_jobs_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.maintenance_categories(id);


--
-- Name: maintenance_jobs maintenance_jobs_equipment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_jobs
    ADD CONSTRAINT maintenance_jobs_equipment_id_fkey FOREIGN KEY (equipment_id) REFERENCES public.vessel_equipment(id);


--
-- Name: maintenance_jobs maintenance_jobs_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.maintenance_jobs
    ADD CONSTRAINT maintenance_jobs_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: menu_roles menu_roles_menu_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.menu_roles
    ADD CONSTRAINT menu_roles_menu_id_fkey FOREIGN KEY (menu_id) REFERENCES public.menus(id) ON DELETE CASCADE;


--
-- Name: menu_roles menu_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.menu_roles
    ADD CONSTRAINT menu_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: menus menus_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.menus
    ADD CONSTRAINT menus_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.menus(id) ON DELETE CASCADE;


--
-- Name: user_activity_logs user_activity_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_activity_logs
    ADD CONSTRAINT user_activity_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_api_keys user_api_keys_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT user_api_keys_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_api_keys user_api_keys_revoked_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT user_api_keys_revoked_by_fkey FOREIGN KEY (revoked_by) REFERENCES public.users(id);


--
-- Name: user_api_keys user_api_keys_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_api_keys
    ADD CONSTRAINT user_api_keys_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_company_access user_company_access_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_company_access
    ADD CONSTRAINT user_company_access_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- Name: user_company_access user_company_access_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_company_access
    ADD CONSTRAINT user_company_access_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_compliance_documents user_compliance_documents_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_compliance_documents
    ADD CONSTRAINT user_compliance_documents_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_compliance_documents user_compliance_documents_original_verified_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_compliance_documents
    ADD CONSTRAINT user_compliance_documents_original_verified_by_fkey FOREIGN KEY (original_verified_by) REFERENCES public.users(id);


--
-- Name: user_compliance_documents user_compliance_documents_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_compliance_documents
    ADD CONSTRAINT user_compliance_documents_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- Name: user_compliance_documents user_compliance_documents_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_compliance_documents
    ADD CONSTRAINT user_compliance_documents_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_compliance_documents user_compliance_documents_verified_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_compliance_documents
    ADD CONSTRAINT user_compliance_documents_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES public.users(id);


--
-- Name: user_dashboards user_dashboards_dashboard_definition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_dashboards
    ADD CONSTRAINT user_dashboards_dashboard_definition_id_fkey FOREIGN KEY (dashboard_definition_id) REFERENCES public.dashboard_definitions(id);


--
-- Name: user_digital_identity user_digital_identity_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_digital_identity
    ADD CONSTRAINT user_digital_identity_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_digital_identity user_digital_identity_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_digital_identity
    ADD CONSTRAINT user_digital_identity_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_digital_identity user_digital_identity_verified_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_digital_identity
    ADD CONSTRAINT user_digital_identity_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES public.users(id);


--
-- Name: user_emergency_contacts user_emergency_contacts_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_emergency_contacts
    ADD CONSTRAINT user_emergency_contacts_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_emergency_contacts user_emergency_contacts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_emergency_contacts
    ADD CONSTRAINT user_emergency_contacts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_emergency_contacts user_emergency_contacts_verified_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_emergency_contacts
    ADD CONSTRAINT user_emergency_contacts_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES public.users(id);


--
-- Name: user_group_members user_group_members_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_group_members
    ADD CONSTRAINT user_group_members_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.user_groups(id);


--
-- Name: user_group_members user_group_members_joined_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_group_members
    ADD CONSTRAINT user_group_members_joined_by_fkey FOREIGN KEY (joined_by) REFERENCES public.users(id);


--
-- Name: user_group_members user_group_members_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_group_members
    ADD CONSTRAINT user_group_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_groups user_groups_parent_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_groups
    ADD CONSTRAINT user_groups_parent_group_id_fkey FOREIGN KEY (parent_group_id) REFERENCES public.user_groups(id);


--
-- Name: user_login_attempts user_login_attempts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_login_attempts
    ADD CONSTRAINT user_login_attempts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_maritime_profiles user_maritime_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_maritime_profiles
    ADD CONSTRAINT user_maritime_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_medical_records user_medical_records_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_medical_records
    ADD CONSTRAINT user_medical_records_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_medical_records user_medical_records_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_medical_records
    ADD CONSTRAINT user_medical_records_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_notifications user_notifications_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_notifications
    ADD CONSTRAINT user_notifications_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_notifications user_notifications_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_notifications
    ADD CONSTRAINT user_notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_password_history user_password_history_changed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_password_history
    ADD CONSTRAINT user_password_history_changed_by_fkey FOREIGN KEY (changed_by) REFERENCES public.users(id);


--
-- Name: user_password_history user_password_history_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_password_history
    ADD CONSTRAINT user_password_history_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_payroll_settings user_payroll_settings_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_payroll_settings
    ADD CONSTRAINT user_payroll_settings_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_payroll_settings user_payroll_settings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_payroll_settings
    ADD CONSTRAINT user_payroll_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_payroll_settings user_payroll_settings_verified_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_payroll_settings
    ADD CONSTRAINT user_payroll_settings_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES public.users(id);


--
-- Name: user_permissions user_permissions_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- Name: user_permissions user_permissions_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.user_groups(id);


--
-- Name: user_permissions user_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id);


--
-- Name: user_permissions user_permissions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_preferences user_preferences_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_preferences
    ADD CONSTRAINT user_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_roles user_roles_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.users(id);


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id);


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_rotation_plans user_rotation_plans_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_rotation_plans
    ADD CONSTRAINT user_rotation_plans_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.users(id);


--
-- Name: user_rotation_plans user_rotation_plans_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_rotation_plans
    ADD CONSTRAINT user_rotation_plans_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_rotation_plans user_rotation_plans_relief_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_rotation_plans
    ADD CONSTRAINT user_rotation_plans_relief_user_id_fkey FOREIGN KEY (relief_user_id) REFERENCES public.users(id);


--
-- Name: user_rotation_plans user_rotation_plans_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_rotation_plans
    ADD CONSTRAINT user_rotation_plans_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_sessions user_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_theme_preferences user_theme_preferences_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_theme_preferences
    ADD CONSTRAINT user_theme_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_training_records user_training_records_assessed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_training_records
    ADD CONSTRAINT user_training_records_assessed_by_fkey FOREIGN KEY (assessed_by) REFERENCES public.users(id);


--
-- Name: user_training_records user_training_records_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_training_records
    ADD CONSTRAINT user_training_records_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: user_training_records user_training_records_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_training_records
    ADD CONSTRAINT user_training_records_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_training_records user_training_records_verified_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_training_records
    ADD CONSTRAINT user_training_records_verified_by_fkey FOREIGN KEY (verified_by) REFERENCES public.users(id);


--
-- Name: user_vessel_assignments user_vessel_assignments_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_assignments
    ADD CONSTRAINT user_vessel_assignments_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.users(id);


--
-- Name: user_vessel_assignments user_vessel_assignments_relieving_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_assignments
    ADD CONSTRAINT user_vessel_assignments_relieving_user_id_fkey FOREIGN KEY (relieving_user_id) REFERENCES public.users(id);


--
-- Name: user_vessel_assignments user_vessel_assignments_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_assignments
    ADD CONSTRAINT user_vessel_assignments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_vessel_connectivity user_vessel_connectivity_assignment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_connectivity
    ADD CONSTRAINT user_vessel_connectivity_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.user_vessel_assignments(id);


--
-- Name: user_vessel_connectivity user_vessel_connectivity_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_vessel_connectivity
    ADD CONSTRAINT user_vessel_connectivity_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_widget_preferences user_widget_preferences_dashboard_widget_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.user_widget_preferences
    ADD CONSTRAINT user_widget_preferences_dashboard_widget_id_fkey FOREIGN KEY (dashboard_widget_id) REFERENCES public.dashboard_widgets(id);


--
-- Name: vessel_alerts vessel_alerts_acknowledged_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: zekigalip
--

ALTER TABLE ONLY public.vessel_alerts
    ADD CONSTRAINT vessel_alerts_acknowledged_by_fkey FOREIGN KEY (acknowledged_by) REFERENCES public.users(id);


--
-- Name: vessel_alerts vessel_alerts_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: zekigalip
--

ALTER TABLE ONLY public.vessel_alerts
    ADD CONSTRAINT vessel_alerts_resolved_by_fkey FOREIGN KEY (resolved_by) REFERENCES public.users(id);


--
-- Name: vessel_alerts vessel_alerts_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: zekigalip
--

ALTER TABLE ONLY public.vessel_alerts
    ADD CONSTRAINT vessel_alerts_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessel_certificates vessel_certificates_certificate_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_certificates
    ADD CONSTRAINT vessel_certificates_certificate_type_id_fkey FOREIGN KEY (certificate_type_id) REFERENCES public.certificate_types(id);


--
-- Name: vessel_certificates vessel_certificates_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_certificates
    ADD CONSTRAINT vessel_certificates_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessel_crew_assignments vessel_crew_assignments_rank_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_crew_assignments
    ADD CONSTRAINT vessel_crew_assignments_rank_id_fkey FOREIGN KEY (rank_id) REFERENCES public.crew_ranks(id);


--
-- Name: vessel_crew_assignments vessel_crew_assignments_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_crew_assignments
    ADD CONSTRAINT vessel_crew_assignments_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessel_equipment vessel_equipment_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_equipment
    ADD CONSTRAINT vessel_equipment_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.maintenance_categories(id);


--
-- Name: vessel_equipment vessel_equipment_parent_equipment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_equipment
    ADD CONSTRAINT vessel_equipment_parent_equipment_id_fkey FOREIGN KEY (parent_equipment_id) REFERENCES public.vessel_equipment(id);


--
-- Name: vessel_equipment vessel_equipment_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_equipment
    ADD CONSTRAINT vessel_equipment_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessel_inspections vessel_inspections_inspection_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_inspections
    ADD CONSTRAINT vessel_inspections_inspection_type_id_fkey FOREIGN KEY (inspection_type_id) REFERENCES public.inspection_types(id);


--
-- Name: vessel_inspections vessel_inspections_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_inspections
    ADD CONSTRAINT vessel_inspections_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessel_performance_daily vessel_performance_daily_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_performance_daily
    ADD CONSTRAINT vessel_performance_daily_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessel_positions vessel_positions_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: zekigalip
--

ALTER TABLE ONLY public.vessel_positions
    ADD CONSTRAINT vessel_positions_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessel_voyages vessel_voyages_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessel_voyages
    ADD CONSTRAINT vessel_voyages_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: vessels vessels_flag_state_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_flag_state_id_fkey FOREIGN KEY (flag_state_id) REFERENCES public.flag_states(id);


--
-- Name: vessels vessels_main_engine_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_main_engine_id_fkey FOREIGN KEY (main_engine_id) REFERENCES public.engine_types(id);


--
-- Name: vessels vessels_vessel_class_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_vessel_class_id_fkey FOREIGN KEY (vessel_class_id) REFERENCES public.vessel_classes(id);


--
-- Name: vessels vessels_vessel_status_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_vessel_status_id_fkey FOREIGN KEY (vessel_status_id) REFERENCES public.vessel_statuses(id);


--
-- Name: vessels vessels_vessel_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_vessel_type_id_fkey FOREIGN KEY (vessel_type_id) REFERENCES public.vessel_types(id);


--
-- Name: voyage_port_calls voyage_port_calls_voyage_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.voyage_port_calls
    ADD CONSTRAINT voyage_port_calls_voyage_id_fkey FOREIGN KEY (voyage_id) REFERENCES public.vessel_voyages(id);


--
-- Name: widget_interaction_logs widget_interaction_logs_dashboard_widget_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_interaction_logs
    ADD CONSTRAINT widget_interaction_logs_dashboard_widget_id_fkey FOREIGN KEY (dashboard_widget_id) REFERENCES public.dashboard_widgets(id);


--
-- Name: widget_interaction_logs widget_interaction_logs_widget_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_interaction_logs
    ADD CONSTRAINT widget_interaction_logs_widget_id_fkey FOREIGN KEY (widget_id) REFERENCES public.widgets(id);


--
-- Name: widget_permissions widget_permissions_widget_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widget_permissions
    ADD CONSTRAINT widget_permissions_widget_id_fkey FOREIGN KEY (widget_id) REFERENCES public.widgets(id);


--
-- Name: widgets widgets_widget_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.widgets
    ADD CONSTRAINT widgets_widget_type_id_fkey FOREIGN KEY (widget_type_id) REFERENCES public.widget_types(id);


--
-- Name: work_orders work_orders_maintenance_job_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.work_orders
    ADD CONSTRAINT work_orders_maintenance_job_id_fkey FOREIGN KEY (maintenance_job_id) REFERENCES public.maintenance_jobs(id);


--
-- Name: work_orders work_orders_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dbadmin
--

ALTER TABLE ONLY public.work_orders
    ADD CONSTRAINT work_orders_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO sms_user;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO sms_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO sms_user;


--
-- PostgreSQL database dump complete
--

