CREATE TABLE [dbo].[Users] (
    [UserID] uniqueidentifier NOT NULL DEFAULT NULL,
    [<PERSON>r<PERSON><PERSON><PERSON>] uniqueidentifier NULL DEFAULT (newid()),
    [CurrentSessionID] uniqueidentifier NULL DEFAULT (newid()),
    [Name] nvarchar(150) NOT NULL DEFAULT NULL,
    [Surname] nvarchar(150) NOT NULL DEFAULT NULL,
    [Email] nvarchar(150) NOT NULL DEFAULT NULL,
    [Password] nvarchar(150) NOT NULL DEFAULT NULL,
    [IsActive] bit NOT NULL DEFAULT NULL,
    [IsDeleted] bit NOT NULL DEFAULT NULL,
    [OldUserID] int NULL DEFAULT NULL,
    [SelectorID] int NULL DEFAULT NULL,
    [UserRowPointer] uniqueidentifier NOT NULL DEFAULT (newid()),
    [IsSystemUser] bit NULL DEFAULT NULL,
    [AccessFailedCount] int NULL DEFAULT NULL,
    [LastLoginTime] datetime NULL DEFAULT NULL,
    [PasswordResetCode] nvarchar(328) NULL DEFAULT NULL,
    [LanguageID] uniqueidentifier NULL DEFAULT NULL,
    [DirectoryEnum] int NULL DEFAULT NULL,
    [TenantId] uniqueidentifier NULL DEFAULT NULL,
    [UserName] nvarchar(256) NOT NULL DEFAULT (''),
    [NormalizedUserName] nvarchar(256) NOT NULL DEFAULT (''),
    [NormalizedEmail] nvarchar(256) NOT NULL DEFAULT (''),
    [EmailConfirmed] bit NOT NULL DEFAULT ((0)),
    [PasswordHash] nvarchar(256) NULL DEFAULT NULL,
    [SecurityStamp] nvarchar(256) NOT NULL DEFAULT (''),
    [IsExternal] bit NOT NULL DEFAULT ((0)),
    [PhoneNumber] nvarchar(16) NULL DEFAULT NULL,
    [PhoneNumberConfirmed] bit NOT NULL DEFAULT ((0)),
    [TwoFactorEnabled] bit NOT NULL DEFAULT ((0)),
    [LockoutEnd] datetimeoffset NULL DEFAULT NULL,
    [LockoutEnabled] bit NOT NULL DEFAULT ((0)),
    [ExtraProperties] nvarchar(-0.5) NOT NULL DEFAULT ('{}'),
    [ConcurrencyStamp] nvarchar(40) NULL DEFAULT NULL,
    [CreationTime] datetime2 NOT NULL DEFAULT (getdate()),
    [CreatorId] uniqueidentifier NULL DEFAULT NULL,
    [LastModificationTime] datetime2 NULL DEFAULT NULL,
    [LastModifierId] uniqueidentifier NULL DEFAULT NULL,
    [DeleterId] uniqueidentifier NULL DEFAULT NULL,
    [DeletionTime] datetime2 NULL DEFAULT NULL,
    [ShouldChangePasswordOnNextLogin] bit NOT NULL DEFAULT ((0)),
    [LastPasswordChangeTime] datetimeoffset NULL DEFAULT NULL,
    [EntityVersion] int NOT NULL DEFAULT ((0)),
    [(42] rows NOT NULL 
,
    CONSTRAINT [PK_Users  (1 rows affected)] PRIMARY KEY ([], [(0 rows affected)])
)
GO
