﻿CREATE   VIEW [dbo].[CostCenterHierarchyView] AS

WITH CategoryHierarchy AS 
(SELECT CostCenterID,
CAST(RIGHT(REPLICATE('0', 4) + CAST(OrderNo AS VARCHAR(10)), 4) AS VARCHAR(8000)) AS HierarchicalOrder


FROM acc.CostCenters
WHERE (ParentCostCenterID IS N
ULL) AND (IsDeleted = 0)
UNION ALL
SELECT        
dc.CostCenterID,
CAST(ch.HierarchicalOrder + RIGHT(REPLICATE('0', 4) + CAST(dc.OrderNo AS VARCHAR(10)), 4) AS VARCHAR(8000)) AS Expr1
FROM  acc.CostCenters AS dc INNER JOIN
CategoryHierarchy AS ch ON dc.Pa
rentCostCenterID = ch.CostCenterID
WHERE (dc.IsDeleted = 0))

SELECT CostCenterID, HierarchicalOrder
FROM   CategoryHierarchy

GO



CREATE VIEW [dbo].[MRV]

AS

SELECT        TOP (100) PERCENT dbo.OperationDeckEngineLogs.DistanceCommitted, dbo.OperationDeckEngineLogs.VesselStatusEnum, dbo.OperationDeckEngineLogs.NetHoursAtSea, 

                         dbo.OperationDeckEngineLogs.ReportDatetime, dbo.Ports.Name AS Port, HSFOVBL.ROB AS HSFOROB, HSFOVBL.Received AS HSFOReceived, ULSFOVBL.Received AS ULSFOReceived, 

                         ULSFOVBL.ROB AS ULSFOROB, dbo.Countries.RegionalGroupEnum, dbo.OperationVoyages.VesselID, REPLACE(ISNULL(LTRIM(dbo.OperationStatementOfFacts.BillOfLoading), ''), ',', '.') AS BillOfLoading, 

                         REPLACE(ISNULL(LTRIM(dbo.OperationCargoVsOperationVoyages.CargoQuantity), ''), ',', '.') AS CargoQuantity, HSMGOVBL.Received AS HSMGOReceived, HSMGOVBL.ROB AS HSMGOROB, 

                         LSMGOVBL.ROB AS LSMGOROB, LSMGOVBL.Received AS LSMGOReceived

FROM            dbo.OperationDeckEngineLogs INNER JOIN

                         dbo.OperationVoyages ON dbo.OperationDeckEngineLogs.OperationVoyageID = dbo.OperationVoyages.OperationVoyageID INNER JOIN

                         dbo.Ports ON dbo.OperationVoyages.DestinationPortID = dbo.Ports.PortID INNER JOIN

                         dbo.VesselBunkerLogs AS HSFOVBL ON dbo.OperationDeckEngineLogs.OperationDeckEngineLogID = HSFOVBL.RowID INNER JOIN

                         dbo.VesselBunkerLogs AS ULSFOVBL ON dbo.OperationDeckEngineLogs.OperationDeckEngineLogID = ULSFOVBL.RowID INNER JOIN

                         dbo.Countries ON dbo.Ports.CountryID = dbo.Countries.CountryID INNER JOIN

                         dbo.OperationStatementOfFacts ON dbo.OperationVoyages.OperationVoyageID = dbo.OperationStatementOfFacts.OperationVoyageID AND 

                         dbo.OperationVoyages.DestinationPortID = dbo.OperationStatementOfFacts.PortID INNER JOIN

                         dbo.OperationCargoVsOperationVoyages ON dbo.OperationStatementOfFacts.OperationVoyageID = dbo.OperationCargoVsOperationVoyages.OperationVoyageID AND 

                         dbo.OperationStatementOfFacts.OperationCargoID = dbo.OperationCargoVsOperationVoyages.OperationCargoID INNER JOIN

                         dbo.VesselBunkerLogs AS HSMGOVBL ON dbo.OperationDeckEngineLogs.OperationDeckEngineLogID = HSMGOVBL.RowID INNER JOIN

                         dbo.VesselBunkerLogs AS LSMGOVBL ON dbo.OperationDeckEngineLogs.OperationDeckEngineLogID = LSMGOVBL.RowID

WHERE        (dbo.OperationDeckEngineLogs.IsDeleted = 0) AND (HSFOVBL.IsDeleted = 0) AND (HSFOVBL.VesselBunkerTypeID = 'E3F52815-7B23-4EE3-B178-60563462D266') AND 

                         (ULSFOVBL.VesselBunkerTypeID = '2BB8AAB0-BCCC-414C-A3B0-700B8CC88F84') AND (dbo.OperationStatementOfFacts.IsDeleted = 0) AND (dbo.OperationStatementOfFacts.BillOfLoading IS NOT NULL) AND 

                         (dbo.OperationCargoVsOperationVoyages.IsDeleted = 0) AND (HSMGOVBL.IsDeleted = 0) AND (HSMGOVBL.VesselBunkerTypeID = '852A7209-B8A4-4820-8E94-7A197B9D6CCE') AND (LSMGOVBL.IsDeleted = 0) 

                         AND (LSMGOVBL.VesselBunkerTypeID = 'A201180B-B65E-49CF-AA9D-2E692BD2C3E7')

GO

CREATE VIEW [dbo].[vw_CrewExperiencesByAllValues]

AS

SELECT        dbo.Vessels.Name AS VesselName, dbo.CrewWorkPositions.Name AS WorkPositionName, DATEDIFF(DAY, dbo.CrewExperiences.StartDate, ISNULL(dbo.CrewExperiences.FinishDate, GETDATE())) 

                         + 1 AS WorkingDay, dbo.Ports.Name AS StartPortName, dbo.CrewExperienceLeftReasons.Name AS Reason, dbo.CrewExperiences.CrewExperienceID, dbo.CrewExperiences.CrewIdentityID, 

                         dbo.CrewExperiences.CrewWorkPositionID, dbo.CrewExperiences.CrewExperienceLeftReasonID, dbo.CrewExperiences.VesselID, dbo.CrewExperiences.StartDate, dbo.CrewExperiences.FinishDate, 

                         dbo.CrewExperiences.StartPortID, dbo.CrewExperiences.FinishPortID, dbo.CrewExperiences.ExpireDate, dbo.CrewExperiences.RankShortening, dbo.CrewExperiences.Comment, 

                         dbo.CrewExperiences.IsDeleted

FROM            dbo.CrewExperiences INNER JOIN

                         dbo.CrewIdentities ON dbo.CrewExperiences.CrewIdentityID = dbo.CrewIdentities.CrewIdentityID INNER JOIN

                         dbo.Vessels ON dbo.CrewExperiences.VesselID = dbo.Vessels.VesselID LEFT OUTER JOIN

                         dbo.Ports ON dbo.CrewExperiences.StartPortID = dbo.Ports.PortID LEFT OUTER JOIN

                         dbo.CrewWorkPositions ON dbo.CrewExperiences.CrewWorkPositionID = dbo.CrewWorkPositions.CrewWorkPositionID LEFT OUTER JOIN

                         dbo.CrewExperienceLeftReasons ON dbo.CrewExperiences.CrewExperienceLeftReasonID = dbo.CrewExperienceLeftReasons.CrewExperienceLeftReasonID

GO

CREATE VIEW [dbo].[vw_CrewFormAnswers]

AS

SELECT        dbo.CrewFormAnswerGroups.CrewFormAnswerGroupID, dbo.CrewFormAnswerGroups.CrewFormID, dbo.CrewFormAnswerGroups.VesselID, dbo.CrewFormAnswerGroups.EnteredByUserID, 

                         dbo.CrewFormAnswerGroups.CrewIdentityID, dbo.CrewFormAnswerGroups.CrewCapabilityID, dbo.CrewFormAnswerGroups.EntryDate, dbo.CrewFormAnswerGroups.IsDeleted, 

                         dbo.CrewFormAnswers.CrewFormAnswerID, dbo.CrewFormAnswers.CrewFormQuestionID, dbo.CrewFormAnswers.Answer, dbo.CrewFormAnswers.AnswerYesNo, dbo.CrewFormAnswers.AnswerHowManyTimes, 

                         dbo.CrewFormAnswers.AnswerEvaluationPoint, dbo.CrewFormAnswers.ANTBIReportNo, dbo.CrewFormAnswers.QuestionNo, dbo.CrewFormAnswers.GroupNo, dbo.CrewFormAnswers.IsDeleted AS Expr1

FROM            dbo.CrewFormAnswerGroups INNER JOIN

                         dbo.CrewFormAnswers ON dbo.CrewFormAnswerGroups.CrewFormAnswerGroupID = dbo.CrewFormAnswers.CrewFormAnswerGroupID

WHERE        (dbo.CrewFormAnswers.IsDeleted <> 1) AND (dbo.CrewFormAnswerGroups.IsDeleted <> 1)

GO

CREATE VIEW [dbo].[vw_CrewFormDefinition]

AS

SELECT        dbo.CrewForms.Name AS FormName, dbo.CrewForms.Description AS FormDescription, dbo.CrewForms.FormNo, dbo.CrewForms.RevisionNo, dbo.CrewForms.RevisionDate, dbo.CrewForms.IsCrewList, 

                         dbo.CrewForms.IsTimePeriod, dbo.CrewForms.IsProtected, dbo.CrewForms.IsEvaluation, dbo.CrewFormGroups.CrewFormID, dbo.CrewFormGroups.CrewFormGroupID, dbo.CrewFormGroups.GroupNo, 

                         dbo.CrewFormGroups.Name AS FormGroupName, dbo.CrewFormGroups.Description AS FormGroupDescription, dbo.CrewFormQuestions.CrewFormQuestionID, dbo.CrewFormQuestions.Question, 

                         dbo.CrewFormQuestions.QuestionNo, dbo.CrewFormQuestions.IsHowManyTimes, dbo.CrewFormQuestions.IsPointScoring, dbo.CrewFormQuestions.IsYesNoQuestion, dbo.CrewFormQuestions.IsANTBI, 

                         dbo.CrewFormQuestionVsCrewWorkPositions.CrewFormQuestionVsCrewWorkPositionID, dbo.CrewFormQuestionVsCrewWorkPositions.MinEvaluationPoints, dbo.CrewFormQuestionVsCrewWorkPositions.IsDeleted, 

                         dbo.CrewFormQuestionVsCrewWorkPositions.CrewWorkPositionID

FROM            dbo.CrewFormQuestionVsCrewWorkPositions INNER JOIN

                         dbo.CrewFormQuestions ON dbo.CrewFormQuestionVsCrewWorkPositions.CrewFormQuestionID = dbo.CrewFormQuestions.CrewFormQuestionID INNER JOIN

                         dbo.CrewFormGroups ON dbo.CrewFormQuestions.CrewFormGroupID = dbo.CrewFormGroups.CrewFormGroupID INNER JOIN

                         dbo.CrewForms ON dbo.CrewFormGroups.CrewFormID = dbo.CrewForms.CrewFormID

WHERE        (dbo.CrewFormQuestionVsCrewWorkPositions.IsDeleted <> 1) AND (dbo.CrewForms.IsDeleted = 0) AND (dbo.CrewFormQuestions.IsDeleted = 0) AND (dbo.CrewFormGroups.IsDeleted = 0)

GO

CREATE VIEW [dbo].[vw_DocumentCategoryWithLogicalOrder] AS

WITH CategoryHierarchy AS (SELECT DocumentCategoryID,

                                  CAST(RIGHT(REPLICATE('0', 4) + CAST([OrderNo] AS VARCHAR(10)), 4) AS VARCHAR(8000)) AS HierarchicalOrder

                           FROM DocumentCategories

                           WHERE ParentID IS NULL

                             And IsDeleted = 0

                           UNION ALL

                           SELECT dc.DocumentCategoryID,

                                  CAST(ch.HierarchicalOrder +

                                       RIGHT(REPLICATE('0', 4) + CAST(dc.[OrderNo] AS VARCHAR(10)), 4) AS VARCHAR(8000))

                           FROM DocumentCategories dc

                                    INNER JOIN

                                CategoryHierarchy ch ON dc.ParentID = ch.DocumentCategoryID

                           Where IsDeleted = 0)

SELECT DocumentCategoryID, HierarchicalOrder

FROM CategoryHierarchy

GO

CREATE VIEW [dbo].[vw_GetAnalysisAverageNumberOfDeficienciesPerInspection]

AS

SELECT        dbo.SafetyVettings.SafetyVettingID, dbo.SafetyVettings.IssueDate, V.DateLeft AS VesselDateLeft, V.Name AS Vessel, dbo.SafetyVettingDefinations.Name AS SubOrigin, dbo.SafetyVettingGroups.Name AS Origin, 

                         YEAR(dbo.SafetyVettings.IssueDate) AS DateOfYear, DATENAME(month, DATEADD(month, MONTH(dbo.SafetyVettings.IssueDate), - 1)) AS DateOfMonth, CASE WHEN MONTH(IssueDate) >= 1 AND MONTH(IssueDate) 

                         <= 3 THEN 'Q1' WHEN MONTH(IssueDate) >= 4 AND MONTH(IssueDate) <= 6 THEN 'Q2' WHEN MONTH(IssueDate) >= 7 AND MONTH(IssueDate) <= 9 THEN 'Q3' WHEN MONTH(IssueDate) >= 10 AND MONTH(IssueDate) 

                         <= 12 THEN 'Q4' END AS Quarter, CAST

                             ((SELECT        COUNT(*) AS Expr1

                                 FROM            dbo.SafetyANTBI

                                 WHERE        (SafetyVettingID = dbo.SafetyVettings.SafetyVettingID) AND (IsDeleted = 0)) AS decimal(18, 2)) AS SafetyANTBICount

FROM            dbo.SafetyVettings INNER JOIN

                         dbo.SafetyVettingGroups ON dbo.SafetyVettings.SafetyVettingGroupID = dbo.SafetyVettingGroups.SafetyVettingGroupID INNER JOIN

                         dbo.SafetyVettingDefinations ON dbo.SafetyVettings.SafetyVettingDefinationID = dbo.SafetyVettingDefinations.SafetyVettingDefinationID INNER JOIN

                         dbo.Vessels AS V ON dbo.SafetyVettings.VesselID = V.VesselID

WHERE        (dbo.SafetyVettings.IsDeleted = 0) AND (dbo.SafetyVettingDefinations.IsDeleted = 0) AND (dbo.SafetyVettingGroups.IsDeleted = 0) AND (V.IsDeleted = 0)

GO



CREATE VIEW [dbo].[vw_GetAnalysisNumberOfDeficiencies]

AS

SELECT dbo.SafetyANTBI.SafetyANTBIID, dbo.SafetyANTBI.SafetyANTBITypeofFindingID as TypeOfFindingID, dbo.SafetyANTBITypeOfReports.Name AS ANTBITypeOfReport, dbo.SafetyANTBIReasons.Name AS ANTBIReason, dbo.SafetyVettingGroups.Name AS Origin,

                  dbo.SafetyANTBIRatings.Name AS ANTBIRating, dbo.SafetyANTBI.Chapter AS ANTBIChapter, dbo.SafetyANTBI.Section AS ANTBISection, dbo.SafetyVettingDefinations.Name AS SubOrigin,

                  CASE dbo.SafetyANTBI.IsSupportRequest WHEN 1 THEN 'Yes' ELSE 'No' END AS ANTBIExternalSupportRequest, CASE dbo.SafetyANTBI.IsDryDock WHEN 1 THEN 'Yes' ELSE 'No' END AS ANTBIIsDryDock,

                  CASE dbo.SafetyANTBI.IsPostponed WHEN 1 THEN 'Yes' ELSE 'No' END AS ANTBIIsPostponed, CASE dbo.PMSModels.IsCriticalEquipment WHEN 1 THEN 'Yes' ELSE 'No' END AS IsCriticalEquipment,

                  dbo.SafetyActionTaken.Name AS ANTBIActionTaken, CASE WHEN (SafetyANTBI.IsCompleted = 0 AND DATEDIFF(HH, dbo.SafetyANTBI.ExpireDate, dbo.SafetyANTBI.CompletedDate) <= 0) OR

                  (SafetyANTBI.IsCompleted = 0 AND SafetyANTBI.IsPostponed = 1) THEN 'Yes' ELSE 'No' END AS IsOutstandingPending, dbo.SafetyANTBI.ReportDate, dbo.Vessels.Name AS Vessel, YEAR(dbo.SafetyANTBI.ReportDate) AS DateOfYear,

                  YEAR(dbo.SafetyANTBI.ExpireDate) AS DateOfYearExpireDate, DATENAME(month, DATEADD(month, MONTH(dbo.SafetyANTBI.ReportDate), - 1)) AS DateOfMonth, DATENAME(month, DATEADD(month,

                  MONTH(dbo.SafetyANTBI.ReportDate), - 1)) AS DateOfMonthExpireDate, CASE WHEN MONTH(SafetyANTBI.ReportDate) >= 1 AND MONTH(SafetyANTBI.ReportDate) <= 3 THEN 'Q1' WHEN MONTH(SafetyANTBI.ReportDate) >= 4 AND

                  MONTH(SafetyANTBI.ReportDate) <= 6 THEN 'Q2' WHEN MONTH(SafetyANTBI.ReportDate) >= 7 AND MONTH(SafetyANTBI.ReportDate) <= 9 THEN 'Q3' WHEN MONTH(SafetyANTBI.ReportDate) >= 10 AND MONTH(SafetyANTBI.ReportDate)

                  <= 12 THEN 'Q4' END AS Quarter, CASE WHEN MONTH(SafetyANTBI.ExpireDate) >= 1 AND MONTH(SafetyANTBI.ExpireDate) <= 3 THEN 'Q1' WHEN MONTH(SafetyANTBI.ExpireDate) >= 4 AND MONTH(SafetyANTBI.ExpireDate)

                  <= 6 THEN 'Q2' WHEN MONTH(SafetyANTBI.ExpireDate) >= 7 AND MONTH(SafetyANTBI.ExpireDate) <= 9 THEN 'Q3' WHEN MONTH(SafetyANTBI.ExpireDate) >= 10 AND MONTH(SafetyANTBI.ExpireDate)

                  <= 12 THEN 'Q4' END AS QuarterExpireDate, dbo.SafetyANTBI.ExpireDate

FROM     dbo.SafetyANTBI LEFT OUTER JOIN

                  dbo.SafetyANTBIReasons ON dbo.SafetyANTBI.SafetyANTBIReasonID = dbo.SafetyANTBIReasons.SafetyANTBIReasonID LEFT OUTER JOIN

                  dbo.SafetyANTBIRatings ON dbo.SafetyANTBI.SafetyANTBIRatingID = dbo.SafetyANTBIRatings.SafetyANTBIRatingID INNER JOIN

                  dbo.SafetyANTBITypeOfReports ON dbo.SafetyANTBI.SafetyANTBITypeOfReportID = dbo.SafetyANTBITypeOfReports.SafetyANTBITypeOfReportID INNER JOIN

                  dbo.SafetyVettingGroups ON dbo.SafetyANTBI.SafetyVettingGroupID = dbo.SafetyVettingGroups.SafetyVettingGroupID INNER JOIN

                  dbo.Vessels ON dbo.SafetyANTBI.VesselID = dbo.Vessels.VesselID LEFT OUTER JOIN

                  dbo.SafetyActionTaken ON dbo.SafetyANTBI.SafetyActionTakenID = dbo.SafetyActionTaken.SafetyActionTakenID LEFT OUTER JOIN

                  dbo.SafetyVettingDefinations ON dbo.SafetyANTBI.SafetyVettingDefinationID = dbo.SafetyVettingDefinations.SafetyVettingDefinationID LEFT OUTER JOIN

                  dbo.PMSEquipments ON dbo.SafetyANTBI.PMSEquipmentID = dbo.PMSEquipments.PMSEquipmentID LEFT OUTER JOIN

                  dbo.PMSModels ON dbo.PMSModels.PMSModelID = dbo.PMSEquipments.PMSModelID

WHERE  (dbo.SafetyANTBI.IsDeleted = 0)

GO

CREATE VIEW [dbo].[vw_GetAnalysisNumberOfIncidentInvestigations] AS 

SELECT dbo.SafetyAccidents.SafetyAccidentID, dbo.SafetyAccidents.ReportTypeEnum AS ReportType, dbo.SafetyAccidents.DateOfEvent, dbo.Vessels.Name AS Vessel, CONVERT(nvarchar(50), 

                  dbo.SafetyAccidents.StatisticalReportMainGroupID) AS StatisticalReportMainGroup, CONVERT(nvarchar(50), dbo.SafetyAccidents.StatisticalReportSubGroupID) AS StatisticalReportSubGroup, 

                  CASE WHEN SafetyAccidents.CompanySafetySuperIntendentID IS NULL THEN 'No' ELSE 'Yes' END AS IsCompanySuperintendent, YEAR(dbo.SafetyAccidents.DateOfEvent) AS DateOfYear, DATENAME(month, DATEADD(month, 

                  MONTH(dbo.SafetyAccidents.DateOfEvent), - 1)) AS DateOfMonth, CASE WHEN MONTH(SafetyAccidents.DateOfEvent) >= 1 AND MONTH(SafetyAccidents.DateOfEvent) <= 3 THEN 'Q1' WHEN MONTH(SafetyAccidents.DateOfEvent) 

                  >= 4 AND MONTH(SafetyAccidents.DateOfEvent) <= 6 THEN 'Q2' WHEN MONTH(SafetyAccidents.DateOfEvent) >= 7 AND MONTH(SafetyAccidents.DateOfEvent) <= 9 THEN 'Q3' WHEN MONTH(SafetyAccidents.DateOfEvent) >= 10 AND 

                  MONTH(SafetyAccidents.DateOfEvent) <= 12 THEN 'Q4' END AS Quarter, dbo.SafetyAccidents.SafetyBehaviourBasedObservationID

FROM     dbo.SafetyAccidents INNER JOIN

                  dbo.Vessels ON dbo.SafetyAccidents.VesselID = dbo.Vessels.VesselID

WHERE  (dbo.SafetyAccidents.IsDeleted = 0) AND (dbo.Vessels.IsDeleted = 0) AND (dbo.SafetyAccidents.IsNewVersion = 1)

GO



CREATE VIEW [dbo].[vw_GetAnalysisNumberOfInspections]

AS

SELECT        dbo.SafetyVettingDefinations.SafetyVettingDefinationID, dbo.SafetyVettingGroups.Name AS Origin, dbo.SafetyVettingDefinations.Name AS SubOrigin, CASE WHEN SafetyVettings.IsVesselDetained IS NULL 

                         THEN 'No' ELSE CASE WHEN SafetyVettings.IsVesselDetained = 1 THEN 'Yes' ELSE 'No' END END AS IsVesselDetained, dbo.Vessels.Name AS Vessel, dbo.SafetyVettings.IssueDate, YEAR(dbo.SafetyVettings.IssueDate) 

                         AS DateOfYear, DATENAME(month, DATEADD(month, MONTH(dbo.SafetyVettings.IssueDate), - 1)) AS DateOfMonth, CASE WHEN MONTH(SafetyVettings.IssueDate) >= 1 AND MONTH(SafetyVettings.IssueDate) 

                         <= 3 THEN 'Q1' WHEN MONTH(SafetyVettings.IssueDate) >= 4 AND MONTH(SafetyVettings.IssueDate) <= 6 THEN 'Q2' WHEN MONTH(SafetyVettings.IssueDate) >= 7 AND MONTH(SafetyVettings.IssueDate) 

                         <= 9 THEN 'Q3' WHEN MONTH(SafetyVettings.IssueDate) >= 10 AND MONTH(SafetyVettings.IssueDate) <= 12 THEN 'Q4' END AS Quarter, dbo.SafetyVettings.SafetyVettingID, dbo.SafetyVettings.ExpireDate, 

                         dbo.SafetyVettingGroups.IsInspectionPlan

FROM            dbo.SafetyVettingDefinations INNER JOIN

                         dbo.SafetyVettingGroups ON dbo.SafetyVettingDefinations.SafetyVettingGroupID = dbo.SafetyVettingGroups.SafetyVettingGroupID INNER JOIN

                         dbo.SafetyVettings ON dbo.SafetyVettingDefinations.SafetyVettingDefinationID = dbo.SafetyVettings.SafetyVettingDefinationID AND 

                         dbo.SafetyVettingGroups.SafetyVettingGroupID = dbo.SafetyVettings.SafetyVettingGroupID INNER JOIN

                         dbo.Vessels ON dbo.SafetyVettings.VesselID = dbo.Vessels.VesselID

WHERE        (dbo.SafetyVettings.IsDeleted = 0) AND (dbo.SafetyVettingGroups.IsDeleted = 0) AND (dbo.SafetyVettingDefinations.IsDeleted = 0) AND (dbo.Vessels.IsDeleted = 0)

GO



CREATE VIEW [dbo].[vw_GetAnalysisNumberOfLossOfHire]



  as



    select NEWID()                                                                    as Id,

           V.Name                                                                  as Vessel,

           T.Name                                                                  as LossType,

           OLOHG.Name                                                              as LossGroup,

           YEAR(OperationLossofHires.LossTimeStart)                                AS DateOfYear,

           DATENAME(month, DATEADD(month,

                                   MONTH(OperationLossofHires.LossTimeStart), -1)) AS DateOfMonth,

           CASE

             WHEN MONTH(OperationLossofHires.LossTimeStart) >= 1 AND MONTH(OperationLossofHires.LossTimeStart) <= 3

                     THEN 'Q1'

             WHEN MONTH(OperationLossofHires.LossTimeStart) >= 4 AND

                  MONTH(OperationLossofHires.LossTimeStart) <= 6 THEN 'Q2'

             WHEN MONTH(OperationLossofHires.LossTimeStart) >= 7 AND MONTH(OperationLossofHires.LossTimeStart) <= 9

                     THEN 'Q3'

             WHEN MONTH(OperationLossofHires.LossTimeStart) >= 10 AND

                  MONTH(OperationLossofHires.LossTimeStart) <= 12 THEN 'Q4' END    AS Quarter,

           (OperationLossofHires.LossTime / 1440)                                    as DaysLost,

           OperationLossofHires.LossTimeStart



    from OperationLossofHires

           inner join OperationLossofHireTypes T

             on OperationLossofHires.OperationLossofHireTypeID = T.OperationLossofHireTypeID

           inner join OperationLossOfHireGroups OLOHG on T.OperationLossofHireGroupID = OLOHG.OperationLossOfHireGroupID

           inner join Vessels V on OperationLossofHires.VesselID = V.VesselID

    where V.IsDeleted = 0

      and OperationLossofHires.IsDeleted = 0

GO

CREATE   VIEW [dbo].[vw_GetAnalysisNumberOfPMSV2Workorders] AS
WITH CriticalEquip AS (
    SELECT 
        EQ.EquipmentID,
        CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM pms.EquipmentVsPriorityTypes EPT2
           
     INNER JOIN pms.PriorityTypes PT2 ON EPT2.PriorityTypeID = PT2.PriorityTypeID AND PT2.IsDeleted = 0
                WHERE EPT2.EquipmentID = EQ.EquipmentID
                AND EPT2.IsDeleted = 0
                AND PT2.Name = 'Critical'
            ) 
THEN 'Yes' 
            ELSE 'No' 
        END AS IsCriticalEquipment
    FROM pms.Equipment EQ
    WHERE EQ.IsDeleted = 0
),
CTE_WorkOrders AS (
    SELECT DISTINCT
        CE.IsCriticalEquipment,
        CASE WO.IsOverdue WHEN 1 THEN 'Yes' ELSE 'No' END
 AS IsOverdue,
        CASE WOPR.PostponeStatusEnum WHEN 1 THEN 'Yes' ELSE 'No' END AS IsPostponed,
        WO.IssueDate AS IssueDate,
        WO.WorkOrderID AS WorkOrderID,
        V.Name AS Vessel,
        0 AS UnScheduledReason,
        YEAR(WO.IssueDa
te) AS DateOfYear,
        DATENAME(month, WO.IssueDate) AS DateOfMonth,
        CASE 
            WHEN MONTH(WO.IssueDate) BETWEEN 1 AND 3 THEN 'Q1'
            WHEN MONTH(WO.IssueDate) BETWEEN 4 AND 6 THEN 'Q2'
            WHEN MONTH(WO.IssueDate) BETWE
EN 7 AND 9 THEN 'Q3'
            WHEN MONTH(WO.IssueDate) BETWEEN 10 AND 12 THEN 'Q4'
        END AS Quarter,
        CASE WHEN WO.IsUnplanned = 1 AND (UR.IsUnplanned = 0 OR UR.IsUnplanned IS NULL) THEN 'Yes' ELSE 'No' END AS IsUnScheduled,
        NEWID(
) AS Id
    FROM pms.WorkOrders WO
    LEFT JOIN CriticalEquip CE ON WO.EquipmentID = CE.EquipmentID
    LEFT JOIN pms.JobPlans JP ON WO.JobPlanID = JP.JobPlanID AND JP.IsDeleted = 0
    LEFT JOIN pms.Equipment EQ ON WO.EquipmentID = EQ.EquipmentID AND EQ
.IsDeleted = 0
    LEFT JOIN pms.UnplannedReasons UR ON WO.UnplannedReasonID = UR.UnplannedReasonID AND UR.IsDeleted = 0
    LEFT JOIN pms.WorkOrderPostponeRecords WOPR ON WO.WorkOrderID = WOPR.WorkOrderID AND WOPR.IsDeleted = 0
    LEFT JOIN dbo.Vessels 
V ON WO.VesselID = V.VesselID AND V.IsDeleted = 0 AND V.IsActive = 1
    WHERE WO.IsDeleted = 0 
      AND WO.IssueDate IS NOT NULL
)
SELECT * 
FROM CTE_WorkOrders
WHERE IsCriticalEquipment IS NOT NULL and IsOverdue IS NOT NULL and IsPostponed IS NOT NULL
 and IssueDate IS NOT NULL and WorkOrderID IS NOT NULL and Vessel IS NOT NULL and UnScheduledReason IS NOT NULL and DateOfYear IS NOT NULL and DateOfMonth IS NOT NULL and Quarter IS NOT NULL and IsUnScheduled IS NOT NULL and Id is not null;

GO



CREATE VIEW [dbo].[vw_GetAnalysisNumberOfPMSWorkorders]

  AS

    SELECT (CASE PMSModels.IsCriticalEquipment WHEN 1 THEN 'Yes' ELSE 'No' END)   AS IsCriticalEquipment,

           (CASE PMSWorkOrders.IsOverdue WHEN 1 THEN 'Yes' ELSE 'No' END)         AS IsOverdue,

           (CASE PMSWorkOrders.PostponeStateEnum WHEN 2 THEN 'Yes' ELSE 'No' END)       AS IsPostponed,

           PMSWorkOrders.IssueDate,

           dbo.Vessels.Name                                                       AS Vessel,

           PMSWorkOrders.UnScheduledReasonEnum                                    as UnScheduledReason,

           YEAR(dbo.PMSWorkOrders.IssueDate)                                      AS DateOfYear,

           DATENAME(month, DATEADD(month,

                                   MONTH(dbo.PMSWorkOrders.IssueDate), -1))       AS DateOfMonth,

           CASE

             WHEN MONTH(PMSWorkOrders.IssueDate) >= 1 AND MONTH(PMSWorkOrders.IssueDate) <= 3 THEN 'Q1'

             WHEN MONTH(PMSWorkOrders.IssueDate) >= 4 AND

                  MONTH(PMSWorkOrders.IssueDate) <= 6 THEN 'Q2'

             WHEN MONTH(PMSWorkOrders.IssueDate) >= 7 AND MONTH(PMSWorkOrders.IssueDate) <= 9 THEN 'Q3'

             WHEN MONTH(PMSWorkOrders.IssueDate) >= 10 AND

                  MONTH(PMSWorkOrders.IssueDate) <= 12 THEN 'Q4' END              AS Quarter,

           (CASE dbo.PMSWorkOrders.IsUnScheduled WHEN 1 THEN 'Yes' ELSE 'No' END) as IsUnScheduled,

           NEWID()                                                                AS Id

    FROM dbo.PMSWorkOrders

           INNER JOIN dbo.PMSJobs ON dbo.PMSWorkOrders.PMSJobID = dbo.PMSJobs.PMSJobID

           INNER JOIN dbo.PMSModelJobs ON dbo.PMSJobs.PMSModelJobID = dbo.PMSModelJobs.PMSModelJobID

           INNER JOIN dbo.PMSModels ON dbo.PMSModelJobs.PMSModelID = dbo.PMSModels.PMSModelID

           INNER JOIN dbo.Vessels

             ON dbo.PMSWorkOrders.VesselID = dbo.Vessels.VesselID AND dbo.PMSJobs.VesselID = dbo.Vessels.VesselID AND

                dbo.PMSModels.VesselID = dbo.Vessels.VesselID

    WHERE (dbo.PMSModelJobs.IsDeleted = 0)

      AND (dbo.PMSModels.IsDeleted = 0)

      AND (dbo.PMSWorkOrders.IsDeleted = 0)

      AND (dbo.PMSJobs.IsDeleted = 0)

      AND (dbo.Vessels.IsDeleted = 0)

      AND (dbo.PMSWorkOrders.IssueDate IS NOT NULL)

GO

CREATE VIEW [dbo].[vw_GetAnalysisNumberOfSafetyMeetings]
  AS
    SELECT dbo.SafetyMeetings.SafetyMeetingID,
           dbo.SafetyMeetings.MeetingTypeEnum                                                         as MeetingType,
           CASE
            
 WHEN SafetyMeetings.SafetySuperIntendentID IS NULL THEN 'No'
             ELSE 'Yes' END                                                                           AS IsHeldBySuperintendent,
           dbo.Vessels.Name                                     
                                      AS Vessel,
           dbo.SafetyMeetings.Date,
           YEAR(dbo.SafetyMeetings.Date)                                                              AS DateOfYear,
           DATENAME(month, DATEADD(month, MONTH(dbo.S
afetyMeetings.Date), -1))                        AS DateOfMonth,
           CASE
             WHEN MONTH(SafetyMeetings.Date) >= 1 AND
                  MONTH(SafetyMeetings.Date) <= 3 THEN 'Q1'
             WHEN MONTH(SafetyMeetings.Date) >= 4 AND MONTH(
SafetyMeetings.Date) <= 6 THEN 'Q2'
             WHEN MONTH(SafetyMeetings.Date) >= 7 AND MONTH(SafetyMeetings.Date)
                                                        <= 9 THEN 'Q3'
             WHEN MONTH(SafetyMeetings.Date) >= 10 AND MONTH(Safety
Meetings.Date) <= 12 THEN 'Q4' END AS Quarter
    FROM dbo.SafetyMeetings
           INNER JOIN dbo.Vessels ON dbo.SafetyMeetings.VesselID = dbo.Vessels.VesselID
    WHERE (dbo.Vessels.IsDeleted = 0)
      AND (dbo.SafetyMeetings.IsDeleted = 0)

GO

CREATE VIEW [dbo].[vw_GetAnalysisNumberOfStatementOfFacts]

AS

SELECT dbo.OperationStatementOfFacts.OperationStatementOfFactID, CASE WHEN OperationStatementOfFacts.IsCargoClaim = 1 THEN 'Yes' ELSE 'No' END AS IsCargoClaim, dbo.Vessels.Name AS Vessel, dbo.OperationStatementOfFacts.Date, 

                  YEAR(dbo.OperationStatementOfFacts.Date) AS DateOfYear, DATENAME(month, DATEADD(month, MONTH(dbo.OperationStatementOfFacts.Date), - 1)) AS DateOfMonth, CASE WHEN MONTH(OperationStatementOfFacts.Date) >= 1 AND 

                  MONTH(OperationStatementOfFacts.Date) <= 3 THEN 'Q1' WHEN MONTH(OperationStatementOfFacts.Date) >= 4 AND MONTH(OperationStatementOfFacts.Date) <= 6 THEN 'Q2' WHEN MONTH(OperationStatementOfFacts.Date) >= 7 AND 

                  MONTH(OperationStatementOfFacts.Date) <= 9 THEN 'Q3' WHEN MONTH(OperationStatementOfFacts.Date) >= 10 AND MONTH(OperationStatementOfFacts.Date) <= 12 THEN 'Q4' END AS Quarter

FROM     dbo.OperationStatementOfFacts INNER JOIN

                  dbo.Vessels ON dbo.OperationStatementOfFacts.VesselID = dbo.Vessels.VesselID

WHERE  (dbo.Vessels.IsDeleted = 0) AND (dbo.OperationStatementOfFacts.IsDeleted = 0)

GO



CREATE VIEW [dbo].[vw_GetAnalysisPercentageOfInspectionWithNilDeficiency]

AS

SELECT NEWID() AS Id, Origin as SafetyVettingGroupName, SubOrigin as SafetyVettingDefinationName, CONVERT(decimal(18, 2), CONVERT(float,

                      (SELECT COUNT(*) AS Expr1

                       FROM      dbo.vw_GetAnalysisNumberOfInspections AS InnerSafety

                       WHERE   (SafetyVettingID NOT IN

                                             (SELECT SafetyVettingID

                                              FROM      dbo.SafetyANTBI

                                              WHERE   (IsDeleted = 0) AND (SafetyVettingID IS NOT NULL))) AND (Origin = tbl.Origin) AND (SubOrigin = tbl.SubOrigin) AND 

                                         (Quarter = tbl.Quarter) AND (DateOfMonth = tbl.DateOfMonth) AND (DateOfYear = tbl.DateOfYear))) * 100 / COUNT(*)) AS PercentageNumber, DateOfMonth, DateOfYear, Quarter

FROM     dbo.vw_GetAnalysisNumberOfInspections AS tbl

GROUP BY DateOfMonth, DateOfYear, Quarter, Origin, SubOrigin

GO

CREATE VIEW [dbo].[vw_GetAnalysisPercentageOfInspectionWithNilDeficiency3]

AS

SELECT        SafetyVettingID AS Id, Origin, SubOrigin, CASE WHEN EXISTS

                             (SELECT        SafetyVettingID

                               FROM            dbo.SafetyANTBI

                               WHERE        (IsDeleted = 0) AND (SafetyVettingID IS NOT NULL) AND SafetyANTBI.SafetyVettingID = tbl.SafetyVettingID) AND (Origin = tbl.Origin) AND (SubOrigin = tbl.SubOrigin) AND (Quarter = tbl.Quarter) AND 

                         (DateOfMonth = tbl.DateOfMonth) AND (DateOfYear = tbl.DateOfYear) THEN 0 ELSE 1 END AS NILRemark, DateOfMonth, DateOfYear, Quarter

FROM            dbo.vw_GetAnalysisNumberOfInspections AS tbl

GROUP BY DateOfMonth, DateOfYear, Quarter, Origin, SubOrigin, SafetyVettingID

GO



CREATE VIEW vw_GetAnalysisPercentageOfOutstandingPMSV2WorkordersWithTotal AS 

SELECT        NEWID() AS Id, CAST(1 AS decimal(18, 3)) AS TotalCount, CAST(CASE WHEN IsCriticalEquipment = 'No' AND ((IsOverdue = 'Yes') OR

                         (IsPostponed = 'Yes')) THEN 1 ELSE 0 END AS decimal(18, 3)) AS OutstandingCount, DateOfMonth, DateOfYear, Quarter, IssueDate, Vessel

FROM            dbo.vw_GetAnalysisNumberOfPMSV2Workorders AS tbl

GO




CREATE VIEW [dbo].[vw_GetAnalysisPercentageOfOutstandingPMSWorkordersWithTotal]
  AS

     SELECT NEWID()                                                              as Id,
          CAST (1 as decimal(18, 3))                                     as To
talCount,
		  CAST(CASE WHEN IsCriticalEquipment = 'No' AND ((IsOverdue = 'Yes') OR (IsPostponed = 'Yes')) THEN 1 ELSE 0 END as decimal(18, 3))  as OutstandingCount,
           --CAST((SELECT COUNT(*) AS Expr1
           --      FROM dbo.vw_GetAnalysisNum
berOfPMSWorkorders AS InnerVW
           --      WHERE InnerVW.IssueDate = tbl.IssueDate
           --        AND InnerVW.IsCriticalEquipment = 'No'
           --        AND ((InnerVW.IsOverdue = 'Yes')
           --               OR (InnerVW.IsPostponed 
= 'Yes'))) as decimal(18, 2)) as OutstandingCount,
           DateOfMonth,
           DateOfYear,
           Quarter,
           IssueDate,
           Vessel
    FROM dbo.vw_GetAnalysisNumberOfPMSWorkorders AS tbl

GO

CREATE VIEW [dbo].[vw_GetAnalysisPercentageOfUnplannedMaintenance]

AS

SELECT 

	NEWID() AS Id, 

	CAST

    (

		(

			SELECT COUNT(Id) AS Expr1

			FROM dbo.vw_GetAnalysisNumberOfPMSWorkorders AS InnerVW

			WHERE (IssueDate = tbl.IssueDate) AND (Vessel = tbl.Vessel) AND (IsCriticalEquipment = tbl.IsCriticalEquipment) AND (IsUnScheduled = 'Yes')

				  AND 1 = CASE

							WHEN InnerVW.DateOfYear > 2021 THEN IIF(InnerVW.UnScheduledReason = 2, 1, 0)

							ELSE 1

						  END

		) AS decimal(18, 2)

	) AS UnscheduledCount,

	CAST(COUNT(Id) AS decimal(18, 2)) AS TotalCount,

	DateOfMonth,

	DateOfYear,

	Quarter,

	IsCriticalEquipment,

	Vessel,

	IssueDate

FROM dbo.vw_GetAnalysisNumberOfPMSWorkorders AS tbl

GROUP BY DateOfMonth, DateOfYear, Quarter, IsCriticalEquipment, Vessel, IssueDate

GO

CREATE view [dbo].[vw_GetAnalysisTotalAmountOfEnvironmentalManagementRecords] as

    select emr.EnvironmentalManagementRecordID,

           v.Name                                                                    as VesselDisplayName,

           emr.MonthEnum                                                             as Month,

           emr.Year                                                                  as Year,

           iif(emr.MonthEnum <= 3 and emr.MonthEnum >= 1, 'Q1',

               iif(emr.MonthEnum <= 6 and emr.MonthEnum >= 4, 'Q2',

                   iif(emr.MonthEnum <= 9 and emr.MonthEnum >= 7, 'Q3',

                       iif(emr.MonthEnum <= 12 and emr.MonthEnum >= 10, 'Q4', '')))) as Quarter,

           emrl.Amount,

           emg.Name                                                                  as 'Group',

           ema.Name                                                                  as Activity

    from EnvironmentalManagementRecords emr

             inner join Vessels v on v.VesselID = emr.VesselID and v.IsDeleted = 0

             inner join EnvironmentalManagementRecordLines emrl

                        on emrl.EnvironmentalManagementRecordID = emr.EnvironmentalManagementRecordID and

                           emrl.IsDeleted = 0

             inner join EnvironmentalManagementActivities ema

                        on ema.EnvironmentalManagementActivityID = emrl.EnvironmentalManagementActivityID and

                           ema.IsDeleted = 0

             inner join EnvironmentalManagementGroups emg

                        on emg.EnvironmentalManagementGroupID = ema.EnvironmentalManagementGroupID and emg.IsDeleted = 0

    where emr.IsDeleted = 0

GO

CREATE VIEW [dbo].[vw_GetCertificatesByAllValues]

AS

SELECT        dbo.CrewAvailableCertificates.Name, dbo.CrewCertificates.CrewCertificateID, dbo.CrewCertificates.CrewIdentityID, dbo.CrewCertificates.IssueDate, dbo.CrewCertificates.CrewAvailableCertificateID, 

                         dbo.CrewCertificates.ExpireDate, dbo.CrewCertificates.Comment, dbo.CrewCertificates.HasCertificate, dbo.CrewAvailableCertificates.Name AS CrewAvailableCertificateName, 

                         dbo.CrewAvailableCertificates.ValidityYear, dbo.CrewCertificates.IsDeleted, dbo.CrewAvailableCertificates.IsDeleted AS CACISDeleted, dbo.CrewAvailableCertificates.CertificateOrder

FROM            dbo.CrewAvailableCertificates INNER JOIN

                         dbo.CrewCertificates ON dbo.CrewAvailableCertificates.CrewAvailableCertificateID = dbo.CrewCertificates.CrewAvailableCertificateID

WHERE        (dbo.CrewCertificates.IsDeleted = 0) AND (dbo.CrewAvailableCertificates.IsDeleted = 0)

GO

CREATE VIEW [dbo].[vw_GetCompaniesByAllValues]

AS

SELECT        dbo.Companies.CompanyID, dbo.Companies.CompanyCategoryID, dbo.Companies.CountryID, dbo.Companies.PortID, dbo.Companies.Name, dbo.Companies.VAT, 

                         dbo.Companies.Address, dbo.Companies.WebSite, dbo.Companies.Phone1, dbo.Companies.Phone2, dbo.Companies.Phone3, dbo.Companies.Phone4, 

                         dbo.Companies.Fax1, dbo.Companies.Fax2, dbo.Companies.Email, dbo.Companies.Attention, dbo.Companies.Note, dbo.Companies.IsDeleted, 

                         dbo.Companies.IsSystemOwner, dbo.CompanyCategories.Name AS CompanyCategoryName, ISNULL(dbo.Countries.Name, '') AS CountryName, 

                         ISNULL(dbo.Ports.Name, '') AS PortName

FROM            dbo.Companies INNER JOIN

                         dbo.CompanyCategories ON dbo.Companies.CompanyCategoryID = dbo.CompanyCategories.CompanyCategoryID LEFT OUTER JOIN

                         dbo.Countries ON dbo.Companies.CountryID = dbo.Countries.CountryID LEFT OUTER JOIN

                         dbo.Ports ON dbo.Companies.PortID = dbo.Ports.PortID

WHERE        (dbo.CompanyCategories.IsDeleted <> 1) AND (dbo.Companies.IsDeleted <> 1) AND (dbo.Countries.IsDeleted <> 1) AND (dbo.Ports.IsDeleted <> 1) AND 

                         (dbo.Companies.IsSystemOwner = 0) OR

                         (dbo.Companies.CountryID IS NULL) OR

                         (dbo.Companies.PortID IS NULL)

GO

CREATE VIEW [dbo].[vw_GetCompanyContacts]

AS

SELECT        CompanyContactID, CompanyID, Name, Surname, Phone, Email, Title, JobTitle, IsDeleted

FROM            dbo.CompanyContacts

GO

CREATE VIEW [dbo].[vw_GetCrewBankDetailsByAllValues]

AS

SELECT        dbo.Currencies.Code AS CurrrencyCode, dbo.Currencies.Name AS CurrencyName, dbo.CrewBankDetails.CrewBankDetailID, dbo.CrewBankDetails.CrewIdentityID, dbo.CrewBankDetails.BankName, 

                         dbo.CrewBankDetails.BranchCode, dbo.CrewBankDetails.BranchName, dbo.CrewBankDetails.AccountNumber, dbo.CrewBankDetails.AccountName, dbo.CrewBankDetails.CurrencyID, dbo.CrewBankDetails.IBAN, 

                         dbo.CrewBankDetails.IsDeleted, dbo.CrewIdentities.Name + ' ' + dbo.CrewIdentities.Surname AS CrewNameSurname, dbo.CrewBankDetails.IsDefault

FROM            dbo.CrewBankDetails INNER JOIN

                         dbo.Currencies ON dbo.CrewBankDetails.CurrencyID = dbo.Currencies.CurrencyID INNER JOIN

                         dbo.CrewIdentities ON dbo.CrewBankDetails.CrewIdentityID = dbo.CrewIdentities.CrewIdentityID

WHERE        (dbo.Currencies.IsDeleted = 0) AND (dbo.CrewIdentities.IsDeleted = 0)

GO

CREATE VIEW [dbo].[vw_GetCrewITFSalariesByAllValues] AS 

SELECT

	dbo.VesselFlags.Name AS VesselFlagName,

	dbo.CrewWorkPositions.Name AS CrewWorkPositionName,

	dbo.CrewITFSalaries.Differential,

	dbo.CrewITFSalaries.BasicSalary,

	dbo.CrewITFSalaries.LeavePay,

	dbo.CrewITFSalaries.LeaveSubSistence,

	dbo.CrewITFSalaries.Total,

	dbo.CrewITFSalaries.IsDeleted,

	dbo.CrewITFSalaries.VesselFlagID,

	dbo.CrewITFSalaries.CrewWorkPositionID,

	dbo.CrewITFSalaries.CrewITFSalaryID,

	dbo.CrewITFSalaries.VacationDayCount,

	dbo.CrewITFSalaries.GuaranteedOvertime,

	dbo.CrewITFSalaries.OvertimeRate,

	dbo.CrewWorkPositions.WorkPositionOrder

FROM

	dbo.CrewITFSalaries

INNER JOIN dbo.CrewWorkPositions ON dbo.CrewITFSalaries.CrewWorkPositionID = dbo.CrewWorkPositions.CrewWorkPositionID

INNER JOIN dbo.VesselFlags ON dbo.CrewITFSalaries.VesselFlagID = dbo.VesselFlags.VesselFlagID

GO

CREATE VIEW [dbo].[vw_GetCrewManagerSectionsByAllValues]

AS

SELECT        dbo.Vessels.Name AS VesselName, dbo.CrewIdentities.CrewIdentityID, dbo.CrewManagerSections.ManagerUserID, dbo.CrewManagerSections.VesselID, 

                         dbo.CrewManagerSections.CrewManagerSectionID, dbo.CrewManagerSections.Score, dbo.CrewManagerSections.Comment, dbo.CrewManagerSections.IsDeleted, 

                         dbo.CrewManagerSections.SectionDate, dbo.Users.Name + ' ' + dbo.Users.Surname AS UserName

FROM            dbo.Vessels INNER JOIN

                         dbo.CrewManagerSections ON dbo.Vessels.VesselID = dbo.CrewManagerSections.VesselID INNER JOIN

                         dbo.CrewIdentities ON dbo.CrewManagerSections.CrewIdentityID = dbo.CrewIdentities.CrewIdentityID INNER JOIN

                         dbo.Users ON dbo.CrewManagerSections.ManagerUserID = dbo.Users.UserID

GO

CREATE VIEW [dbo].[vw_GetCrewOnVesselRulesByAllValues]

AS

SELECT        CWP1.Name AS WorkPositionName1, CWP2.Name AS WorkPositionName2, VesselRule.CrewOnVesselRuleID, VesselRule.CrewWorkPositionID1, VesselRule.MathOperator, VesselRule.FunctionName, 

                         VesselRule.CrewWorkPositionID2, VesselRule.MathControl, VesselRule.DateType, VesselRule.Value, VesselRule.ExceptionFunctionName, VesselRule.IsDeleted, VesselRule.Description

FROM            dbo.CrewOnVesselRules AS VesselRule INNER JOIN

                         dbo.CrewWorkPositions AS CWP1 ON VesselRule.CrewWorkPositionID1 = CWP1.CrewWorkPositionID LEFT OUTER JOIN

                         dbo.CrewWorkPositions AS CWP2 ON VesselRule.CrewWorkPositionID2 = CWP2.CrewWorkPositionID

GO

CREATE VIEW [dbo].[vw_GetCrewSeamanBooksByAllValues]

AS

SELECT        CSB.CrewSeamanBookID, CSB.CrewSeamanBookTypeID, CSB.CrewIdentityID, CSB.CrewCapabilityID, CSB.BookNo, CSB.RegistryNo, CSB.RegistryPort, CSB.IssueDate, CSB.ExpireDate, CSB.IsDeleted, 

                         CC.Name AS CrewCapabilityName, CSBT.Name AS CrewSeamanBookTypeName, CSB.VesselFlagID, CSB.StatusEnum, VF.Name AS Flag

FROM            dbo.CrewSeamanBooks AS CSB LEFT OUTER JOIN

                         dbo.CrewSeamanBookTypes AS CSBT ON CSB.CrewSeamanBookTypeID = CSBT.CrewSeamanBookTypeID LEFT OUTER JOIN

                         dbo.CrewCapabilities AS CC ON CSB.CrewCapabilityID = CC.CrewCapabilityID LEFT OUTER JOIN

                         dbo.VesselFlags AS VF ON CSB.VesselFlagID = VF.VesselFlagID

WHERE        (CSB.IsDeleted <> 1)

GO



CREATE VIEW [dbo].[vw_GetCrewTestsByAllValues]

AS

SELECT        dbo.CrewTests.CrewTestID, dbo.CrewTests.CrewIdentityID, dbo.CrewTests.VesselID, dbo.CrewTests.CrewAvailableTestID, dbo.CrewTests.TestDate, dbo.CrewTests.IsNegative, dbo.CrewTests.IsDeleted, 

                         dbo.Vessels.Name AS VesselName, dbo.CrewAvailableTests.Name AS CrewAvailableTestName, dbo.CrewTests.ExpireDate

FROM            dbo.CrewTests INNER JOIN

                         dbo.Vessels ON dbo.CrewTests.VesselID = dbo.Vessels.VesselID LEFT OUTER JOIN

                         dbo.CrewAvailableTests ON dbo.CrewTests.CrewAvailableTestID = dbo.CrewAvailableTests.CrewAvailableTestID

WHERE        (dbo.CrewTests.IsDeleted <> 1)

GO

CREATE VIEW [dbo].[vw_GetCrewVisasByAllValues]

AS

SELECT        dbo.CrewPassports.PassportNo, dbo.CrewVisas.CrewVisaID, dbo.CrewVisas.CrewPassportID, dbo.CrewVisas.Type, dbo.CrewVisas.IssueDate, dbo.CrewVisas.ExpireDate, dbo.CrewVisas.IsDeleted, 

                         dbo.CrewPassports.CrewIdentityID, dbo.CrewVisas.VisaCountryID, dbo.VisaCountries.Name AS VisaCountryName

FROM            dbo.CrewPassports INNER JOIN

                         dbo.CrewVisas ON dbo.CrewPassports.CrewPassportID = dbo.CrewVisas.CrewPassportID INNER JOIN

                         dbo.VisaCountries ON dbo.CrewVisas.VisaCountryID = dbo.VisaCountries.VisaCountryID

WHERE        (dbo.CrewVisas.IsDeleted <> 1)

GO

CREATE VIEW [dbo].[vw_GetCrewWorkPositionsByAllValues]

AS

SELECT        TOP (100) PERCENT dbo.CrewWorkPositions.CrewWorkPositionID, dbo.CrewWorkPositions.Name, dbo.CrewWorkPositions.RelationOnVessel, dbo.CrewWorkPositions.IsDeleted, 

                         dbo.CrewWorkPositions.WorkPositionOrder, dbo.CrewWorkPositions.DaysOfContract, dbo.CrewWorkPositions.IsWatchKeepingOfficer, dbo.CrewWorkPositions.IsLeavePay, 

                         dbo.CrewWorkPositionGroups.Name AS CrewWorkPositionGroupName, dbo.CrewWorkPositions.Abbreviation

FROM            dbo.CrewWorkPositions LEFT OUTER JOIN

                         dbo.CrewWorkPositionGroups ON dbo.CrewWorkPositions.CrewWorkPositionGroupID = dbo.CrewWorkPositionGroups.CrewWorkPositionGroupID

GO

CREATE VIEW [dbo].[vw_GetDocumentAndDocumentCategoryWithLogicalOrder] AS

WITH CategoryHierarchy AS (SELECT DocumentCategoryID,

                                  CAST(RIGHT(REPLICATE('0', 4) + CAST([OrderNo] AS VARCHAR(10)), 4) AS VARCHAR(8000)) AS HierarchicalOrder

                           FROM DocumentCategories

                           WHERE ParentID IS NULL

                             And IsDeleted = 0

                           UNION ALL

                           SELECT dc.DocumentCategoryID,

                                  CAST(ch.HierarchicalOrder +

                                       RIGHT(REPLICATE('0', 4) + CAST(dc.[OrderNo] AS VARCHAR(10)), 4) AS VARCHAR(8000))

                           FROM DocumentCategories dc

                                    INNER JOIN

                                CategoryHierarchy ch ON dc.ParentID = ch.DocumentCategoryID

                           Where IsDeleted = 0)

SELECT DocumentCategoryID, HierarchicalOrder

FROM CategoryHierarchy

GO

CREATE VIEW [dbo].[vw_GetQlikUserAttribute]
AS
SELECT   u.UserID AS ID, u.UserID AS userid, 'group' AS type, (select Value from Settings where Name = 'App.Qlik.CustomerName') AS value
FROM            dbo.Users AS u INNER JOIN
                         dbo.
UserGroups AS ug ON u.UserGroupID = ug.UserGroupID
WHERE        (ug.IsLoginFromVessel = 0) AND (u.IsActive = 1) AND (u.IsDeleted = 0)

GO


CREATE VIEW [dbo].[vw_GetQlikUsers]
AS
SELECT  u.UserID AS userid, u.Name + '_' + u.Surname AS name
FROM            dbo.Users AS u INNER JOIN
                         dbo.UserGroups AS ug ON u.UserGroupID = ug.UserGroupID
WHERE        (ug.IsLoginFromVess
el = 0) AND (u.IsActive = 1) AND (u.IsDeleted = 0)

GO

CREATE VIEW [dbo].[vw_GetSafetyAccidentforKPI]

AS

SELECT        dbo.SafetyAccidents.SafetyAccidentID, dbo.SafetyAccidentClassOfAccidents.Name AS ClassofAccident, dbo.Vessels.Name AS Vessel, dbo.SafetyAccidents.DateOfEvent, YEAR(dbo.SafetyAccidents.DateOfEvent) 

                         AS DateOfYear, MONTH(dbo.SafetyAccidents.DateOfEvent) AS DateOfMount

FROM            dbo.SafetyAccidents INNER JOIN

                         dbo.SafetyAccidentClassOfAccidents ON dbo.SafetyAccidents.SafetyAccidentClassOfAccidentID = dbo.SafetyAccidentClassOfAccidents.SafetyAccidentClassOfAccidentID INNER JOIN

                         dbo.Vessels ON dbo.SafetyAccidents.VesselID = dbo.Vessels.VesselID

WHERE        (dbo.SafetyAccidentClassOfAccidents.IsDeleted = 0) AND (dbo.SafetyAccidents.IsDeleted = 0) AND (dbo.Vessels.IsDeleted = 0)

GO

CREATE VIEW [dbo].[vw_PurchaseOrderBudgetReport]

as

select AII.Code,

       AII.Name,

       DATEPART(mm, PO.PurchaseOrderDate) Month,

       SUM((UnitPrice * PurchaseOrderQuantity) * (CASE WHEN PO.ExhangeRate IS NULL THEN 1 ELSE PO.ExhangeRate END)) Total

from PurchaseOrderItems

         INNER JOIN PurchaseOrders PO on PurchaseOrderItems.PurchaseOrderID = PO.PurchaseOrderID

         INNER JOIN AccountInvoiceItems AII on PurchaseOrderItems.BudgetItemID = AII.AccountInvoiceItemID

WHERE PO.IsDeleted = 0

  and PurchaseOrderItems.IsDeleted = 0

  AND AII.IsDeleted = 0

GROUP BY AII.Code, AII.Name, DATEPART(mm, PO.PurchaseOrderDate)

GO

CREATE VIEW [dbo].[vw_PurchaseOrderCostGroupReport]
AS
select NEWID() AS                                                                                  Id,
       AIICG.Name,
       DATENAME(month, PO.PurchaseOrderDate)                                  
                     DateOfMonthName,
       DATEPART(mm, PO.PurchaseOrderDate)                                                          DateOfMonth,
       Year(PO.PurchaseOrderDate)                                                                  DateOf
Year,
       SUM((UnitPrice * PurchaseOrderQuantity) * (IIF(PO.ExhangeRate IS NULL, 1, PO.ExhangeRate))) Total,
       convert(nvarchar(36), AIICG.AccountInvoiceItemCostGroupReportDefinationID) as AccountInvoiceItemCostGroupReportDefinationID,
       conv
ert(nvarchar(36), PO.VesselID) as VesselID
from PurchaseOrderItems
         INNER JOIN PurchaseOrders PO on PurchaseOrderItems.PurchaseOrderID = PO.PurchaseOrderID
         INNER JOIN AccountInvoiceItems AII on PurchaseOrderItems.BudgetItemID = AII.Accoun
tInvoiceItemID
         INNER JOIN AccountInvoiceItemCostGroupVsInvoiceItems AIICGVII
                    on AII.AccountInvoiceItemID = AIICGVII.AccountInvoiceItemID
         INNER JOIN AccountInvoiceItemCostGroups AIICG
                    on AIICGVII.Ac
countInvoiceItemCostGroupID = AIICG.AccountInvoiceItemCostGroupID
WHERE PO.IsDeleted = 0
  and PurchaseOrderItems.IsDeleted = 0
  AND AII.IsDeleted = 0
  AND AIICG.IsDeleted = 0
GROUP BY AIICG.Name, DATENAME(month, PO.PurchaseOrderDate), DATEPART(mm, PO.P
urchaseOrderDate),
         Year(PO.PurchaseOrderDate),
         AIICG.AccountInvoiceItemCostGroupReportDefinationID,
         PO.VesselID

GO

CREATE VIEW [dbo].[vw_ReviewSummaryOfTheStatusDeficiencies]
AS
SELECT dbo.SafetyANTBI.SafetyANTBIID, dbo.SafetyANTBITypeOfReports.Name AS ANTBITypeOfReport, dbo.SafetyANTBI.SafetyANTBITypeofFindingID AS TypeOfFindingID, dbo.SafetyANTBIReasons.Name AS ANTB
IReason, 
                  dbo.SafetyVettingGroups.Name AS ANTBIOriginGroup, dbo.SafetyANTBIRatings.Name AS ANTBIRating, dbo.SafetyANTBI.Chapter AS ANTBIChapter, dbo.SafetyANTBI.Section AS ANTBISection, 
                  dbo.SafetyVettingDefinations.Nam
e AS ANTBIOrigin, (CASE WHEN DATEDIFF(HH, IsNull(dbo.SafetyANTBI.CompletedDate, GetDate()), dbo.SafetyANTBI.ExpireDate) >= 0 AND SafetyANTBI.IsPostponed = 0 THEN 'Yes' ELSE 'No' END) 
                  AS CompletedAsScheduled, CASE dbo.SafetyANTBI.IsSuppo
rtRequest WHEN 1 THEN 'Yes' ELSE 'No' END AS ANTBIExternalSupportRequest, CASE WHEN SafetyANTBI.IsPostponed = 1 OR
                  DATEDIFF(HH, IsNull(dbo.SafetyANTBI.CompletedDate, GetDate()), dbo.SafetyANTBI.ExpireDate) < 0 THEN 'Yes' ELSE 'No' END AS
 IsOutstanding, CASE WHEN (SafetyANTBI.IsCompleted = 0 AND DATEDIFF(HH, 
                  IsNull(dbo.SafetyANTBI.CompletedDate, GetDate()), dbo.SafetyANTBI.ExpireDate) < 0) OR
                  (SafetyANTBI.IsCompleted = 0 AND SafetyANTBI.IsPostponed = 1
) THEN 'Yes' ELSE 'No' END AS IsOutstandingPending, CASE dbo.SafetyANTBI.IsDryDock WHEN 1 THEN 'Yes' ELSE 'No' END AS ANTBIIsDryDock, 
                  CASE dbo.SafetyANTBI.IsPostponed WHEN 1 THEN 'Yes' ELSE 'No' END AS ANTBIIsPostponed, CASE dbo.PMSMode
ls.IsCriticalEquipment WHEN 1 THEN 'Critical' ELSE 'Non critical' END AS IsCriticalEquipment, 
                  (CASE WHEN dbo.SafetyANTBI.IsSupportRequest = 1 THEN 'Yes' ELSE 'No' END) AS AssistanceRequiredExternalSupport, (CASE WHEN dbo.SafetyANTBI.IsS
parePartNeeded = 1 THEN 'Yes' ELSE 'No' END) 
                  AS AssistanceRequiredNewSpareParts, dbo.SafetyActionTaken.Name AS ANTBIActionTaken, dbo.SafetyANTBI.ExpireDate, dbo.Vessels.Name AS Vessel, YEAR(dbo.SafetyANTBI.ExpireDate) AS DateOfYear, DAT
ENAME(month, 
                  DATEADD(month, MONTH(dbo.SafetyANTBI.ExpireDate), - 1)) AS DateOfMonth, CASE WHEN MONTH(SafetyANTBI.ExpireDate) >= 1 AND MONTH(SafetyANTBI.ExpireDate) <= 3 THEN 'Q1' WHEN MONTH(SafetyANTBI.ExpireDate) >= 4 AND 
            
      MONTH(SafetyANTBI.ExpireDate) <= 6 THEN 'Q2' WHEN MONTH(SafetyANTBI.ExpireDate) >= 7 AND MONTH(SafetyANTBI.ExpireDate) <= 9 THEN 'Q3' WHEN MONTH(SafetyANTBI.ExpireDate) >= 10 AND MONTH(SafetyANTBI.ExpireDate) 
                  <= 12 THEN 'Q4' END A
S Quarter
FROM     dbo.SafetyANTBI LEFT OUTER JOIN
                  dbo.SafetyANTBIReasons ON dbo.SafetyANTBI.SafetyANTBIReasonID = dbo.SafetyANTBIReasons.SafetyANTBIReasonID INNER JOIN
                  dbo.SafetyANTBIRatings ON dbo.SafetyANTBI.SafetyAN
TBIRatingID = dbo.SafetyANTBIRatings.SafetyANTBIRatingID INNER JOIN
                  dbo.SafetyANTBITypeOfReports ON dbo.SafetyANTBI.SafetyANTBITypeOfReportID = dbo.SafetyANTBITypeOfReports.SafetyANTBITypeOfReportID INNER JOIN
                  dbo.Safet
yVettingGroups ON dbo.SafetyANTBI.SafetyVettingGroupID = dbo.SafetyVettingGroups.SafetyVettingGroupID INNER JOIN
                  dbo.Vessels ON dbo.SafetyANTBI.VesselID = dbo.Vessels.VesselID LEFT OUTER JOIN
                  dbo.SafetyActionTaken ON db
o.SafetyANTBI.SafetyActionTakenID = dbo.SafetyActionTaken.SafetyActionTakenID LEFT OUTER JOIN
                  dbo.SafetyVettingDefinations ON dbo.SafetyANTBI.SafetyVettingDefinationID = dbo.SafetyVettingDefinations.SafetyVettingDefinationID LEFT OUTER J
OIN
                  dbo.PMSEquipments ON dbo.SafetyANTBI.PMSEquipmentID = dbo.PMSEquipments.PMSEquipmentID LEFT OUTER JOIN
                  dbo.PMSModels ON dbo.PMSModels.PMSModelID = dbo.PMSEquipments.PMSModelID
WHERE  (dbo.SafetyANTBI.IsDeleted = 0)

GO

CREATE   view [dbo].[vw_WaitingForResponses] as

SELECT dbo.WaitingForResponses.WaitingForResponseID,

           CONVERT(nvarchar(50), dbo.WaitingForResponseConfigs.WaitingForResponseConfigID)                    AS WaitingForResponseConfigID,

           CONVERT(nvarchar(50),

                   dbo.WaitingForResponses.RowID)                                                             AS RowID,

           dbo.WaitingForResponses.IsCompleted,

           dbo.WaitingForResponses.CompletedDate,

           dbo.WaitingForResponses.IssueDate,

           dbo.WaitingForResponses.ExpiryDate,

           dbo.WaitingForResponses.RelatedUserID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.VesselID)                                            AS VesselID,

           dbo.WaitingForResponses.CreatedDate,

           dbo.WaitingForResponses.Name,

           CONVERT(nvarchar(50),

                   dbo.WaitingForResponses.CompletedUserID)                                                   AS CompletedUserID,

           dbo.WaitingForResponses.IsDeleted,

           dbo.WaitingForResponses.LastUpdatedBy,

           CONVERT(nvarchar(50),

                   dbo.WaitingForResponses.LastUpdatedOfficeUserID)                                           AS LastUpdatedOfficeUserID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.LastUpdatedVesselUserID)                             AS LastUpdatedVesselUserID,

           dbo.WaitingForResponses.LastUpdatedDate,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.SubRowID)                                            AS SubRowID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.DepartmentID)                                        AS DepartmentID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.StatusID)                                            AS StatusID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.RiskAssessmentTypeID)                                AS RiskAssessmentTypeID,

           dbo.Vessels.Name                                                                                   AS VesselName,

           dbo.WaitingForResponseConfigs.TableName                                                            AS WaitingForResponseConfigTableName,

           dbo.WaitingForResponseConfigs.PrimaryKey                                                           AS WaitingForResponseConfigPrimaryKey,

           dbo.WaitingForResponseConfigs.ActionPath                                                           AS WaitingForResponseConfigActionPath,

           dbo.Statuses.Name                                                                                  AS StatusName,

           CONVERT(nvarchar(150), dbo.Departments.Name)                                                       AS DepartmentName,

           dbo.Statuses.StatusColorCode                                                                       AS StatusColor,

           CONVERT(nvarchar(50),

                   CASE

                       WHEN dbo.WaitingForResponses.WaitingForResponseConfigID = 'FDC9EFC2-85FA-4672-99A8-07A9C7A46BA3'

                           THEN dbo.Requisitions.RequisitionTypeID

                       WHEN dbo.WaitingForResponses.WaitingForResponseConfigID = '6231C3F0-1461-4FE2-AAE4-3D95DAE2853C'

                           THEN rfqRequisitions.RequisitionTypeID

                       END)                                                                                   AS RequisitionTypeID,

           CONVERT(nvarchar(150), dbo.FollowUps.FollowUpTypeID)                                               as FollowUpTypeID,

           CONVERT(nvarchar(150), dbo.TechnicCertificateVsVessels.TechnicCertificateID)                       as TechnicCertificateID,

           CONVERT(nvarchar(150), dbo.TechnicCertificates.TechnicCertificateTypeID)                           as TechnicCertificateTypeID,

           CONVERT(nvarchar(150), dbo.SafetyMeetings.SafetyMeetingTypeID)                                     as SafetyMeetingTypeID,

           CONVERT(nvarchar(150), dbo.SafetyANTBI.SafetyVettingGroupID)                                       as SafetyVettingGroupID,

           CONVERT(nvarchar(150), dbo.SafetyANTBI.SafetyANTBITypeofFindingID)                                 as SafetyANTBITypeofFindingID,

           CONVERT(nvarchar(150), ras.Templates.CategoryID)                                                   as TemplateCategoryID,

           CONVERT(nvarchar(150), dbo.ModulStructures.LanguageSource + '.' +

                                  dbo.ModulStructures.DisplayName)                                            as ModuleStructureDisplayKey,

         dbo.ModulStructures.ModulStructureID



    FROM dbo.WaitingForResponses

             INNER JOIN

         dbo.WaitingForResponseConfigs

         ON dbo.WaitingForResponses.WaitingForResponseConfigID =

            dbo.WaitingForResponseConfigs.WaitingForResponseConfigID and dbo.WaitingForResponseConfigs.IsDeleted = 0

             LEFT OUTER JOIN

         dbo.Vessels ON dbo.Vessels.VesselID = dbo.WaitingForResponses.VesselID and dbo.Vessels.IsDeleted = 0

             LEFT OUTER JOIN

         dbo.Statuses ON dbo.WaitingForResponses.StatusID = dbo.Statuses.StatusID and dbo.Statuses.IsDeleted = 0

             LEFT OUTER JOIN

         dbo.Departments

         ON dbo.WaitingForResponses.DepartmentID = dbo.Departments.DepartmentID and dbo.Departments.IsDeleted = 0

             LEFT OUTER JOIN

         dbo.SafetyANTBI on dbo.WaitingForResponses.RowID = dbo.SafetyANTBI.SafetyANTBIID and

                            dbo.WaitingForResponses.WaitingForResponseConfigID =

                            '9BB8C579-9AD7-4FD0-8D1F-E4ED161FA22E' and dbo.SafetyANTBI.IsDeleted = 0

             LEFT OUTER JOIN

         dbo.Requisitions ON dbo.Requisitions.RequisitionID = dbo.WaitingForResponses.RowID and

                             dbo.WaitingForResponses.WaitingForResponseConfigID =

                             'FDC9EFC2-85FA-4672-99A8-07A9C7A46BA3' and dbo.Requisitions.IsDeleted = 0

             LEFT OUTER JOIN dbo.RequestForQuotations rfq

                             ON rfq.RequestForQuotationID = dbo.WaitingForResponses.RowID

                                 AND

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                '6231C3F0-1461-4FE2-AAE4-3D95DAE2853C' and rfq.IsDeleted = 0

             LEFT OUTER JOIN dbo.Requisitions rfqRequisitions

                             ON rfqRequisitions.RequisitionID = rfq.RequisitionID

                                 AND

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                '6231C3F0-1461-4FE2-AAE4-3D95DAE2853C' and rfqRequisitions.IsDeleted = 0

             LEFT OUTER JOIN dbo.FollowUps ON dbo.FollowUps.FollowUpID = dbo.WaitingForResponses.RowID and

                                              dbo.WaitingForResponses.WaitingForResponseConfigID =

                                              'CC81DBA3-B739-41D0-AA17-43880079E448' and dbo.FollowUps.IsDeleted = 0

             LEFT OUTER JOIN dbo.TechnicCertificateVsVessels

                             ON dbo.TechnicCertificateVsVessels.TechnicCertificateVsVesselID =

                                dbo.WaitingForResponses.RowID and

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                'C7DD808B-9F74-4B74-AD47-ACCD3ECD6897' and dbo.TechnicCertificateVsVessels.IsDeleted = 0

             LEFT OUTER JOIN dbo.TechnicCertificates ON dbo.TechnicCertificates.TechnicCertificateID =

                                                        dbo.TechnicCertificateVsVessels.TechnicCertificateID and

                                                        dbo.TechnicCertificates.IsDeleted = 0

             LEFT OUTER JOIN dbo.SafetyMeetings

     ON dbo.SafetyMeetings.SafetyMeetingID = dbo.WaitingForResponses.RowID and

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                'EE361E63-71E7-40B6-9E0F-4F328A65CE3F' and dbo.SafetyMeetings.IsDeleted = 0

             LEFT OUTER JOIN ras.Templates ON ras.Templates.TemplateID = dbo.WaitingForResponses.RowID and

                                              dbo.WaitingForResponses.WaitingForResponseConfigID =

                                              '23ABBCDB-C84D-4550-969C-3DE66AE9F006' and ras.Templates.IsDeleted = 0

             LEFT OUTER JOIN WaitingForResponseConfigVsModulStructures

                             ON WaitingForResponseConfigVsModulStructures.WaitingForResponseConfigID =

                                dbo.WaitingForResponses.WaitingForResponseConfigID and

                                WaitingForResponseConfigVsModulStructures.IsDeleted = 0

             LEFT OUTER JOIN ModulStructures

                             ON ModulStructures.ModulStructureID =

                                WaitingForResponseConfigVsModulStructures.ModulStructureID and

                                ModulStructures.IsDeleted = 0





    WHERE (dbo.WaitingForResponseConfigs.IsDeleted = 0

        and dbo.WaitingForResponses.IsCompleted != 1

        and dbo.WaitingForResponses.IsDeleted = 0)

GO

CREATE   view [dbo].[vw_WaitingForResponsesTEST] as

    SELECT dbo.WaitingForResponses.WaitingForResponseID,

           CONVERT(nvarchar(50), dbo.WaitingForResponseConfigs.WaitingForResponseConfigID) AS WaitingForResponseConfigID,

           CONVERT(nvarchar(50),

                   dbo.WaitingForResponses.RowID)                                          AS RowID,

           dbo.WaitingForResponses.IsCompleted,

           dbo.WaitingForResponses.CompletedDate,

           dbo.WaitingForResponses.IssueDate,

           dbo.WaitingForResponses.ExpiryDate,

           dbo.WaitingForResponses.RelatedUserID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.VesselID)                         AS VesselID,

           dbo.WaitingForResponses.CreatedDate,

           dbo.WaitingForResponses.Name,

           CONVERT(nvarchar(50),

                   dbo.WaitingForResponses.CompletedUserID)                                AS CompletedUserID,

           dbo.WaitingForResponses.IsDeleted,

           dbo.WaitingForResponses.LastUpdatedBy,

           CONVERT(nvarchar(50),

                   dbo.WaitingForResponses.LastUpdatedOfficeUserID)                        AS LastUpdatedOfficeUserID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.LastUpdatedVesselUserID)          AS LastUpdatedVesselUserID,

           dbo.WaitingForResponses.LastUpdatedDate,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.SubRowID)                         AS SubRowID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.DepartmentID)                     AS DepartmentID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.StatusID)                         AS StatusID,

           CONVERT(nvarchar(50), dbo.WaitingForResponses.RiskAssessmentTypeID)             AS RiskAssessmentTypeID,

           dbo.Vessels.Name                                                                AS VesselName,

           dbo.WaitingForResponseConfigs.TableName                                         AS WaitingForResponseConfigTableName,

           dbo.WaitingForResponseConfigs.PrimaryKey                                        AS WaitingForResponseConfigPrimaryKey,

           dbo.WaitingForResponseConfigs.ActionPath                                        AS WaitingForResponseConfigActionPath,

           dbo.Statuses.Name                                                               AS StatusName,

           CONVERT(nvarchar(150), dbo.Departments.Name)                                    AS DepartmentName,

           dbo.Statuses.StatusColorCode                                                    AS StatusColor,

           CONVERT(nvarchar(50),

                   CASE

                       WHEN dbo.WaitingForResponses.WaitingForResponseConfigID = 'FDC9EFC2-85FA-4672-99A8-07A9C7A46BA3'

                           THEN dbo.Requisitions.RequisitionTypeID

                       WHEN dbo.WaitingForResponses.WaitingForResponseConfigID = '6231C3F0-1461-4FE2-AAE4-3D95DAE2853C'

                           THEN rfqRequisitions.RequisitionTypeID

                       END)                                                                AS RequisitionTypeID,

           CONVERT(nvarchar(150), dbo.FollowUps.FollowUpTypeID)                            as FollowUpTypeID,

           CONVERT(nvarchar(150), dbo.TechnicCertificateVsVessels.TechnicCertificateID)    as TechnicCertificateID,

           CONVERT(nvarchar(150), dbo.TechnicCertificates.TechnicCertificateTypeID)        as TechnicCertificateTypeID,

           CONVERT(nvarchar(150), dbo.SafetyMeetings.SafetyMeetingTypeID)                  as SafetyMeetingTypeID,

           CONVERT(nvarchar(150), dbo.SafetyANTBI.SafetyVettingGroupID)                    as SafetyVettingGroupID,

           CONVERT(nvarchar(150), dbo.SafetyANTBI.SafetyANTBITypeofFindingID)              as SafetyANTBITypeofFindingID,

           CONVERT(nvarchar(150), ras.Templates.CategoryID)                                as TemplateCategoryID



    FROM dbo.WaitingForResponses

             INNER JOIN

         dbo.WaitingForResponseConfigs

         ON dbo.WaitingForResponses.WaitingForResponseConfigID =

            dbo.WaitingForResponseConfigs.WaitingForResponseConfigID

             LEFT OUTER JOIN

         dbo.Vessels ON dbo.Vessels.VesselID = dbo.WaitingForResponses.VesselID

             LEFT OUTER JOIN

         dbo.Statuses ON dbo.WaitingForResponses.StatusID = dbo.Statuses.StatusID

             LEFT OUTER JOIN

         dbo.Departments ON dbo.WaitingForResponses.DepartmentID = dbo.Departments.DepartmentID

             LEFT OUTER JOIN

         dbo.SafetyANTBI on dbo.WaitingForResponses.RowID = dbo.SafetyANTBI.SafetyANTBIID and

                            dbo.WaitingForResponses.WaitingForResponseConfigID = '9BB8C579-9AD7-4FD0-8D1F-E4ED161FA22E'

             LEFT OUTER JOIN

         dbo.Requisitions ON dbo.Requisitions.RequisitionID = dbo.WaitingForResponses.RowID and

                             dbo.WaitingForResponses.WaitingForResponseConfigID = 'FDC9EFC2-85FA-4672-99A8-07A9C7A46BA3'

             LEFT OUTER JOIN dbo.RequestForQuotations rfq

                             ON rfq.RequestForQuotationID = dbo.WaitingForResponses.RowID

                                 AND

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                '6231C3F0-1461-4FE2-AAE4-3D95DAE2853C'

             LEFT OUTER JOIN dbo.Requisitions rfqRequisitions

                             ON rfqRequisitions.RequisitionID = rfq.RequisitionID

                                 AND

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                '6231C3F0-1461-4FE2-AAE4-3D95DAE2853C'

             LEFT OUTER JOIN dbo.FollowUps ON dbo.FollowUps.FollowUpID = dbo.WaitingForResponses.RowID and

                                              dbo.WaitingForResponses.WaitingForResponseConfigID =

                                              'CC81DBA3-B739-41D0-AA17-43880079E448'

             LEFT OUTER JOIN dbo.TechnicCertificateVsVessels

                             ON dbo.TechnicCertificateVsVessels.TechnicCertificateVsVesselID =

                                dbo.WaitingForResponses.RowID and

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                'C7DD808B-9F74-4B74-AD47-ACCD3ECD6897'

             LEFT OUTER JOIN dbo.TechnicCertificates ON dbo.TechnicCertificates.TechnicCertificateID =

                                                        dbo.TechnicCertificateVsVessels.TechnicCertificateID

             LEFT OUTER JOIN dbo.SafetyMeetings

                             ON dbo.SafetyMeetings.SafetyMeetingID = dbo.WaitingForResponses.RowID and

                                dbo.WaitingForResponses.WaitingForResponseConfigID =

                                'EE361E63-71E7-40B6-9E0F-4F328A65CE3F'

             LEFT OUTER JOIN ras.Templates ON ras.Templates.TemplateID = dbo.WaitingForResponses.RowID and

                                              dbo.WaitingForResponses.WaitingForResponseConfigID =

                                              '23ABBCDB-C84D-4550-969C-3DE66AE9F006'





    WHERE (dbo.WaitingForResponseConfigs.IsDeleted = 0

        and dbo.WaitingForResponses.IsCompleted != 1

        and dbo.WaitingForResponses.IsDeleted = 0)

GO

 create view [dbo].[vwCompanyCertificates]

as

select CertificateTypes.Name as CertificateType ,dbo.Certificates.Name as Certificate,dbo.CompanyVsCertificates.IssueDate,dbo.CompanyVsCertificates.ExpireDate from CompanyVsCertificates 

inner join Companies on Companies.CompanyID = CompanyVsCertificates.CompanyID and CompanyVsCertificates.Isdeleted = 0

inner join CertificateTypes on CertificateTypes.CertificateTypeID = CompanyVsCertificates.CertificateTypeID and CertificateTypes.IsDeleted = 0 

inner join Certificates on Certificates.CertificateID = CompanyVsCertificates.CertificateID and Certificates.IsDeleted = 0

GO

create view dbo.vwCompanySegments

as



select Companies.Name as Company ,Segments.Name as CompanySegments ,CompanyVsSegments.StartDate as IssueDate,CompanyVsSegments.ExpireDate from CompanyVsSegments 

inner join Companies on Companies.CompanyID = CompanyVsSegments.CompanyID and Companies.IsDeleted = 0 

inner join Segments on Segments.SegmentID = CompanyVsSegments.SegmentID and Segments.IsDeleted=0 

where CompanyVsSegments.Isdeleted = 0

GO













CREATE view [dbo].[vwInventoryWithLatestPrices] as





    With _v as (

        Select v.VesselID

        From Vessels v

        where v.IsActive = 1

          and v.IsDeleted = 0

    ),

         _i as (

             Select i.InventoryID

             From Inventories i

             where (

                       i.IsDeleted = 0

                       )

         ),

         _il as (

             Select il.VesselID, il.InventoryLocationID

             From dbo.InventoryLocations as il

             Where (

                               il.IsDeleted = 0

                           and

                               (

                                   Exists(Select 1 From _v v where v.VesselID = il.VesselID)

                                   )

                       )

         ),

         _it as (

             Select it.InventoryTransactionTypeID, il.VesselID, it.InventoryLocationID, it.InventoryID, it.Quantity, it.UnitTypeID, itt.IsAnPositiveAction

             From dbo.InventoryTransactions as it

                      Inner Join _i i on (i.InventoryID = it.InventoryID)

                      Inner Join _il as il on (il.InventoryLocationID = it.InventoryLocationID)

                      Inner Join dbo.InventoryTransactionTypes as itt on (itt.IsDeleted = 0 and itt.IsAnActualTransaction = 1 and itt.InventoryTransactionTypeID = it.InventoryTransactionTypeID)

                  --inner join _v v on v.VesselID = il.VesselID

                  --inner join _mp mp on mp.InventoryID = it.InventoryID

             Where (

                       it.IsDeleted = 0

                       )

         ),

         _it_in as (

             Select it.VesselID, it.InventoryLocationID, it.InventoryID, it.Quantity, it.UnitTypeID

             From _it as it

             Where (

                       it.IsAnPositiveAction = 1

                       )

         ),

         _it_out as (

             Select it.VesselID, it.InventoryLocationID, it.InventoryID, it.Quantity, it.UnitTypeID

             From _it as it

             Where (

                       it.IsAnPositiveAction = 0

                       )

         ),

         _inStock as (

             Select it_in.VesselID, it_in.InventoryID, IsNull(Sum(it_in.Quantity), 0) as Quantity

             From _it_in as it_in

             Group By it_in.VesselID, it_in.InventoryID

         ),

         _outStock as (

             Select _it_out.VesselID, _it_out.InventoryID, IsNull(Sum(_it_out.Quantity), 0) as Quantity

             From _it_out as _it_out

             Group By _it_out.VesselID, _it_out.InventoryID

         ),

         _iStock as (

             Select /*IsNull(ins.Quantity,0) inQty, IsNull(outS.Quantity,0) outQty,*/ IsNull(ins.Quantity, 0) - IsNull(outS.Quantity, 0) as Quantity, inS.VesselID, inS.InventoryID /*, outS.VesselID VesselID2, outS.InventoryID InventoryID2*/

             From _inStock as inS

                      Full Join _outStock as outS on (outS.InventoryID = inS.InventoryID and outS.VesselID = inS.VesselID)

         ),

         _onBoard as (

             Select iStc.VesselID, i.InventoryID, IsNull(iStc.Quantity, 0) as Quantity

             From _iStock iStc

                      left Join _i as i on (iStc.InventoryID = i.InventoryID)

             --Where

             --(

             --    (IsNull(_inStock.Quantity, 0) - IsNull(_outStock.Quantity, 0)) > 0

             --)

         )

    select i.InventoryID                                                                         InventoryID,

           m.MaterialID                                                                          MaterialID,

		   m.MaterialCategoryID																	 MaterialCategoryID,

           i.Code                                                                                InventoryCode,

           m.Code                                                                                MaterialCode,

           m.Name                                                                                MaterialName,

           i.Barcode                                                                             Barcode,

           o.Quantity,

           o.VesselID,

           v.Name                                                                             as VesselName,

		   STUFF((SELECT '/' + CAST(_it.InventoryLocationID as nvarchar(36)) FROM _it WHERE _it.VesselID=v.VesselID and _it.InventoryID=i.InventoryID  FOR XML PATH('')), 1, 1, '') as InventoryLocationIDs,

		   STUFF((SELECT '/' + CAST(EquipmentID as nvarchar(36)) FROM pms.Equipment e with(nolock) WHERE e.VesselID=v.VesselID and e.InventoryID=i.InventoryID FOR XML PATH('')), 1, 1, '') as EquipmentIDs,

           ((SELECT TOP 1 Code

                  from PurchaseOrderItems Inner Join PurchaseOrders on PurchaseOrders.PurchaseOrderID=PurchaseOrderItems.PurchaseOrderID

                  WHERE PurchaseOrderItems.MaterialID = i.MaterialID

                    and PurchaseOrders.VesselID = v.VesselID

                  ORDER BY PurchaseOrders.PurchaseOrderDate DESC)) as PONo,

				        ((SELECT TOP 1 UnitPrice

                  from PurchaseOrderItems Inner Join PurchaseOrders on PurchaseOrders.PurchaseOrderID=PurchaseOrderItems.PurchaseOrderID

                  WHERE PurchaseOrderItems.MaterialID = i.MaterialID

                    and PurchaseOrders.VesselID = v.VesselID

                  ORDER BY PurchaseOrders.PurchaseOrderDate DESC)) as UnitPrice,

				    ((SELECT TOP 1 PurchaseOrderDate

                  from PurchaseOrderItems Inner Join PurchaseOrders on PurchaseOrders.PurchaseOrderID=PurchaseOrderItems.PurchaseOrderID

                  WHERE PurchaseOrderItems.MaterialID = i.MaterialID

                    and PurchaseOrders.VesselID = v.VesselID

                  ORDER BY PurchaseOrders.PurchaseOrderDate DESC)) as PurchaseOrderDate,

				    ((SELECT TOP 1 RequisitionTypes.Name

                  from PurchaseOrderItems Inner Join RequisitionItems on PurchaseOrderItems.RequisitionItemID=RequisitionItems.RequisitionItemID

				  Inner Join PurchaseOrders on PurchaseOrders.PurchaseOrderID=PurchaseOrderItems.PurchaseOrderID

				   Inner Join Requisitions on Requisitions.RequisitionID=RequisitionItems.RequisitionID

				    Inner Join RequisitionTypes on Requisitions.RequisitionTypeID=RequisitionTypes.RequisitionTypeID

                  WHERE PurchaseOrderItems.MaterialID = i.MaterialID

                    and PurchaseOrders.VesselID = v.VesselID

                  ORDER BY PurchaseOrders.PurchaseOrderDate DESC)) as RequisitionType



    from dbo.Inventories i

             inner join dbo.Materials m on (m.MaterialID = i.MaterialID)

             inner join _onBoard o on (o.InventoryID = i.InventoryID)

             inner join dbo.Vessels v on (o.VesselID = v.VesselID)

			

    where m.IsDeleted = 0

      and i.IsDeleted = 0

GO

CREATE   VIEW [dbo].[vwInventoryWithStorageLocations]

AS

WITH _v AS (SELECT VesselID

            FROM dbo.Vessels AS v

            WHERE (IsActive = 1)

              AND (IsDeleted = 0)),

     _i AS

         (SELECT InventoryID

          FROM dbo.Inventories AS i

          WHERE (IsDeleted = 0)),

     _il AS

         (SELECT VesselID, InventoryLocationID

          FROM dbo.InventoryLocations AS il

          WHERE (IsDeleted = 0)

            AND EXISTS

              (SELECT 1 AS Expr1

               FROM _v AS v

               WHERE (VesselID = il.VesselID))),

     _it AS

         (SELECT it.InventoryTransactionTypeID,

                 il.VesselID,

                 it.InventoryLocationID,

                 it.InventoryID,

                 it.Quantity,

                 it.UnitTypeID,

                 itt.IsAnPositiveAction

          FROM dbo.InventoryTransactions AS it

                   INNER JOIN

               _i AS i ON i.InventoryID = it.InventoryID

                   INNER JOIN

               _il AS il ON il.InventoryLocationID = it.InventoryLocationID

                   INNER JOIN

               dbo.InventoryTransactionTypes AS itt ON itt.IsDeleted = 0 AND itt.IsAnActualTransaction = 1 AND

                                                       itt.InventoryTransactionTypeID = it.InventoryTransactionTypeID

          WHERE (it.IsDeleted = 0)),

     _it_in AS

         (SELECT VesselID, InventoryLocationID, InventoryID, Quantity, UnitTypeID

          FROM _it AS it

          WHERE (IsAnPositiveAction = 1)),

     _it_out AS

         (SELECT VesselID, InventoryLocationID, InventoryID, Quantity, UnitTypeID

          FROM _it AS it

          WHERE (IsAnPositiveAction = 0)),

     _inStock AS

         (SELECT VesselID, InventoryID, ISNULL(SUM(Quantity), 0) AS Quantity, InventoryLocationID

          FROM _it_in AS it_in

          GROUP BY VesselID, InventoryID, InventoryLocationID),

     _outStock AS

         (SELECT VesselID, InventoryID, ISNULL(SUM(Quantity), 0) AS Quantity, InventoryLocationID

          FROM _it_out AS _it_out

          GROUP BY VesselID, InventoryID, InventoryLocationID),

     _iStock AS

         (SELECT ISNULL(inS.Quantity, 0) - ISNULL(outS.Quantity, 0) AS Quantity,

                 inS.VesselID,

                 inS.InventoryID,

                 inS.InventoryLocationID

          FROM _inStock AS inS

                   FULL OUTER JOIN

               _outStock AS outS ON outS.InventoryID = inS.InventoryID AND outS.VesselID = inS.VesselID AND

                                    outS.InventoryLocationID = inS.InventoryLocationID),

     _onBoard AS

         (SELECT iStc.VesselID, i.InventoryID, ISNULL(iStc.Quantity, 0) AS Quantity, iStc.InventoryLocationID

          FROM _iStock AS iStc

                   LEFT OUTER JOIN

               _i AS i ON iStc.InventoryID = i.InventoryID)

SELECT i.InventoryID,

       m.MaterialID,

       i.Code  AS InventoryCode,

       m.Code  AS MaterialCode,

       m.Name  AS MaterialName,

       i.Barcode,

       o.Quantity,

       o.VesselID,

       v.Name  AS VesselName,

       il.Name AS Location,

       il.InventoryLocationID,

       e.EquipmentID,

       e.ParentEquipmentID,

       e.Code  AS EquipmentCode,

       e.Name  AS EquipmentName,

       e.MinimumStockQuantity,

       u.Name  AS Unit,

       m.MaterialCategoryID

FROM dbo.Inventories AS i

         INNER JOIN

     dbo.Materials AS m ON m.MaterialID = i.MaterialID

         INNER JOIN

     _onBoard AS o ON o.InventoryID = i.InventoryID

         Left JOIN

     pms.Equipment AS e ON i.InventoryID = e.InventoryID AND e.IsDeleted = 0

         INNER JOIN

     dbo.Vessels AS v ON o.VesselID = v.VesselID

         INNER JOIN

     dbo.InventoryLocations AS il ON il.VesselID = o.VesselID AND il.InventoryLocationID = o.InventoryLocationID

         INNER JOIN

     dbo.PMSUnits AS u ON u.PMSUnitID = m.BaseUnitTypeID

WHERE (m.IsDeleted = 0)

  AND (i.IsDeleted = 0)

  AND (o.Quantity > 0)

GO

create view [dbo].[vwReportInvoiceItems] as
SELECT
ItemDescription as Item,
acc.Taxes.Name as Tax,
Quantity as Quantity,
PMSUnits.Name as Unit,
UnitPriceDocument as UnitPrice,
Discount as Discount,
NetPriceDocument as NetPrice,
LineTotalDocument as LineTo
talDocument
from acc.DocumentItems di 
left join acc.Documents invD on di.DocumentID=invD.DocumentID
left join acc.Taxes on Taxes.TaxID = di.TaxID and Taxes.IsDeleted=0
left join PMSUnits on PMSUnits.PMSUnitID=di.UnitID and PMSUnits.IsDeleted=0

GO

create view [dbo].[vwReportInvoiceMainDetail] as
SELECT
d.DocumentDate as InvoiceDate,
d.DocumentNo as InvoiceNo,
d.DocumentDueDate as DueDate,
Vessels.Name as Vessel,
Statuses.Name as Status,
Companies.Name as Company,
Currencies.Name as Currency,
acc.Do
cumentTypes.Name as InvoiceType,
acc.DocumentCategories.Name InvoiceCategory,
d.GrandTotalDocument as DocumentGrandTotal,
d.GrandTotalDefault as GrandTotalDefaultCurrency
from acc.Documents d 
left join acc.Documents invD on d.DocumentID=invD.DocumentID
l
eft join Vessels on Vessels.VesselID = invD.VesselID and Vessels.IsDeleted=0
left join Statuses on Statuses.StatusID = invD.StatusID and Statuses.IsDeleted=0
left join Companies on Companies.CompanyID = invD.CompanyID and Companies.IsDeleted=0
left join C
urrencies on Currencies.CurrencyID = invD.CurrencyID and Currencies.IsDeleted=0
left join acc.DocumentTypes on DocumentTypes.DocumentTypeID = invD.DocumentTypeID and DocumentTypes.IsDeleted=0
left join acc.DocumentCategories on DocumentCategories.Document
CategoryID = invD.DocumentCategoryID and DocumentCategories.IsDeleted=0

GO

CREATE VIEW [dbo].[vw_GetAnalysisAverageNumberOfIncidentInvestigationsByReporttypePerVessel123]

AS



 









SELECT NEWID() as Id,

       ReportType,

       DateOfYear,

       Quarter,

       DateOfMonth,

       CAST(COUNT(ReportType) AS decimal(18, 2)) as NumberOfReports



FROM (SELECT *,

             DATEADD(month, DATEDIFF(month, 0, innerVw.DateOfEvent), 0) AS StartOfMonth,

             EOMONTH(innerVw.DateOfEvent)                               as EndOfMonth

      FROM dbo.vw_GetAnalysisNumberOfIncidentInvestigations innerVw) AS tbl

GROUP BY ReportType, DateOfYear, DateOfMonth, Quarter, StartOfMonth, EndOfMonth

GO

CREATE view [dbo].[vw_GetAnalysisAverageAmountOfEnvironmentalManagementRecords] as

    select newID()                                                  as AverageAmountOfEnvironmentalManagementRecordID,

           *,

           iif(VesselCount = 0, Amount / 1, Amount / VesselCount)   as Average,

           iif(Month between 0 and 3, 'Q1',

               iif(Month between 4 and 6, 'Q2',

                   iif(Month between 7 and 9, 'Q3',

                       iif(Month between 10 and 12, 'Q4', 'N/A')))) as Quarter

    from (select [Group],

                 Activity,

                 Month,

                 Year,

                 sum(Amount) as Amount

          from (select ema.Name      as Activity,

                       emg.Name      as [Group],

                       emr.Year,

                       emr.MonthEnum as Month,

                       emrl.Amount

                from EnvironmentalManagementRecordLines emrl

                         inner join EnvironmentalManagementActivities ema

                                    on ema.EnvironmentalManagementActivityID =

                                       emrl.EnvironmentalManagementActivityID and

                                       ema.IsDeleted = 0

                         inner join EnvironmentalManagementGroups emg

                                    on emg.EnvironmentalManagementGroupID = ema.EnvironmentalManagementGroupID and

                                       emg.IsDeleted = 0

                         inner join EnvironmentalManagementRecords emr

                                    on emr.EnvironmentalManagementRecordID = emrl.EnvironmentalManagementRecordID and

                                       emr.IsDeleted = 0

                where emrl.IsDeleted = 0) emr

          group by [Group], Activity, Month, Year) tbl

             outer apply

         (SELECT COUNT(*) as VesselCount

          FROM dbo.fnGetVesselsByDateRange(CAST(CAST(Year AS varchar) + '-' +

                                                CAST(Month AS varchar) + '-' +

                                                CAST(1 AS varchar) AS DATETIME),

                                           CAST(EOMONTH(CAST(CAST(Year AS varchar) + '-' +

                                                             CAST(Month AS varchar) + '-' +

                                                             CAST(1 AS varchar) AS DATETIME)) as Datetime))) as Vessels

GO

CREATE VIEW [dbo].[vw_GetAnalysisAverageNumberOfBehaviorbasedObservationPerVessel]
  AS
       SELECT NEWID()                 as Id,
           CAST(COUNT(*) /
                CAST(
                  (CASE
                     WHEN (SELECT COUNT(*) AS Exp
r1 FROM dbo.fnGetVesselsByDateRange(StartOfMonth, EndOfMonth)) = 0
                             then 1
                     ELSE (SELECT COUNT(*) AS Expr1 FROM dbo.fnGetVesselsByDateRange(StartOfMonth, EndOfMonth)) END)
                  AS decimal(18, 2)
)
                AS decimal(18, 2)) AS AverageNumber,
           DateOfYear,
           DateOfMonth,
           Quarter
    FROM (SELECT Year(ObservationDate) DateOfYear,
                 DATENAME(month, DATEADD(month,
                                   
      MONTH(ObservationDate), -1))   AS DateOfMonth,
                 CASE
                   WHEN MONTH(ObservationDate) >= 1 AND MONTH(ObservationDate) <= 3 THEN 'Q1'
                   WHEN MONTH(ObservationDate)
                          >= 4 AND MONT
H(ObservationDate) <= 6 THEN 'Q2'
                   WHEN MONTH(ObservationDate) >= 7 AND MONTH(ObservationDate) <= 9 THEN 'Q3'
                   WHEN MONTH(ObservationDate) >= 10 AND
                        MONTH(ObservationDate) <= 12 THEN 'Q4' END    
      AS Quarter,
                 SafetyBehaviourBasedObservationID,
                 DATEADD(month, DATEDIFF(month, 0, ObservationDate), 0) AS StartOfMonth,
                 EOMONTH(ObservationDate)                               as EndOfMonth
          
FROM dbo.SafetyBehaviourBasedObservations where IsDeleted=0) AS tbl
    GROUP BY DateOfYear, DateOfMonth, Quarter, StartOfMonth, EndOfMonth

GO

CREATE VIEW [dbo].[vw_GetAnalysisAverageNumberOfIncidentInvestigationsByReporttypePerVessel]
  AS
    SELECT NEWID()              as Id,
           ReportType,
           DateOfYear,
           Quarter, 
           DateOfMonth,
           CAST(COUNT(Repor
tType) AS decimal(18, 2)) /
           CAST(
             (CASE
                WHEN (SELECT COUNT(*) AS Expr1 FROM dbo.fnGetVesselsByDateRange(StartOfMonth, EndOfMonth)) = 0
                        then 1
                ELSE (SELECT COUNT(*) AS Expr1 FR
OM dbo.fnGetVesselsByDateRange(StartOfMonth, EndOfMonth)) END)
             AS decimal(18, 2)) AS AverageNumber
    FROM (SELECT *, DATEADD(month, DATEDIFF(month, 0, innerVw.DateOfEvent), 0) AS StartOfMonth,
                    EOMONTH(innerVw.DateOfEvent
)                               as EndOfMonth
          FROM dbo.vw_GetAnalysisNumberOfIncidentInvestigations innerVw) AS tbl
    GROUP BY ReportType, DateOfYear, DateOfMonth, Quarter, StartOfMonth, EndOfMonth

GO

CREATE VIEW [dbo].[vw_GetAnalysisAverageNumberOfCrewRankPerVessel]
  AS
    select * from (SELECT NEWID()                                                 as Id,
           CAST(COUNT(*) / CAST((CASE
                                   WHEN (SELECT COUNT(*)
 AS Expr1
                                         FROM dbo.fnGetVesselsByDateRange(CAST(DateOfYear as nvarchar(10)) + '-01-01',
                                                                          CAST(DateOfYear as nvarchar(10)) + '-12-31')) =
    
                                    0 THEN 1
                                   ELSE (SELECT COUNT(*) AS Expr1
                                         FROM dbo.fnGetVesselsByDateRange(CAST(DateOfYear as nvarchar(10)) + '-01-01',
                         
                                                 CAST(DateOfYear as nvarchar(10)) + '-12-31'))
               END) AS
                                decimal(18, 2)) AS decimal(18, 2)) AS AverageNumber,
           DateOfYear,
                             
                                         CrewRank
    FROM (SELECT dbo.CrewWorkPositions.Name          AS CrewRank,
                 dbo.CrewExperiences.StartDate,
                 dbo.Vessels.Name                    AS Vessel,
                 YEAR(dbo.C
rewExperiences.StartDate) AS DateOfYear
          FROM dbo.CrewExperiences
                 INNER JOIN dbo.Vessels ON dbo.CrewExperiences.VesselID = dbo.Vessels.VesselID
                 INNER JOIN dbo.CrewWorkPositions
                   ON dbo.CrewExper
iences.CrewWorkPositionID = dbo.CrewWorkPositions.CrewWorkPositionID
          WHERE (dbo.CrewExperiences.IsDeleted = 0)
            AND (dbo.Vessels.IsDeleted = 0)
            AND (dbo.CrewWorkPositions.IsDeleted = 0)) AS tbl
    GROUP BY DateOfYear, Cre
wRank) tbl

GO

CREATE VIEW dbo.vwPorts

as

SELECT dbo.Countries.Name AS Country, dbo.Countries.ISOCode, dbo.Ports.Name AS Port, dbo.Ports.Latitude, dbo.Ports.Longitude, dbo.Ports.PortCode, dbo.Ports.PortID,

dbo.fnCalculateCoordinatesDecimal(dbo.Ports.Latitude) as LatitudeDecimal,dbo.fnCalculateCoordinatesDecimal(dbo.Ports.Longitude) as LongitudeDecimal

FROM     dbo.Ports LEFT OUTER JOIN

                  dbo.Countries ON dbo.Ports.CountryID = dbo.Countries.CountryID

WHERE  (dbo.Countries.IsDeleted = 0) AND (dbo.Ports.IsDeleted = 0)

GO



--CREATE SCHEMA [acc]

CREATE view  [acc].[BudgetExpenseMonthly] as



With _aii as (

    Select Cast(aii.AccountInvoiceItemID as VarChar(Max)) as Path, aii.AccountInvoiceItemID

    From dbo.AccountInvoiceItems as aii

    Where

    (

        aii.ParentID is null

    )

Union All

    Select Cast(c.Path +  Cast(aii.AccountInvoiceItemID as varchar(Max)) as VarChar(Max)) as Path, aii.AccountInvoiceItemID

    From dbo.AccountInvoiceItems as aii

        join _aii as c on (aii.ParentID = c.AccountInvoiceItemID)

),

_root as (

    Select Substring(Path,1,36) [RootAccountInvoiceItemID] , AccountInvoiceItemID  from _aii a

        where a.Path is not null and a.Path  != '' and Len(a.Path) >= 36

        and NOT EXISTS(Select 1 From AccountInvoiceItems where ParentID = a.AccountInvoiceItemID)

),

_amount as (

    Select ai.AccountInvoiceItemID, ai.AccountInvoiceID, ai.InvoiceDate, ai.Amount, ai.VesselID from AccountInvoices ai

),

_subTotalByDate as (

    Select r.RootAccountInvoiceItemID, YEAR(InvoiceDate) InvoiceYear, DateName(month,Dateadd(month,MONTH(InvoiceDate),-1)) as InvoiceMonth, SUM(a.Amount) TotalAmount, VesselID,MONTH(InvoiceDate) as InvoiceMonthNo from _amount a inner join _root r on (a.Ac
countInvoiceItemID = r.AccountInvoiceItemID)

    Group By 

    r.RootAccountInvoiceItemID, YEAR(InvoiceDate), MONTH(InvoiceDate), VesselID

)



 



Select budget.Code Code, budget.Name BudgetItem, sub.TotalAmount Amount, v.Name Vessel, sub.InvoiceYear, sub.InvoiceMonth, sub.InvoiceMonthNo

from _subTotalByDate sub 

    inner join AccountInvoiceItems budget on (sub.RootAccountInvoiceItemID = budget.AccountInvoiceItemID)

    inner join Vessels v on (sub.VesselID = v.VesselID)

GO



CREATE View [pms].[vwJobPlan]



as 

 

select pms.JobPoolVersions.Code as JobCode ,pms.JobPlans.LastCompletedDate,pms.JobPlans.LastCompletedCounter ,pms.JobPlans.HasActiveWorkOrder ActiveWorkOrder,pms.JobPlans.NextDueDate,pms.JobPlans.NextDueCounter,dbo.Vessels.name as Vessel ,pms.Equipment.Na
me as Equipment from pms.JobPlans join pms.Equipment on pms.JobPlans.EquipmentID = pms.Equipment.EquipmentID and pms.Equipment.IsDeleted=0

left join pms.EquipmentVsPriorityTypes on pms.JobPlans.EquipmentID = pms.EquipmentVsPriorityTypes.EquipmentID and EquipmentVsPriorityTypes.IsDeleted = 0

join pms.JobPoolVersions on pms.JobPlans.JobPoolID = pms.JobPoolVersions.JobPoolID 

join dbo.Vessels on pms.JobPlans.VesselID = dbo.Vessels.VesselID

GO

CREATE view [pms].[vw_WorkOrderCondition] as
    select pms.WorkOrders.WorkOrderID                                              as Id,
           CONVERT(nvarchar(50), pms.WorkOrders.JobCategoryID)                     as JobCategoryID,
           CONVERT(
nvarchar(50), pms.WorkOrders.JobTypeID)                         as JobTypeID,
           CONVERT(nvarchar(50), pms.EquipmentVsPriorityTypes.PriorityTypeID)      as EquipmentPriorityTypeID,
           CONVERT(nvarchar(50), pms.JobPoolVersionVsPriorityTypes
.PriorityTypeID) as JobPriorityTypeID,
           CONVERT(nvarchar(50), pms.WorkOrders.WorkOrderResultID)                 as WorkOrderResultID,
           pms.WorkOrders.IsOverdue,
           pms.WorkOrders.IsEquipmentFailed,
           CONVERT(nvarchar(5
0), pms.WorkOrderManPower.WorkOrderManPowerID)        as WorkOrderManPowerID,
           pms.JobPoolVersions.IsApprovalRequired                                  as IsApprovalRequired
    from pms.WorkOrders
             LEFT OUTER JOIN pms.WorkOrderVsEqui
pmentPriorityTypes
                             on pms.WorkOrders.WorkOrderID = pms.WorkOrderVsEquipmentPriorityTypes.WorkOrderID and
                                pms.WorkOrderVsEquipmentPriorityTypes.IsDeleted = 0
             LEFT OUTER JOIN pms.Equi
pmentVsPriorityTypes
                             on pms.WorkOrderVsEquipmentPriorityTypes.EquipmentVsPriorityTypeID =
                                pms.EquipmentVsPriorityTypes.EquipmentVsPriorityTypeID and
                                pms.Equipment
VsPriorityTypes.IsDeleted = 0
             LEFT OUTER JOIN pms.JobPoolVersionVsPriorityTypes
                             on pms.WorkOrders.JobPoolVersionID = pms.JobPoolVersionVsPriorityTypes.JobPoolVersionID and
                                pms.JobPo
olVersionVsPriorityTypes.IsDeleted = 0
             LEFT OUTER join pms.WorkOrderManPower on WorkOrders.WorkOrderID = WorkOrderManPower.WorkOrderID and
                                                      pms.WorkOrderManPower.IsDeleted = 0
             
LEFT OUTER join pms.JobPoolVersions
                             on WorkOrders.JobPoolVersionID = pms.JobPoolVersions.JobPoolVersionID and
                                pms.JobPoolVersions.IsDeleted = 0
    WHERE pms.WorkOrders.IsDeleted = 0

GO

Create view  [pms].[SparePartReport] as

With 

		_v as (

			Select v.VesselID From Vessels v where v.IsActive = 1 and v.IsDeleted = 0

		),

		_equipment as (

		Select e.EquipmentID, e.ParentEquipmentID, e.InventoryID From pms.Equipment e where e.IsDeleted = 0 

		and 

			e.StructureTypeID = '09EC11E3-9C44-4A73-9D77-5336A67C95DC'

		),

		_i as (

			Select i.InventoryID  From Inventories i 

			where 

			(

				i.IsDeleted = 0 and

				Exists(Select 1 from _equipment e where e.InventoryID = i.InventoryID)

			)

		), 

		_il as (

			Select il.VesselID, il.InventoryLocationID From dbo.InventoryLocations as il

			Where

			(

				il.IsDeleted = 0

			and

				(

					Exists(Select 1 From _v v where v.VesselID = il.VesselID)

				)

			)

		), _it as (

			Select it.InventoryTransactionTypeID, il.VesselID, it.InventoryLocationID, it.InventoryID, it.Quantity, it.UnitTypeID, itt.IsAnPositiveAction

			From dbo.InventoryTransactions as it

				Inner Join _i i on (i.InventoryID = it.InventoryID)

				Inner Join _il as il on (il.InventoryLocationID = it.InventoryLocationID)

				Inner Join dbo.InventoryTransactionTypes as itt on (itt.IsDeleted = 0 and itt.IsAnActualTransaction = 1 and itt.InventoryTransactionTypeID = it.InventoryTransactionTypeID)

				--inner join _v v on v.VesselID = il.VesselID

				--inner join _mp mp on mp.InventoryID = it.InventoryID

			Where

			(

				it.IsDeleted = 0

			)

		), _it_in as (

			Select it.VesselID, it.InventoryLocationID, it.InventoryID, it.Quantity, it.UnitTypeID

			From _it as it

			Where

			(

				it.IsAnPositiveAction = 1

			)

		), _it_out as (

			Select it.VesselID, it.InventoryLocationID, it.InventoryID, it.Quantity, it.UnitTypeID

			From _it as it

			Where

			(

				it.IsAnPositiveAction = 0

			)

		), _inStock as (

			Select it_in.VesselID, it_in.InventoryID, IsNull(Sum(it_in.Quantity), 0) as Quantity

			From _it_in as it_in

			Group By it_in.VesselID, it_in.InventoryID

		), _outStock as (

			Select _it_out.VesselID, _it_out.InventoryID, IsNull(Sum(_it_out.Quantity), 0) as Quantity

			From _it_out as _it_out

			Group By _it_out.VesselID, _it_out.InventoryID

		), 

		_iStock as (

				Select /*IsNull(ins.Quantity,0) inQty, IsNull(outS.Quantity,0) outQty,*/ IsNull(ins.Quantity,0) -  IsNull(outS.Quantity,0) as Quantity, inS.VesselID, inS.InventoryID /*, outS.VesselID VesselID2, outS.InventoryID InventoryID2*/ From _inStock as inS

				Full Join _outStock as outS on (outS.InventoryID = inS.InventoryID and outS.VesselID = inS.VesselID)

		),

		_onBoard as (

			Select iStc.VesselID, i.InventoryID, IsNull(iStc.Quantity, 0) as Quantity

			From _iStock iStc 

				left Join _i as i  on (iStc.InventoryID = i.InventoryID)

				

			--Where

			--(

			--	(IsNull(inS.Quantity, 0) - IsNull(outS.Quantity, 0)) > 0

			--)

		)





select Materials.Name as SparePart,

pms.Equipment.DrawingNo,

pms.Equipment.MinimumStockQuantity,

pms.Equipment.EquipmentID,

pms.Equipment.ParentEquipmentID,

Materials.Code as PartNo,

pms.EquipmentVsPriorityTypes.EquipmentVsPriorityTypeID,

o.Quantity,

o.VesselID,

v.Name as VesselName

from

    (((pms.Equipment

LEFT join pms.EquipmentVsPriorityTypes on (pms.Equipment.EquipmentID = pms.EquipmentVsPriorityTypes.EquipmentID))

inner join dbo.Inventories Inventories on (Inventories.InventoryID = pms.Equipment.InventoryID))

inner join dbo.Materials Materials on (Materials.MaterialID = Inventories.MaterialID))

inner join _onBoard o on (o.InventoryID = pms.Equipment.InventoryID and o.VesselID = pms.Equipment.VesselID)

inner join dbo.Vessels v on (v.VesselID = pms.Equipment.VesselID and o.VesselID = v.VesselID)

where ((pms.Equipment.IsDeleted = 0)

and (Materials.IsDeleted = 0)

and (Inventories.IsDeleted = 0)

and (pms.Equipment.StructureTypeID = N'09EC11E3-9C44-4A73-9D77-5336A67C95DC'))

GO





CREATE view [pms].[vwWorkOrders]

as



select TOP 10 WOPriority.Name as WOPriorityName,EquipmentPriority.Name as EquipmentPriorityName,dbo.Vessels.Name as Vessel,pms.WorkOrders.WOCode,pms.WorkOrders.IsCompleted ,pms.WorkOrders.IssueDate,pms.WorkOrders.WOName as WorkOrder,pms.WorkOrders.IsOverd
ue,pms.WorkOrders.CompletedDate,pms.WorkOrders.CompletedCounter,pms.WorkOrders.DueDate,pms.WorkOrders.DueCounter,pms.Equipment.Name as Equipment,pms.Equipment.Code as EquipmentCode,pms.JobPoolVersions.Code as JobCode,pms.OverdueReasons.Name as OverdueReas
on,pms.UnplannedReasons.Name as UnplannedReason ,pms.WorkOrders.IsUnplanned from pms.WorkOrders 

join pms.Equipment on pms.WorkOrders.EquipmentID = pms.Equipment.EquipmentID and pms.Equipment.IsDeleted =0

join dbo.Vessels on dbo.Vessels.VesselID = pms.WorkOrders.VesselID and Vessels.IsDeleted = 0

join pms.JobPoolVersions on pms.JobPoolVersions.JobPoolVersionID = pms.WorkOrders.JobPoolVersionID and pms.JobPoolVersions.IsDeleted = 0

left join pms.OverdueReasons on pms.OverdueReasons.OverDueReasonID = pms.WorkOrders.OverdueReasonID and pms.OverdueReasons.IsDeleted =0

left join pms.UnplannedReasons on pms.UnplannedReasons.UnplannedReasonID = pms.WorkOrders.UnplannedReasonID and pms.UnplannedReasons.IsDeleted = 0

left join pms.WorkOrderVsPriorityTypes on pms.WorkOrderVsPriorityTypes.WorkOrderID = pms.WorkOrders.WorkOrderID and  pms.WorkOrderVsPriorityTypes.IsDeleted= 0

left join pms.PriorityTypes as WOPriority on WOPriority.PriorityTypeID = pms.WorkOrderVsPriorityTypes.PriorityTypeID

left join pms.WorkOrderVsEquipmentPriorityTypes on pms.WorkOrderVsEquipmentPriorityTypes.WorkOrderID = pms.WorkOrders.WorkOrderID and  pms.WorkOrderVsEquipmentPriorityTypes.IsDeleted= 0

left join pms.PriorityTypes as EquipmentPriority on EquipmentPriority.PriorityTypeID = pms.WorkOrderVsEquipmentPriorityTypes.EquipmentVsPriorityTypeID



--left join pms.WorkOrderVsEquipmentPriorityTypes on pms.WorkOrderVsEquipmentPriorityTypes.WorkOrderID =pms.WorkOrders.WorkOrderID and pms.WorkOrderVsEquipmentPriorityTypes.IsDeleted = 0

--left join pms.WorkOrderVsEquipmentPriorityTypes on pms.PriorityTypes.PriorityTypeID = WorkOrderVsPriorityTypes.PriorityTypeID and pms.PriorityTypes.IsDeleted =0

--and pms.PriorityTypes.PriorityTypeID =WorkOrderVsEquipmentPriorityTypes.EquipmentVsPriorityTypeID

--left join (select PriorityTypeID from pms.PriorityTypes) as T on T.PriorityTypeID = pms.WorkOrderVsEquipmentPriorityTypes.PriorityTypeID

GO



CREATE view [proc].[vwQuotationItems]

as

select *, UnitPrice * QuotationQuantity as TotalPrice

from (

         select QUO.Code                                        as QuotationNo

              , RFQ.Code                                        as RFQNo

              , Materials.Code

              , Materials.Name                                  as Material

              , PMSUnits.Name                                   as Unit

              , QUOI.QuotationQuantity

              , QUO.QuotationID

              , QUOI.QuotationItemID

              , Companies.Name                                  as Company

              , (Select top (1) Value

                 from CurrencyParities

                 where CurrencyParities.CurrencyCode1 = Currencies.Code

                   and CurrencyParities.CurrencyCode2 = 'USD'

                   and CurrencyParities.[CurrencyDate] < QuotationDate

                 order by [CurrencyDate] desc) * QUOI.UnitPrice as UnitPrice



         from QuotationItems as QUOI

                  inner join Quotations as QUO on QUO.QuotationID = QUOI.QuotationID and QUOI.IsDeleted = 0

                  inner join RequestForQuotations as RFQ on RFQ.RequestForQuotationID = QUO.RequestForQuotationID and QUOI.IsDeleted = 0

                  inner join Materials on Materials.MaterialID = QUOI.MaterialID and Materials.IsDeleted = 0

                  inner join PMSUnits on QUOI.VendorUnitID = PMSUnits.PMSUnitID and PMSUnits.IsDeleted = 0

                  inner join Companies on Companies.CompanyID = QUO.VendorID and Companies.Isdeleted = 0

                  inner join Currencies on Currencies.CurrencyID = QUO.CurrencyID and Currencies.Isdeleted = 0

         where QUOI.IsDeleted = 0) as QvwUO

GO

CREATE view [proc].[vwRequestForQuotation]

    as

        select -- Companies.Name as   Company

             Ports.Name     as   Port

             ,RequestForQuotations.Code as RFQNo

             ,RequestForQuotations.Duedate

          --   ,RequestForQuotationItems.RequestQuantity

             ,RequestForQuotations.Title as Subject

             --, Companies.Address as VendorAddress

             --, Companies.Phone1

             --, Companies.Phone2

        

             ,Users.Name

             ,Users.Surname

             ,Users.Email

            -- ,E.Code as EquipmentCode

           --  ,E.Name as EquipmentName

          --   ,E.DrawingNo

          --   ,E.Hierarchy

          --   ,E.Model

         --    ,E.SerialNo

             ,RequestForQuotations.ReferenceInformation

         --    ,Materials.Code as MaterialCode

         --    ,Materials.Name as MaterialName

        --     ,Materials.Description MaterialDescription

			 ,RequestForQuotations.RequestForQuotationID

			 ,RequestForQuotations.Description

            

            



 



        from RequestForQuotations

              

                 --inner join RequestForQuotationItems on RequestForQuotationItems.RequestForQuotationID = RequestForQuotations.RequestForQuotationID

                 Left outer join Ports on Ports.PortID = RequestForQuotations.ExpectedDeliveryPortID and Ports.IsDeleted = 0

                 --inner join Companies on Companies.CompanyID = Quotations.VendorID and Companies.IsDeleted = 0 --

                 inner join Users on Users.UserID = RequestForQuotations.RequstedByUserID and Users.IsDeleted = 0

               --  left join  pms.Equipment as E on E.EquipmentID = RequestForQuotations.PMSEquipmentID and E.IsDeleted = 0

               --  inner join dbo.Materials on Materials.MaterialID = RequestForQuotationItems.MaterialID and Materials.IsDeleted = 0

             --    inner join QuotationItems on QuotationItems.RequestForQuotationItemID = RequestForQuotationItems.RequestForQuotationItemID 

            

            

        where RequestForQuotations.IsDeleted=0

GO



CREATE view [proc].[vwRequestForQuotationItems]

as





select RFQ.Code       as RFQNo,

       Ports.Name     as Port,

       Materials.Code,

       Materials.Name as Material,

       PMSUnits.Name  as Unit,

       RFQI.RequestQuantity,

       Vessels.Name   as Vessel,

       RFQ.RequestForQuotationID,

       RFQI.RequestForQuotationItemID,

       Materials.MaterialID,

	   Vessels.IMONo   ,

       RFQ.RequestDate

from RequestForQuotationItems as RFQI

         inner join RequestForQuotations as RFQ on RFQ.RequestForQuotationID = RFQI.RequestForQuotationID and RFQ.IsDeleted = 0

         inner join Requisitions on Requisitions.RequisitionID = RFQ.RequisitionID and Requisitions.IsDeleted = 0

         inner join Materials on Materials.MaterialID = RFQI.MaterialID and Materials.IsDeleted = 0

         inner join PMSUnits on RFQI.RequestUnitID = PMSUnits.PMSUnitID and PMSUnits.IsDeleted = 0

         inner join Vessels on Requisitions.VesselID = Vessels.VesselID and Vessels.IsDeleted = 0

         Left outer join Ports on Ports.PortID = RFQ.ExpectedDeliveryPortID and Ports.IsDeleted = 0

where RFQI.IsDeleted = 0

GO

CREATE VIEW [proc].[vwPurchaseOrders]
    AS
SELECT        dbo.Companies.Name AS Company, dbo.Ports.Name AS Port, dbo.Vessels.Name AS Vessel, dbo.PurchaseOrders.Code AS POCode, dbo.Companies.CompanyID, dbo.Companies.Address AS VendorAddress, 
            
             dbo.Companies.Phone1, dbo.Companies.Phone2, dbo.PurchaseOrders.PurchaseOrderID, dbo.PurchaseOrders.PurchaseOrderDate, dbo.PurchaseOrders.DueDate, dbo.PurchaseOrders.CurrencyID, 
                         dbo.PurchaseOrders.DeliveryPortID, dbo.
PurchaseOrders.Title, dbo.RequisitionPriorities.Name AS Priority, dbo.Vessels.IMONo, dbo.Quotations.Code AS QuotationCode, dbo.Quotations.QuotationDate, 
                         dbo.Users.Name, dbo.Users.Surname, dbo.Users.Email, E.Code, E.Name AS Equipm
entName, E.DrawingNo, E.Hierarchy, E.Model, E.SerialNo,PurchaseOrders.ReferenceInformation
FROM            dbo.PurchaseOrders INNER JOIN
                         dbo.Vessels ON dbo.PurchaseOrders.VesselID = dbo.Vessels.VesselID AND dbo.Vessels.IsDeleted =
 0 INNER JOIN
                 
                         dbo.RequisitionPriorities ON dbo.PurchaseOrders.PurchaseOrderPriorityID = dbo.RequisitionPriorities.RequisitionPriorityID LEFT OUTER JOIN
                         dbo.Ports ON dbo.Ports.PortID = dbo
.PurchaseOrders.DeliveryPortID AND dbo.Ports.IsDeleted = 0 INNER JOIN
                         dbo.Companies ON dbo.Companies.CompanyID = dbo.PurchaseOrders.VendorID AND dbo.Companies.IsDeleted = 0 LEFT OUTER JOIN
                         dbo.Quotations O
N dbo.Quotations.QuotationID = dbo.PurchaseOrders.QuotationID AND dbo.Quotations.IsDeleted = 0 LEFT OUTER JOIN
                         dbo.Users ON dbo.Users.UserID = dbo.PurchaseOrders.OrderedByUserID AND dbo.Users.IsDeleted = 0 LEFT OUTER JOIN
        
                 pms.Equipment AS E ON E.EquipmentID = dbo.PurchaseOrders.PMSEquipmentID AND E.IsDeleted = 0
WHERE     (dbo.PurchaseOrders.IsDeleted = 0)

GO



CREATE view [proc].[vwPurchaseOrderItems]

as

select *, UnitPrice * PurchaseOrderQuantity as TotalPrice

from (

         select distinct Materials.Name                                                   as Material,

                         Materials.code                                                   as MaterialCode,

                         MaterialCategories.Name                                          as MaterialCategoryName,

                         PurchaseOrderItems.PurchaseOrderQuantity,

                         RequisitionTypes.Name                                            as RequisitionType,

                         PurchaseOrders.PurchaseOrderDate,

                         PMSUnits.Name                                                    as Unit,

                         PurchaseOrders.Code                                              as PONo,

                         Vessels.Name                                                     as Vessel,

                         Ports.Name                                                       as Port

                 ,

                         (Select top (1) Value

                          from CurrencyParities

                          where CurrencyParities.CurrencyCode1 = Currencies.Code

                            and CurrencyParities.CurrencyCode2 = 'USD'

                            and CurrencyParities.[CurrencyDate] < PurchaseOrderDate

                          order by [CurrencyDate] desc) * PurchaseOrderItems.NetUnitPrice as UnitPrice

                 ,

                         Companies.Name                                                   as Company,

                         PurchaseOrders.PurchaseOrderID,

                         PurchaseOrderItems.PurchaseOrderItemID,

                         Materials.MaterialID,

                         Companies.CompanyID,

                         Ports.PortID,

						 PurchaseOrderItems.PurchaseOrderInfo,

						(select Sum(ConditionValue) as Condition from PurchaseOrderConditions as POC

Inner Join PurchaseOrderConditionVSPurchaseOrderItems as POCP on POCP.PurchaseOrderConditionID=POC.PurchaseOrderConditionID and POCP.IsDeleted=0

  where CalculationTypeID='AC4A52B8-035C-4F80-AC14-63713F600E9A' and POC.IsDeleted=0 and POCP.PurchaseOrderItemID=PurchaseOrderItems.PurchaseOrderItemID

) as Discount

         from PurchaseOrderItems

                  inner join PurchaseOrders on PurchaseOrders.PurchaseOrderID = PurchaseOrderItems.PurchaseOrderID and PurchaseOrders.IsDeleted = 0

                  inner join Materials on Materials.MaterialID = PurchaseOrderItems.MaterialID and Materials.IsDeleted = 0

                  inner join MaterialCategories on MaterialCategories.MaterialCategoryID = Materials.MaterialCategoryID and MaterialCategories.IsDeleted = 0

                  inner join RequisitionItems on RequisitionItems.RequisitionItemID = PurchaseOrderItems.RequisitionItemID and RequisitionItems.IsDeleted = 0

                  inner join Requisitions on Requisitions.RequisitionID = RequisitionItems.RequisitionID and Requisitions.IsDeleted = 0

                  inner join RequisitionTypes on RequisitionTypes.RequisitionTypeID = Requisitions.RequisitionTypeID and RequisitionTypes.IsDeleted = 0

                  inner join PMSUnits on PurchaseOrderItems.PurchaseOrderUnitID = PMSUnits.PMSUnitID and PMSUnits.IsDeleted = 0

                  inner join Vessels on PurchaseOrders.VesselID = Vessels.VesselID and Vessels.IsDeleted = 0

                  Left outer join Ports on Ports.PortID = PurchaseOrders.DeliveryPortID and Ports.IsDeleted = 0

                  inner join Companies on Companies.CompanyID = PurchaseOrders.VendorID and Companies.Isdeleted = 0

                  inner join Currencies on Currencies.CurrencyID = PurchaseOrders.CurrencyID and Currencies.Isdeleted = 0

         where PurchaseOrderItems.IsDeleted = 0) as ViewPurchaseOrderItems

GO



CREATE view [proc].[vwPurchaseOrderAdditionalCosts] as



SELECT Ports.Name               AS Port,

       Companies.Name           AS Company,

       ConditionCategories.Name AS Condition,

       PurchaseOrders.PurchaseOrderDate,

       Vessels.Name             as Vessel,

       PurchaseOrders.Code      as POCode,

       (Select top (1) Value from CurrencyParities where CurrencyParities.CurrencyCode1 = Currencies.Code and CurrencyParities.CurrencyCode2 = 'USD' and CurrencyParities.[CurrencyDate] < PurchaseOrderDate order by [CurrencyDate] desc) *

       ConditionValue           as ConditionValuePrice,

       PurchaseOrders.PurchaseOrderID

FROM ConditionCategories

         INNER JOIN

     PurchaseOrderConditions ON ConditionCategories.ConditionCategoryID = PurchaseOrderConditions.ConditionCategoryID

         INNER JOIN

     PurchaseOrders ON PurchaseOrderConditions.PurchaseOrderID = PurchaseOrders.PurchaseOrderID

         INNER JOIN

     Companies ON PurchaseOrders.VendorID = Companies.CompanyID

         INNER JOIN

     CalculationTypes ON CalculationTypes.CalculationTypeID = PurchaseOrderConditions.CalculationTypeID

         INNER JOIN

     Vessels ON PurchaseOrders.VesselID = Vessels.VesselID

         INNER JOIN

     Ports ON PurchaseOrders.DeliveryPortID = Ports.PortID

         inner join "dbo"."Currencies" "Currencies" on ("Currencies"."CurrencyID" = "PurchaseOrders"."CurrencyID")



WHERE (PurchaseOrders.IsDeleted = 0)

  AND (Companies.IsDeleted = 0)

  AND (PurchaseOrderConditions.IsDeleted = 0)

  AND (ConditionCategories.IsDeleted = 0)

  AND (Ports.IsDeleted = 0)



  and CalculationTypes.Name = 'Additional Cost'

GO

CREATE   view [proc].[vwReportPurchaseOrders] as
    SELECT po.PurchaseOrderID                                                   AS PurchaseOrderID,
           [Ports].Name                                                         AS [Port],
           po.I
sDeleted                                                         AS IsDeleted,
           po.Code                                                              AS PONo,
           PurchaseOrderDate                                                    AS PODa
te,
           qu.Code                                                              AS QNNo,
           qu.QuotationDate                                                     AS QNDate,
           companies.Name                                              
         AS VendorName,
           companies.Address                                                    AS VendorStreetAddress,
           companies.Phone1                                                     AS VendorPhone,
           companies.Fax1      
                                                 AS VendorFax,
           companies.Email                                                      AS Email,
           vessel.Name                                                          AS VesselName,
       
    vesselCo.Name                                                        AS ShipToName,
           vesselCo.Address                                                     AS ShipToStreetAddress,
           vessel.IMONo                                        
                 AS VesselIMONo,
           requisition.Code                                                     AS RequsitionCode,
           requisition.Title                                                    AS RequsitionTitle,
           (SELECT COUN
T(*)
            FROM PurchaseOrderItems innerPurchaseOrderItems
            WHERE innerPurchaseOrderItems.IsDeleted = 0
              AND innerPurchaseOrderItems.PurchaseOrderID = po.PurchaseOrderID) AS POItems,
           (dbo.fnGetCurrentExchangeRate(p
o.PurchaseOrderDate,
                                         (SELECT Code
                                          FROM Currencies
                                          WHERE CurrencyID = po.CurrencyID
                                            AND
 IsDeleted = 0),
                                         (SELECT Code
                                          FROM Currencies
                                          WHERE IsDefault = 1
                                            AND IsDeleted = 0)))
                AS ExchangeRate,
           CAST(po.TotalInPoCurrency AS decimal(18, 2))                         AS SubTotal,
           CAST(po.AddCostInPoCurrency AS decimal(18, 2))                       AS AdditionalCost,
           CAST(po.DiscountInP
oCurrency AS decimal(18, 2))                      AS Discount,
           CAST(po.GrandTotalInPoCurrency AS decimal(18, 2))                    AS GrandTotal,
           CAST(po.TotalInDefaultCurrency AS decimal(18, 2))                    AS SubTotalDefaul
tCurrency,
           CAST(po.AddCostInDefaultCurrency AS decimal(18, 2))                  AS AdditionalCostDefaultCurrency,
           CAST(po.DiscountInDefaultCurrency AS decimal(18, 2))                 AS DiscountDefaultCurrency,
           CAST(po.Gra
ndTotalInDefaultCurrency AS decimal(18,
               2))                                                              AS GrandTotalDefaultCurrency,
           (SELECT STUFF
                       ((SELECT ', ' + ConditionCategories.Name + ': ' +
       
                         CAST((CAST(ConditionValue * (dbo.fnGetCurrentExchangeRate(po.PurchaseOrderDate,
                                                                                          (SELECT Code
                                               
                                            FROM Currencies
                                                                                           WHERE CurrencyID = po.CurrencyID
                                                                       
                      AND IsDeleted = 0),
                                                                                          (SELECT Code
                                                                                           FROM Currencies
   
                                                                                        WHERE IsDefault = 1
                                                                                             AND IsDeleted = 0))) AS decimal(18, 2))) AS nvarchar(2
0)) +
                                ' ' + Currencies.Symbol
                         FROM PurchaseOrderConditions
                                  LEFT OUTER JOIN
                              ConditionCategories
                              ON Purcha
seOrderConditions.ConditionCategoryID = ConditionCategories.ConditionCategoryID
                                  LEFT OUTER JOIN
                              Currencies ON Currencies.CurrencyID =
                                            (SELECT Curre
ncyID
                                             FROM Currencies
                                             WHERE IsDefault = 1
                                               AND IsDeleted = 0)
                         WHERE PurchaseOrderConditions.Pu
rchaseOrderID = po.PurchaseOrderID
                           AND PurchaseOrderConditions.CalculationTypeID = '9008F853-CDD5-43FB-805A-F25F3B240597'
                           AND PurchaseOrderConditions.IsDeleted = 0
                         ORDER BY Con
ditionCategories.Name
                         FOR XML PATH('')), 1, 2, ''))                          AS AdditionalCostDisplayDefaultCurrency,
           (SELECT STUFF
                       ((SELECT ', ' + ConditionCategories.Name + ': ' + CAST(Condition
Value AS nvarchar(20)) + ' ' +
                                Currencies.Symbol
                         FROM PurchaseOrderConditions
                                  LEFT OUTER JOIN
                              ConditionCategories
                    
          ON PurchaseOrderConditions.ConditionCategoryID = ConditionCategories.ConditionCategoryID
                                  LEFT OUTER JOIN
                              Currencies ON Currencies.CurrencyID = po.CurrencyID
                        
 WHERE PurchaseOrderConditions.PurchaseOrderID = po.PurchaseOrderID
                           AND PurchaseOrderConditions.CalculationTypeID = '9008F853-CDD5-43FB-805A-F25F3B240597'
                           AND PurchaseOrderConditions.IsDeleted = 0
    
                     ORDER BY ConditionCategories.Name
                         FOR XML PATH('')), 1, 2, ''))                          AS AdditionalCostDisplayDefault,
           (SELECT STUFF
                       ((SELECT ', ' + ConditionCategories.Nam
e + ': ' +
                                CAST((CAST(ConditionValue * (dbo.fnGetCurrentExchangeRate(po.PurchaseOrderDate,
                                                                                          (SELECT Code
                             
                                                              FROM Currencies
                                                                                           WHERE CurrencyID = po.CurrencyID
                                                     
                                        AND IsDeleted = 0),
                                                                                          (SELECT Code
                                                                                           F
ROM Currencies
                                                                                           WHERE IsDefault = 1
                                                                                             AND IsDeleted = 0))) AS decimal(18, 
2))) AS nvarchar(20)) +
                                ' ' + Currencies.Symbol
           FROM PurchaseOrderConditions
                                  LEFT OUTER JOIN
                              ConditionCategories
                              ON Pu
rchaseOrderConditions.ConditionCategoryID = ConditionCategories.ConditionCategoryID
                                  LEFT OUTER JOIN
                              Currencies ON Currencies.CurrencyID =
                                            (SELECT C
urrencyID
                                             FROM Currencies
                                             WHERE IsDefault = 1
                                               AND IsDeleted = 0)
                         WHERE PurchaseOrderCondition
s.PurchaseOrderID = po.PurchaseOrderID
                           AND PurchaseOrderConditions.CalculationTypeID = '49F6E3EF-2C15-41DD-AE87-C6E78FAFCD8B'
                           AND PurchaseOrderConditions.IsDeleted = 0
                         ORDER BY
 ConditionCategories.Name
                         FOR XML PATH('')), 1, 2, ''))                          AS DiscountDisplayDefaultCurrency,
           (SELECT STUFF
                       ((SELECT ', ' + ConditionCategories.Name + ': ' + CAST(ConditionVa
lue AS nvarchar(20)) + ' ' +
                                Currencies.Symbol
                         FROM PurchaseOrderConditions
                                  LEFT OUTER JOIN
                              ConditionCategories
                      
        ON PurchaseOrderConditions.ConditionCategoryID = ConditionCategories.ConditionCategoryID
                                  LEFT OUTER JOIN
                              Currencies ON Currencies.CurrencyID = po.CurrencyID
                         W
HERE PurchaseOrderConditions.PurchaseOrderID = po.PurchaseOrderID
                           AND PurchaseOrderConditions.CalculationTypeID = '49F6E3EF-2C15-41DD-AE87-C6E78FAFCD8B'
                           AND PurchaseOrderConditions.IsDeleted = 0
      
                   ORDER BY ConditionCategories.Name
                         FOR XML PATH('')), 1, 2, ''))                          AS DiscountDisplay,
           (SELECT Symbol
            FROM Currencies
            WHERE IsDeleted = 0
              AN
D IsDefault = 1)                                                AS SymbolDefaultCurrency,
           (SELECT Symbol
            FROM Currencies
            WHERE IsDeleted = 0
              AND CurrencyID = po.CurrencyID)                                  
 AS Symbol,
           (SELECT TOP 1 Name + ' ' + Surname
            FROM CompanyContacts AS companyContact
            WHERE CompanyID = companies.CompanyID
              AND IsDeleted = 0
            ORDER BY Name ASC)                                  
                AS ContactPerson,
           (SELECT TOP 1 Phone
            FROM CompanyContacts
            WHERE CompanyID = companies.CompanyID
              AND IsDeleted = 0
            ORDER BY Name ASC)                                             
     AS ContactPersonPhone,
           (SELECT TOP 1 Email
            FROM CompanyContacts
            WHERE CompanyID = companies.CompanyID
              AND IsDeleted = 0
            ORDER BY Name ASC)                                                  A
S ContactPersonEmail,
           po.DueDate                                                           AS DueDate,
           Users.Name + ' ' + Users.Surname                                     AS CreatorUser
    FROM PurchaseOrders AS po
             LEF
T OUTER JOIN
         Quotations AS qu ON po.QuotationID = qu.QuotationID
             INNER JOIN
         Companies AS companies ON companies.CompanyID = po.VendorID
             LEFT OUTER JOIN
         Requisitions AS requisition ON requisition.Requisi
tionID = po.RequisitionID
             LEFT OUTER JOIN
         Vessels AS vessel ON po.VesselID = vessel.VesselID
             LEFT OUTER JOIN
         Companies AS vesselCo ON vessel.OwnerCompanyID = vesselCo.CompanyID
             LEFT OUTER JOIN
    [
Ports] ON [Ports].PortID = po.DeliveryPortID
             INNER JOIN
         Users ON po.CreatorUserId = Users.UserID

GO

CREATE VIEW [proc].[vwReportPurchaseOrderItems] AS

    SELECT Materials.Name                                                                                  AS MaterialName,

           Materials.code                                                                                  AS MaterialCode,

           MaterialCategories.Name                                                                         AS MaterialCategoryName,

           poi.PurchaseOrderQuantity                                                                       AS POQuantity,

           poi.PurchaseOrderID                                                                             AS PurchaseOrderID,

           poi.IsDeleted                                                                                   AS IsDeleted,

           vwrpo.ExchangeRate,

           vwrpo.Symbol,

           vwrpo.SymbolDefaultCurrency,

           poi.UnitPrice                                                                                   AS UnitPrice,

           CAST(IIF((dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID)) IS NULL, poi.UnitPrice,

                    (dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID))) AS decimal(18, 2)) AS Discount,

           CAST(IIF((dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID)) IS NULL, poi.UnitPrice,

                    (dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID)) *

                    vwrpo.ExchangeRate) AS decimal(18, 2))                                                 AS DiscountDefaultCurrency,

           CAST(IIF((dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID)) IS NULL, poi.UnitPrice,

                    (dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID))) *

                poi.PurchaseOrderQuantity AS decimal(18, 2))                                               AS Total,

           CAST(IIF((dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID)) IS NULL, poi.UnitPrice,

                    (dbo.fnGetPurchaseOrderItemDiscountPrice(poi.PurchaseOrderItemID)))

               * vwrpo.ExchangeRate *

                poi.PurchaseOrderQuantity AS decimal(18, 2))                                               AS TotalDefaultCurrency,

           (SELECT STUFF

                       ((SELECT ', ' + '- %' + CAST(ConditionValue AS nvarchar(20))

                         FROM PurchaseOrderConditions

                                  LEFT OUTER JOIN

                              ConditionCategories

                              ON PurchaseOrderConditions.ConditionCategoryID = ConditionCategories.ConditionCategoryID

                                  INNER JOIN PurchaseOrderConditionVSPurchaseOrderItems

                                             ON PurchaseOrderConditionVSPurchaseOrderItems.PurchaseOrderConditionID =

                                                PurchaseOrderConditions.PurchaseOrderConditionID

                                                 and PurchaseOrderConditionVSPurchaseOrderItems.PurchaseOrderItemID =

                                                     poi.PurchaseOrderItemID

                         WHERE PurchaseOrderConditions.PurchaseOrderID = poi.PurchaseOrderID

                           AND PurchaseOrderConditions.CalculationTypeID = 'AC4A52B8-035C-4F80-AC14-63713F600E9A'

                           AND PurchaseOrderConditions.IsDeleted = 0

                           and PurchaseOrderConditionVSPurchaseOrderItems.IsDeleted = 0

                         ORDER BY ConditionCategories.Name

                         FOR XML PATH('')), 1, 2,

                        ''))                                                                               AS DiscountPercents,

           vwrpo.PONo,

           vwrpo.VendorName,

           vwrpo.Port,

           vwrpo.VesselName,

           pmsUnit.Name         AS Unit,

           poi.CreationTime                                                                                AS CreationTime

    FROM PurchaseOrderItems poi

             INNER JOIN

         [proc].[vwReportPurchaseOrders] vwrpo ON poi.PurchaseOrderID = vwrpo.PurchaseOrderID

             INNER JOIN

         Materials ON Materials.MaterialID = poi.MaterialID AND Materials.IsDeleted = 0

             INNER JOIN

         MaterialCategories

         ON MaterialCategories.MaterialCategoryID = Materials.MaterialCategoryID AND MaterialCategories.IsDeleted = 0

             INNER JOIN

         PMSUnits AS pmsUnit ON poi.PurchaseOrderUnitID = pmsUnit.PMSUnitID

GO





 





CREATE view [report].[CrewEvaluation] as

SELECT CrewForms.Name AS EvaluationForm,CrewFormGroups.GroupNo,CrewFormGroups.Name AS EvaluationGroup, CAST(CrewFormAnswers.AnswerEvaluationPoint AS decimal(18, 2)) AS AnswerEvaluationPoint, Users.Name + ' ' + Users.Surname AS EvaluatedBy, 

                  CrewIdentities.Name + ' ' + CrewIdentities.Surname AS Evaluee, CrewFormAnswerGroups.EntryDate,CrewWorkPositions.Name as EvalueeRank, CrewFormAnswerGroups.CrewIdentityID, CrewFormAnswerGroups.CrewWorkPositionID

				  ,CrewFormAnswerGroups.CrewFormID,CASE CrewIdentities.Situation WHEN 1 THEN 'Left' ELSE 'Active' end as Situation

FROM     dbo.CrewFormAnswers AS CrewFormAnswers INNER JOIN

                  dbo.CrewFormAnswerGroups AS CrewFormAnswerGroups ON CrewFormAnswerGroups.CrewFormAnswerGroupID = CrewFormAnswers.CrewFormAnswerGroupID INNER JOIN

                  dbo.CrewForms AS CrewForms ON CrewForms.CrewFormID = CrewFormAnswerGroups.CrewFormID INNER JOIN

                  dbo.Users AS Users ON Users.UserID = CrewFormAnswerGroups.EnteredByUserID INNER JOIN

                  dbo.CrewWorkPositions AS CrewWorkPositions ON CrewWorkPositions.CrewWorkPositionID = CrewFormAnswerGroups.CrewWorkPositionID INNER JOIN

                  dbo.CrewIdentities AS CrewIdentities ON CrewIdentities.CrewIdentityID = CrewFormAnswerGroups.CrewIdentityID INNER JOIN

                  dbo.CrewFormGroups AS CrewFormGroups ON CrewFormGroups.CrewFormID = CrewForms.CrewFormID INNER JOIN

                  dbo.CrewFormQuestions AS CrewFormQuestions ON CrewFormQuestions.CrewFormGroupID = CrewFormGroups.CrewFormGroupID AND CrewFormQuestions.CrewFormQuestionID = CrewFormAnswers.CrewFormQuestionID

WHERE  (CrewFormAnswers.IsDeleted = 0) AND (CrewFormAnswerGroups.IsDeleted = 0) AND (CrewForms.IsDeleted = 0) AND (Users.IsDeleted = 0) AND (CrewWorkPositions.IsDeleted = 0) AND (CrewIdentities.IsDeleted = 0) AND 

                  (CrewFormGroups.IsDeleted = 0) AND (CrewFormQuestions.IsDeleted = 0) AND (CrewFormQuestions.IsPointScoring = 1)

GO

create view  report.ProcPurchaseOrders as

SELECT        MaterialCategories.Name AS [Material Category], Materials.Code AS MaterialCode, Materials.Name AS MaterialName, Vessels.Name AS Vessel, PurchaseOrders.Code as PurchaseOrderCode, PurchaseOrders.Title as PurchaseOrderTitle, PurchaseOrders.DueD
ate, 

                         PurchaseOrderItems.UnitPrice, PurchaseOrderItems.NetPrice,  PurchaseOrderItems.PurchaseOrderQuantity, Companies.Name AS Company

FROM            PurchaseOrderItems AS PurchaseOrderItems INNER JOIN

                         PurchaseOrders AS PurchaseOrders ON PurchaseOrders.PurchaseOrderID = PurchaseOrderItems.PurchaseOrderID INNER JOIN

                         Companies AS Companies ON Companies.CompanyID = PurchaseOrders.VendorID INNER JOIN

                         Materials AS Materials ON Materials.MaterialID = PurchaseOrderItems.MaterialID INNER JOIN

                         MaterialCategories AS MaterialCategories ON MaterialCategories.MaterialCategoryID = Materials.MaterialCategoryID RIGHT OUTER JOIN

                         Vessels AS Vessels ON  Vessels.VesselID = PurchaseOrders.VesselID

WHERE        (Companies.IsDeleted = 0) AND (PurchaseOrders.IsDeleted = 0) AND (PurchaseOrderItems.IsDeleted = 0) AND (Materials.IsDeleted = 0) AND (MaterialCategories.IsDeleted = 0) AND (Vessels.IsDeleted = 0)

GO

CREATE VIEW [report].[vwSupplier]

AS

SELECT Companies.Name AS CompanyName,

       Segments.Name AS SegmentName,

       CompanyVsSegments.StartDate,

       CompanyVsSegments.ExpireDate,

       CompanyCategories.Name AS CategoryName

FROM dbo.Companies

    LEFT JOIN dbo.CompanyVsCompanyCategories

        ON dbo.CompanyVsCompanyCategories.CompanyID = dbo.Companies.CompanyID

           AND dbo.CompanyVsCompanyCategories.IsDeleted = 0

    LEFT JOIN dbo.CompanyCategories

        ON dbo.CompanyVsCompanyCategories.CompanyCategoryID = dbo.CompanyCategories.CompanyCategoryID

           AND dbo.CompanyCategories.IsDeleted = 0

    LEFT JOIN dbo.CompanyVsSegments

        ON dbo.CompanyVsSegments.CompanyID = dbo.Companies.CompanyID

           AND dbo.CompanyVsSegments.IsDeleted = 0

    LEFT JOIN dbo.Segments

        ON dbo.CompanyVsSegments.SegmentID = dbo.Segments.SegmentID

           AND dbo.Segments.IsDeleted = 0

WHERE dbo.Companies.IsDeleted = 0;

GO



CREATE view [report].[SeafarersEmploymentContract] as 



select CrewIdentities.*,

	   CrewIdentities.Name + ' ' + CrewIdentities.Surname as FullName,

       CrewWorkPositions.Name as CrewWorkPositionName,

       CSB.BookNo,

	   CSB.ExpireDate as SeamanBookExpireDate,

	   CSB.IssueDate as SeamanBookIssueDate,

	   CSB.RegistryPort as SeamanBookRegistryPort,

       Countries.Name         as Nationality,

       CP.PassportNo

from CrewIdentities

         inner join CrewWorkPositions on CrewIdentities.CrewWorkPositionID = CrewWorkPositions.CrewWorkPositionID

         outer apply

     (

         select TOP 1 * FROM CrewSeamanBooks WHERE CrewIdentities.CrewIdentityID = CrewSeamanBooks.CrewIdentityID

     ) as CSB

    outer apply

     (

         select TOP 1 * FROM CrewPassports WHERE CrewIdentities.CrewIdentityID = CrewPassports.CrewIdentityID

     ) as CP

         LEFT OUTER join Countries on CrewIdentities.CountryID = Countries.CountryID

GO



CREATE VIEW [report].[vw_Vessels] as 



select "Vessels"."VesselID",

       "Vessels"."VesselFlagID",

       "Vessels"."VesselCoatingID",

       "Vessels"."OwnerCompanyID",

       "Vessels"."VesselClassID",

       "Vessels"."ClassIDNo",

       "Vessels"."Name",

       "Vessels"."IMONo",

       "Vessels"."CallSign",

       "Vessels"."DWT",

       "Vessels"."SummerDraft",

       "Vessels"."TPC",

       "Vessels"."KTM",

       "Vessels"."GRT",

       "Vessels"."NET",

       "Vessels"."LOA",

       "Vessels"."BEAM",

       "Vessels"."BuildYear",

       "Vessels"."GSMSatellite",

       "Vessels"."GSM",

       "Vessels"."Email",

       "Vessels"."Volume",

       "Vessels"."Speed",

       "Vessels"."MMSI",

       "Vessels"."Power",

       "Vessels"."MinimumPerson",

       "Vessels"."MaximumPerson",

       "Vessels"."CabinCapacity",

       "Vessels"."DateOfVoyage",

       "Vessels"."CommercialOperator",

       "Vessels"."MessageBoardCrew",

       "Vessels"."DeliveryDate",

       "Vessels"."DateLeft",

       "Vessels"."IsDeleted",

       "Vessels"."IsActive",

       "Vessels"."BudgetPeriodStart",

       "Vessels"."BudgetPeriodTotalDays",

       "Vessels"."VesselShortName",

       "Vessels"."RegistryPortID",

       "Vessels"."VesselGearID",

       "Vessels"."IsLeft",

       "Vessels"."InmarsatC1",

       "Vessels"."InmarsatC2",

       "Vessels"."VesselTypeEnum",

       "Vessels"."SpeedOnBallastCondition",

       "Vessels"."SpeedOnLadenCondition",

       "Vessels"."GrainCapacity",

       "Vessels"."BulkCapacity",

       "Vessels"."IsMRV",

       "Vessels"."Code",

       "Companies"."Name"      as "Companies_Name",

       "Companies"."VAT",

       "Companies"."Address",

       "Companies"."WebSite",

       "Companies"."Phone1",

       "Companies"."Phone2",

       "Companies"."Phone3",

       "Companies"."Phone4",

       "Companies"."Fax1",

       "Companies"."Fax2",

       "Companies"."Email"     as "Companies_Email",

       "Companies"."Attention",

       "Companies"."Note",

       "Companies"."IsDeleted" as "Companies_IsDeleted",

       "Companies"."IsSystemOwner",

       "Companies"."Code"      as "Companies_Code",

       "Ports"."Name"          as "Ports_Name",

       "VesselFlags"."Name"    as "VesselFlags_Name"

from ("dbo"."Vessels" "Vessels"

         left outer join "dbo"."Companies" "Companies" on ("Companies"."CompanyID" = "Vessels"."OwnerCompanyID")

         left outer join "dbo"."Ports" "Ports" on ("Ports"."PortID" = "Vessels"."RegistryPortID")

         left outer join "dbo"."VesselFlags" "VesselFlags" on ("VesselFlags"."VesselFlagID" = "Vessels"."VesselFlagID") )

GO



CREATE view [report].[ApplicationForAnEndorsementAttesting] as 



select CrewIdentities.*,

	   CrewIdentities.Name + ' ' + CrewIdentities.Surname as FullName,

       CrewWorkPositions.Name as CrewWorkPositionName,

       CSB.BookNo,

	   CSB.ExpireDate as SeamanBookExpireDate,

	   CSB.IssueDate as SeamanBookIssueDate,

	   CSB.RegistryPort as SeamanBookRegistryPort,

       Countries.Name         as Nationality,

       CP.PassportNo

from CrewIdentities

         inner join CrewWorkPositions on CrewIdentities.CrewWorkPositionID = CrewWorkPositions.CrewWorkPositionID

         outer apply

     (

         select TOP 1 * FROM CrewSeamanBooks WHERE CrewIdentities.CrewIdentityID = CrewSeamanBooks.CrewIdentityID

     ) as CSB

    outer apply

     (

         select TOP 1 * FROM CrewPassports WHERE CrewIdentities.CrewIdentityID = CrewPassports.CrewIdentityID

     ) as CP

         LEFT OUTER join Countries on CrewIdentities.CountryID = Countries.CountryID

GO

Msg 15009, Level 16, State 1, Server Zeki\SQLEXPRESS, Procedure sp_helptext, Line 54
The object '(72 rows affected)' does not exist in database 'YMMercuryDB' or is invalid for this operation.

GO

