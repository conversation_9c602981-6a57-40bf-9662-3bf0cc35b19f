Building...
/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/GeneratePasswordHash.cs(6,24): warning CS7022: The entry point of the program is global code; ignoring 'GeneratePasswordHash.Main()' entry point. [/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj]
/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/Views/Crew/Details.cshtml(68,72): error CS1061: 'CrewViewModel' does not contain a definition for 'DepartmentName' and no accessible extension method 'DepartmentName' accepting a first argument of type 'CrewViewModel' could be found (are you missing a using directive or an assembly reference?) [/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj]
/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/Services/AuthenticationService.cs(93,29): warning CS1998: This async method lacks 'await' operators and will run synchronously. Consider using the 'await' operator to await non-blocking API calls, or 'await Task.Run(...)' to do CPU-bound work on a background thread. [/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj]
/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/Controllers/CrewController.cs(104,13): error CS0117: 'CrewViewModel' does not contain a definition for 'DepartmentName' [/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj]
/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/Controllers/CrewController.cs(104,46): error CS1061: 'string' does not contain a definition for 'Name' and no accessible extension method 'Name' accepting a first argument of type 'string' could be found (are you missing a using directive or an assembly reference?) [/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj]
/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/Controllers/DocumentsController.cs(531,37): warning CS8602: Dereference of a possibly null reference. [/Users/<USER>/Source/SMS_v3/SMS_Maritime_Web/SMS_Maritime_Web.csproj]

The build failed. Fix the build errors and run again.
